#!/bin/bash

SOURCE_FILE="$PWD/amplify/graphql-spec/queries.js"
TARGET_FILE="$PWD/src/graphql-with-spec/queries.js"

# Function to remove specific functions from target file
remove_functions() {
  echo "Removing functions from target file..."

  for FUNCTION_NAME in "$@"; do
    echo "Removing function: $FUNCTION_NAME"

    # Create a temporary file
    TEMP_FILE=$(mktemp)

    # Use sed to find and remove the function definition
    if [ "$(uname)" == "Darwin" ]; then  # macOS
      sed -E "/export const $FUNCTION_NAME = \/\* GraphQL \*\/ \`/,/\`;/d" "$TARGET_FILE" > "$TEMP_FILE"
    else  # Linux
      sed -E "/export const $FUNCTION_NAME = \/\* GraphQL \*\/ \\\`/,/\\\`;/d" "$TARGET_FILE" > "$TEMP_FILE"
    fi

    # Replace the original file with the modified one
    mv "$TEMP_FILE" "$TARGET_FILE"
  done

  echo "Function removal completed."
}

# Function to copy specific functions from source to target file
copy_functions() {
  echo "Copying functions from source file to target file..."

  for FUNCTION_NAME in "$@"; do
    echo "Copying function: $FUNCTION_NAME"

    # Create temporary files
    TEMP_FUNCTION=$(mktemp)

    # Extract the function from the source file
    if [ "$(uname)" == "Darwin" ]; then  # macOS
      sed -n "/export const $FUNCTION_NAME = \/\* GraphQL \*\/ \`/,/\`;/p" "$SOURCE_FILE" > "$TEMP_FUNCTION"
    else  # Linux
      sed -n "/export const $FUNCTION_NAME = \/\* GraphQL \*\/ \\\`/,/\\\`;/p" "$SOURCE_FILE" > "$TEMP_FUNCTION"
    fi

    # Append the function to the target file
    cat "$TEMP_FUNCTION" >> "$TARGET_FILE"

    # Add a newline after the function
    echo "" >> "$TARGET_FILE"

    # Clean up temporary file
    rm "$TEMP_FUNCTION"
  done

  echo "Function copying completed."
}

# Check if files exist
if [ ! -f "$SOURCE_FILE" ]; then
  echo "Error: Source file '$SOURCE_FILE' not found."
  exit 1
fi

if [ ! -f "$TARGET_FILE" ]; then
  echo "Error: Target file '$TARGET_FILE' not found."
  exit 1
fi

# Check for at least one argument
if [ $# -eq 0 ]; then
  echo "Usage: $0 --remove <function1> <function2> ... --copy <function1> <function2> ..."
  exit 1
fi

# Parse arguments
REMOVE_FUNCTIONS=()
COPY_FUNCTIONS=()
CURRENT_MODE=""

for arg in "$@"; do
  if [ "$arg" == "--remove" ]; then
    CURRENT_MODE="remove"
  elif [ "$arg" == "--copy" ]; then
    CURRENT_MODE="copy"
  else
    if [ "$CURRENT_MODE" == "remove" ]; then
      REMOVE_FUNCTIONS+=("$arg")
    elif [ "$CURRENT_MODE" == "copy" ]; then
      COPY_FUNCTIONS+=("$arg")
    else
      echo "Error: Please specify --remove or --copy before listing function names."
      exit 1
    fi
  fi
done

# Remove specified functions if any
if [ ${#REMOVE_FUNCTIONS[@]} -gt 0 ]; then
  remove_functions "${REMOVE_FUNCTIONS[@]}"
fi

# Copy specified functions if any
if [ ${#COPY_FUNCTIONS[@]} -gt 0 ]; then
  copy_functions "${COPY_FUNCTIONS[@]}"
fi

echo "All operations completed successfully!"
