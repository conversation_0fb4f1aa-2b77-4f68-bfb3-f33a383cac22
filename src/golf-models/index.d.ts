import { ModelInit, MutableModel } from "@aws-amplify/datastore";
// @ts-ignore
import { LazyLoading, LazyLoadingDisabled, AsyncCollection, AsyncItem } from "@aws-amplify/datastore";

export enum CompetitiveLevel {
  YOUTH_LESS_THAN13 = "YouthLessThan13",
  HIGH_SCHOOL = "HighSchool",
  COLLEGE = "College",
  PROFESSIONAL = "Professional",
  AMATEUR = "Amateur"
}

export enum LmDistanceUnits {
  FEET = "Feet",
  YARDS = "Yards",
  METERS = "Meters"
}

export enum LmSpeedUnits {
  MPH = "Mph",
  KPH = "Kph",
  MPS = "Mps"
}

export enum LmTemperatureUnits {
  FAHRENHEIT = "Fahrenheit",
  CELCIUS = "Celcius"
}

export enum LmDisplayMode {
  GRAPHIC = "Graphic",
  TILES = "Tiles",
  MULTI_DATA = "MultiData",
  SINGLE_DATA = "SingleData"
}

export enum LmTheme {
  LIGHT = "Light",
  DARK = "Dark"
}

export enum LmTargetOption {
  NONE = "None",
  BASKET = "Basket",
  GREEN = "Green",
  CIRCLE = "Circle"
}

export enum LmCameraView {
  STATIONARY = "Stationary",
  BALL = "Ball",
  FLIGHT = "Flight"
}

export enum LmBallPath {
  SIMULATED = "Simulated",
  RAW = "Raw"
}

export enum LmBallType {
  PREMIUM = "Premium",
  TITLEIST_RCT = "TitleistRct"
}

export enum LmHandedness {
  LEFT = "Left",
  RIGHT = "Right"
}

export enum DataType {
  INT = "Int",
  FLOAT = "Float"
}

export enum LmLocation {
  NET = "Net",
  SCREEN = "Screen",
  SIMULATOR = "Simulator",
  OUTDOOR_RANGE = "OutdoorRange",
  INDOOR_RANGE = "IndoorRange",
  COURSE = "Course",
  FIELD = "Field"
}

export enum ValueType {
  CLUB_SPEED = "ClubSpeed",
  BALL_SPEED = "BallSpeed",
  SMASH_FACTOR = "SmashFactor",
  ATTACK_ANGLE = "AttackAngle",
  CLUB_PATH = "ClubPath",
  LAUNCH_ANGLE = "LaunchAngle",
  HORIZONTAL_LAUNCH_ANGLE = "HorizontalLaunchAngle",
  FACE_ANGLE = "FaceAngle",
  FACE_TO_PATH = "FaceToPath",
  SPIN_RATE = "SpinRate",
  SPIN_AXIS = "SpinAxis",
  CARRY_DISTANCE = "CarryDistance",
  TOTAL_DISTANCE = "TotalDistance",
  SIDE = "Side",
  SIDE_TOTAL = "SideTotal",
  APEX = "Apex",
  BALL_DIRECTION = "BallDirection",
  BALL_CURVE = "BallCurve",
  DESCENT_ANGLE = "DescentAngle"
}

export enum BaseBallValueType {
  PITCH_SPIN = "PitchSpin",
  PITCH_SPEED = "PitchSpeed",
  EXIT_VELOCITY = "ExitVelocity",
  LAUNCH_ANGLE = "LaunchAngle",
  HORIZONTAL_LAUNCH_ANGLE = "HorizontalLaunchAngle",
  DISTANCE = "Distance",
  APEX = "Apex",
  BAT_SPEED = "BatSpeed"
}

export enum BaseballValueType {
  PITCH_RELEASE_TIMESTAMP = "pitchReleaseTimestamp",
  PITCH_RELEASE_WORLD3D_POSITION_METERS = "pitchReleaseWorld3dPositionMeters",
  PITCH_RELEASE_RADAR3D_POSITION_METERS_DEG = "pitchReleaseRadar3dPositionMetersDeg",
  PITCH_RELEASE_VELOCITY_MPS = "pitchReleaseVelocityMps",
  PITCH_RELEASE_ARM_SLOT_DEG = "pitchReleaseArmSlotDeg",
  PITCH_RELEASE_HORIZONTAL_ANGLE_DEG = "pitchReleaseHorizontalAngleDeg",
  PITCH_RELEASE_VERTICAL_ANGLE_DEG = "pitchReleaseVerticalAngleDeg",
  PITCH_RELEASE_BACKWARD_EXTENSION_METERS = "pitchReleaseBackwardExtensionMeters",
  PITCH_BREAK_HORIZONTAL_METERS = "pitchBreakHorizontalMeters",
  PITCH_BREAK_VERTICAL_METERS = "pitchBreakVerticalMeters",
  PITCH_BREAK_INDUCED_VERTICAL_METERS = "pitchBreakInducedVerticalMeters",
  PITCH_SPIN_TOTAL_RPM = "pitchSpinTotalRpm",
  PITCH_SPIN_ACTIVE_RPM = "pitchSpinActiveRpm",
  PITCH_SPIN_BACK_RPM = "pitchSpinBackRpm",
  PITCH_SPIN_SIDE_RPM = "pitchSpinSideRpm",
  PITCH_SPIN_TOP_RPM = "pitchSpinTopRpm",
  PITCH_SPIN_DIRECTION_CLOCK_BEARING = "pitchSpinDirectionClockBearing",
  PITCH_SPIN_DIRECTION_EFFICIENCY_PCT = "pitchSpinDirectionEfficiencyPct",
  PITCH_APPROACH_VELOCITY_MPS = "pitchApproachVelocityMps",
  PITCH_APPROACH_PLATE_TIMESTAMP = "pitchApproachPlateTimestamp",
  PITCH_APPROACH_PLATE_WORLD3D_POSITION_METERS = "pitchApproachPlateWorld3dPositionMeters",
  PITCH_CROSS_PLATE_TIMESTAMP = "pitchCrossPlateTimestamp",
  PITCH_CROSS_PLATE_WORLD3D_POSITION_METERS = "pitchCrossPlateWorld3dPositionMeters",
  HIT_DIRECTION_DEG = "hitDirectionDeg",
  HIT_DISTANCE_METERS = "hitDistanceMeters",
  HIT_EXIT_VELOCITY_MPS = "hitExitVelocityMps",
  HIT_LAUNCH_ANGLE_DEG = "hitLaunchAngleDeg",
  HIT_SPIN_TOTAL_RPM = "hitSpinTotalRpm",
  HIT_SPIN_ACTIVE_RPM = "hitSpinActiveRpm",
  HIT_SPIN_BACK_RPM = "hitSpinBackRpm",
  HIT_SPIN_SIDE_RPM = "hitSpinSideRpm",
  HIT_SPIN_TOP_RPM = "hitSpinTopRpm",
  HIT_SPIN_DIRECTION_CLOCK_BEARING = "hitSpinDirectionClockBearing",
  HIT_SPIN_DIRECTION_EFFICIENCY_PCT = "hitSpinDirectionEfficiencyPct",
  HIT_BALL_CONTACT_PRESENCE = "hitBallContactPresence",
  HIT_BALL_CONTACT_TIMESTAMP = "hitBallContactTimestamp",
  HIT_BALL_CONTACT_WORLD3D_POSITION_METERS = "hitBallContactWorld3dPositionMeters",
  HIT_BALL_CONTACT_RADAR3D_POSITION_METERS_DEG = "hitBallContactRadar3dPositionMetersDeg",
  BAT_SPEED_MPS = "batSpeedMps",
  BAT_ROTATIONAL_ACCELERATION_GS = "batRotationalAccelerationGs",
  BAT_ATTACK_ANGLE_DEG = "batAttackAngleDeg",
  BAT_EARLY_CONNECTION_DEG = "batEarlyConnectionDeg",
  BAT_CONNECTION_AT_IMPACT_DEG = "batConnectionAtImpactDeg",
  BAT_VERTICAL_ANGLE_DEG = "batVerticalAngleDeg",
  BAT_SWING_START_PRESENCE = "batSwingStartPresence",
  BAT_SWING_START_TIMESTAMP = "batSwingStartTimestamp",
  BAT_PEAK_HAND_SPEED_MPS = "batPeakHandSpeedMps",
  TEED_BALL_LOCATION_WORLD3D_POSITION_METERS = "teedBallLocationWorld3dPositionMeters"
}

export enum LmGolfTrajectoryType {
  FLIGHT = "Flight",
  NORMALIZED = "Normalized"
}

export enum LmBaseballTrajectoryType {
  UNKNOWN = "Unknown",
  PITCH = "Pitch",
  HIT = "Hit"
}

export enum LmUserRole {
  USERS = "Users",
  COACHES = "Coaches",
  ADMIN = "Admin"
}

export enum LmTeamPlayerType {
  BATTING = "Batting",
  PITCHING = "Pitching",
  BOTH = "Both"
}

export enum LmDataSessionPlayMode {
  LIVE_AT_BAT = "LiveAtBat",
  BATTING_PRACT = "BattingPract",
  TEE_MODE = "TeeMode"
}

export enum LmClubType {
  UNKNOWN = "Unknown",
  DRIVER = "Driver",
  WOOD2 = "Wood2",
  WOOD3 = "Wood3",
  WOOD4 = "Wood4",
  WOOD5 = "Wood5",
  WOOD6 = "Wood6",
  WOOD7 = "Wood7",
  WOOD8 = "Wood8",
  WOOD9 = "Wood9",
  IRON1 = "Iron1",
  IRON2 = "Iron2",
  IRON3 = "Iron3",
  IRON4 = "Iron4",
  IRON5 = "Iron5",
  IRON6 = "Iron6",
  IRON7 = "Iron7",
  IRON8 = "Iron8",
  IRON9 = "Iron9",
  PITCHING_WEDGE = "PitchingWedge",
  SAND_WEDGE = "SandWedge",
  LOB_WEDGE = "LobWedge",
  APPROACH_WEDGE = "ApproachWedge",
  GAP_WEDGE = "GapWedge",
  WEDGE46 = "Wedge46",
  WEDGE48 = "Wedge48",
  WEDGE50 = "Wedge50",
  WEDGE52 = "Wedge52",
  WEDGE54 = "Wedge54",
  WEDGE56 = "Wedge56",
  WEDGE58 = "Wedge58",
  WEDGE60 = "Wedge60",
  WEDGE62 = "Wedge62",
  WEDGE64 = "Wedge64",
  PUTTER = "Putter",
  HYBRID = "Hybrid",
  HYBRID1 = "Hybrid1",
  HYBRID2 = "Hybrid2",
  HYBRID3 = "Hybrid3",
  HYBRID4 = "Hybrid4",
  HYBRID5 = "Hybrid5",
  HYBRID6 = "Hybrid6",
  HYBRID7 = "Hybrid7",
  HYBRID8 = "Hybrid8",
  HYBRID9 = "Hybrid9",
  OTHER = "Other"
}

export enum LmClubCategory {
  ALL = "All",
  DRIVERS = "Drivers",
  WOODS = "Woods",
  HYBRIDS = "Hybrids",
  IRONS = "Irons",
  WEDGES = "Wedges"
}

export enum LmShotType {
  OFF_TEE = "OffTee",
  APPROACH = "Approach",
  AROUND_GREEN = "AroundGreen",
  PUTT = "Putt"
}

export enum LmShotShapeType {
  STRAIGHT = "Straight",
  FADE = "Fade",
  DRAW = "Draw"
}

export enum LmShotTrajectoryType {
  HIGH = "High",
  NORMAL = "Normal",
  LOW = "Low"
}

export enum LmDrillTargetType {
  DISTANCE = "Distance",
  SHOT_SHAPING = "ShotShaping",
  UP_DOWN = "UpDown"
}

export enum DateResolution {
  DAY = "Day",
  WEEK = "Week",
  MONTH = "Month"
}

type EagerLmClockBearing = {
  readonly hours?: number | null;
  readonly minutes?: number | null;
}

type LazyLmClockBearing = {
  readonly hours?: number | null;
  readonly minutes?: number | null;
}

export declare type LmClockBearing = LazyLoading extends LazyLoadingDisabled ? EagerLmClockBearing : LazyLmClockBearing

export declare const LmClockBearing: (new (init: ModelInit<LmClockBearing>) => LmClockBearing)

type EagerLmTimestamp = {
  readonly seconds?: number | null;
  readonly nanos?: number | null;
}

type LazyLmTimestamp = {
  readonly seconds?: number | null;
  readonly nanos?: number | null;
}

export declare type LmTimestamp = LazyLoading extends LazyLoadingDisabled ? EagerLmTimestamp : LazyLmTimestamp

export declare const LmTimestamp: (new (init: ModelInit<LmTimestamp>) => LmTimestamp)

type EagerLm2dPosition = {
  readonly x_pos?: number | null;
  readonly y_pos?: number | null;
}

type LazyLm2dPosition = {
  readonly x_pos?: number | null;
  readonly y_pos?: number | null;
}

export declare type Lm2dPosition = LazyLoading extends LazyLoadingDisabled ? EagerLm2dPosition : LazyLm2dPosition

export declare const Lm2dPosition: (new (init: ModelInit<Lm2dPosition>) => Lm2dPosition)

type EagerLm3dPosition = {
  readonly x_pos?: number | null;
  readonly y_pos?: number | null;
  readonly z_pos?: number | null;
}

type LazyLm3dPosition = {
  readonly x_pos?: number | null;
  readonly y_pos?: number | null;
  readonly z_pos?: number | null;
}

export declare type Lm3dPosition = LazyLoading extends LazyLoadingDisabled ? EagerLm3dPosition : LazyLm3dPosition

export declare const Lm3dPosition: (new (init: ModelInit<Lm3dPosition>) => Lm3dPosition)

type EagerLmSphericalPoint3d = {
  readonly r?: number | null;
  readonly theta?: number | null;
  readonly phi?: number | null;
}

type LazyLmSphericalPoint3d = {
  readonly r?: number | null;
  readonly theta?: number | null;
  readonly phi?: number | null;
}

export declare type LmSphericalPoint3d = LazyLoading extends LazyLoadingDisabled ? EagerLmSphericalPoint3d : LazyLmSphericalPoint3d

export declare const LmSphericalPoint3d: (new (init: ModelInit<LmSphericalPoint3d>) => LmSphericalPoint3d)

type EagerLmOrientation3d = {
  readonly pitch?: number | null;
  readonly yaw?: number | null;
  readonly roll?: number | null;
}

type LazyLmOrientation3d = {
  readonly pitch?: number | null;
  readonly yaw?: number | null;
  readonly roll?: number | null;
}

export declare type LmOrientation3d = LazyLoading extends LazyLoadingDisabled ? EagerLmOrientation3d : LazyLmOrientation3d

export declare const LmOrientation3d: (new (init: ModelInit<LmOrientation3d>) => LmOrientation3d)

type EagerLmPolynomial = {
  readonly x_coefficients?: (number | null)[] | null;
  readonly y_coefficients?: (number | null)[] | null;
  readonly z_coefficients?: (number | null)[] | null;
}

type LazyLmPolynomial = {
  readonly x_coefficients?: (number | null)[] | null;
  readonly y_coefficients?: (number | null)[] | null;
  readonly z_coefficients?: (number | null)[] | null;
}

export declare type LmPolynomial = LazyLoading extends LazyLoadingDisabled ? EagerLmPolynomial : LazyLmPolynomial

export declare const LmPolynomial: (new (init: ModelInit<LmPolynomial>) => LmPolynomial)

type EagerLmTrajectory = {
  readonly baseballType?: LmBaseballTrajectoryType | keyof typeof LmBaseballTrajectoryType | null;
  readonly golfType?: LmGolfTrajectoryType | keyof typeof LmGolfTrajectoryType | null;
  readonly polynomial?: LmPolynomial | null;
}

type LazyLmTrajectory = {
  readonly baseballType?: LmBaseballTrajectoryType | keyof typeof LmBaseballTrajectoryType | null;
  readonly golfType?: LmGolfTrajectoryType | keyof typeof LmGolfTrajectoryType | null;
  readonly polynomial?: LmPolynomial | null;
}

export declare type LmTrajectory = LazyLoading extends LazyLoadingDisabled ? EagerLmTrajectory : LazyLmTrajectory

export declare const LmTrajectory: (new (init: ModelInit<LmTrajectory>) => LmTrajectory)

type EagerLmShotNormalizedValues = {
  readonly carryDistance?: number | null;
  readonly totalDistance?: number | null;
  readonly side?: number | null;
  readonly sideTotal?: number | null;
  readonly apex?: number | null;
  readonly distanceToPin?: number | null;
}

type LazyLmShotNormalizedValues = {
  readonly carryDistance?: number | null;
  readonly totalDistance?: number | null;
  readonly side?: number | null;
  readonly sideTotal?: number | null;
  readonly apex?: number | null;
  readonly distanceToPin?: number | null;
}

export declare type LmShotNormalizedValues = LazyLoading extends LazyLoadingDisabled ? EagerLmShotNormalizedValues : LazyLmShotNormalizedValues

export declare const LmShotNormalizedValues: (new (init: ModelInit<LmShotNormalizedValues>) => LmShotNormalizedValues)

type EagerLmDrillTarget = {
  readonly id?: string | null;
  readonly type?: LmDrillTargetType | keyof typeof LmDrillTargetType | null;
  readonly targetQuality?: number | null;
  readonly shotCount?: number | null;
  readonly shotType?: LmShotType | keyof typeof LmShotType | null;
  readonly clubCategory?: LmClubCategory | keyof typeof LmClubCategory | null;
  readonly distance?: LmDistanceTarget | null;
  readonly shape?: LmShotShapeTarget | null;
  readonly upDown?: LmUpDownTarget | null;
}

type LazyLmDrillTarget = {
  readonly id?: string | null;
  readonly type?: LmDrillTargetType | keyof typeof LmDrillTargetType | null;
  readonly targetQuality?: number | null;
  readonly shotCount?: number | null;
  readonly shotType?: LmShotType | keyof typeof LmShotType | null;
  readonly clubCategory?: LmClubCategory | keyof typeof LmClubCategory | null;
  readonly distance?: LmDistanceTarget | null;
  readonly shape?: LmShotShapeTarget | null;
  readonly upDown?: LmUpDownTarget | null;
}

export declare type LmDrillTarget = LazyLoading extends LazyLoadingDisabled ? EagerLmDrillTarget : LazyLmDrillTarget

export declare const LmDrillTarget: (new (init: ModelInit<LmDrillTarget>) => LmDrillTarget)

type EagerLmDistanceRange = {
  readonly min?: number | null;
  readonly max?: number | null;
}

type LazyLmDistanceRange = {
  readonly min?: number | null;
  readonly max?: number | null;
}

export declare type LmDistanceRange = LazyLoading extends LazyLoadingDisabled ? EagerLmDistanceRange : LazyLmDistanceRange

export declare const LmDistanceRange: (new (init: ModelInit<LmDistanceRange>) => LmDistanceRange)

type EagerLmDistanceTarget = {
  readonly maxDistance?: boolean | null;
  readonly distance?: number | null;
  readonly range?: LmDistanceRange | null;
  readonly fairwayWidth?: number | null;
  readonly targetWidth?: number | null;
}

type LazyLmDistanceTarget = {
  readonly maxDistance?: boolean | null;
  readonly distance?: number | null;
  readonly range?: LmDistanceRange | null;
  readonly fairwayWidth?: number | null;
  readonly targetWidth?: number | null;
}

export declare type LmDistanceTarget = LazyLoading extends LazyLoadingDisabled ? EagerLmDistanceTarget : LazyLmDistanceTarget

export declare const LmDistanceTarget: (new (init: ModelInit<LmDistanceTarget>) => LmDistanceTarget)

type EagerLmShotShapeTarget = {
  readonly shape?: LmShotShapeType | keyof typeof LmShotShapeType | null;
  readonly trajectory?: LmShotTrajectoryType | keyof typeof LmShotTrajectoryType | null;
}

type LazyLmShotShapeTarget = {
  readonly shape?: LmShotShapeType | keyof typeof LmShotShapeType | null;
  readonly trajectory?: LmShotTrajectoryType | keyof typeof LmShotTrajectoryType | null;
}

export declare type LmShotShapeTarget = LazyLoading extends LazyLoadingDisabled ? EagerLmShotShapeTarget : LazyLmShotShapeTarget

export declare const LmShotShapeTarget: (new (init: ModelInit<LmShotShapeTarget>) => LmShotShapeTarget)

type EagerLmUpDownTarget = {
  readonly numBalls?: number | null;
  readonly swingCount?: number | null;
}

type LazyLmUpDownTarget = {
  readonly numBalls?: number | null;
  readonly swingCount?: number | null;
}

export declare type LmUpDownTarget = LazyLoading extends LazyLoadingDisabled ? EagerLmUpDownTarget : LazyLmUpDownTarget

export declare const LmUpDownTarget: (new (init: ModelInit<LmUpDownTarget>) => LmUpDownTarget)

type EagerShotValues = {
  readonly clubId: string;
  readonly clubSpeed?: (number | null)[] | null;
  readonly ballSpeed?: (number | null)[] | null;
  readonly smashFactor?: (number | null)[] | null;
  readonly attackAngle?: (number | null)[] | null;
  readonly clubPath?: (number | null)[] | null;
  readonly launchAngle?: (number | null)[] | null;
  readonly horizontalLaunchAngle?: (number | null)[] | null;
  readonly faceAngle?: (number | null)[] | null;
  readonly spinRate?: (number | null)[] | null;
  readonly spinAxis?: (number | null)[] | null;
  readonly carryDistance?: (number | null)[] | null;
  readonly totalDistance?: (number | null)[] | null;
  readonly side?: (number | null)[] | null;
  readonly sideTotal?: (number | null)[] | null;
  readonly apex?: (number | null)[] | null;
  readonly ballDirection?: (number | null)[] | null;
  readonly ballCurve?: (number | null)[] | null;
  readonly descentAngle?: (number | null)[] | null;
  readonly dynamicLoft?: (number | null)[] | null;
  readonly shotQuality?: (number | null)[] | null;
  readonly normalizedCarryDistance?: (number | null)[] | null;
  readonly normalizedTotalDistance?: (number | null)[] | null;
  readonly normalizedSide?: (number | null)[] | null;
  readonly normalizedSideTotal?: (number | null)[] | null;
  readonly normalizedApex?: (number | null)[] | null;
}

type LazyShotValues = {
  readonly clubId: string;
  readonly clubSpeed?: (number | null)[] | null;
  readonly ballSpeed?: (number | null)[] | null;
  readonly smashFactor?: (number | null)[] | null;
  readonly attackAngle?: (number | null)[] | null;
  readonly clubPath?: (number | null)[] | null;
  readonly launchAngle?: (number | null)[] | null;
  readonly horizontalLaunchAngle?: (number | null)[] | null;
  readonly faceAngle?: (number | null)[] | null;
  readonly spinRate?: (number | null)[] | null;
  readonly spinAxis?: (number | null)[] | null;
  readonly carryDistance?: (number | null)[] | null;
  readonly totalDistance?: (number | null)[] | null;
  readonly side?: (number | null)[] | null;
  readonly sideTotal?: (number | null)[] | null;
  readonly apex?: (number | null)[] | null;
  readonly ballDirection?: (number | null)[] | null;
  readonly ballCurve?: (number | null)[] | null;
  readonly descentAngle?: (number | null)[] | null;
  readonly dynamicLoft?: (number | null)[] | null;
  readonly shotQuality?: (number | null)[] | null;
  readonly normalizedCarryDistance?: (number | null)[] | null;
  readonly normalizedTotalDistance?: (number | null)[] | null;
  readonly normalizedSide?: (number | null)[] | null;
  readonly normalizedSideTotal?: (number | null)[] | null;
  readonly normalizedApex?: (number | null)[] | null;
}

export declare type ShotValues = LazyLoading extends LazyLoadingDisabled ? EagerShotValues : LazyShotValues

export declare const ShotValues: (new (init: ModelInit<ShotValues>) => ShotValues)

type EagerShotAverage = {
  readonly clubId: string;
  readonly clubSpeed?: number | null;
  readonly ballSpeed?: number | null;
  readonly smashFactor?: number | null;
  readonly attackAngle?: number | null;
  readonly clubPath?: number | null;
  readonly launchAngle?: number | null;
  readonly horizontalLaunchAngle?: number | null;
  readonly faceAngle?: number | null;
  readonly spinRate?: number | null;
  readonly spinAxis?: number | null;
  readonly carryDistance?: number | null;
  readonly totalDistance?: number | null;
  readonly side?: number | null;
  readonly sideTotal?: number | null;
  readonly apex?: number | null;
  readonly ballDirection?: number | null;
  readonly ballCurve?: number | null;
  readonly descentAngle?: number | null;
  readonly dynamicLoft?: number | null;
  readonly normalizedCarryDistance?: number | null;
  readonly normalizedTotalDistance?: number | null;
  readonly normalizedSide?: number | null;
  readonly normalizedSideTotal?: number | null;
  readonly normalizedApex?: number | null;
}

type LazyShotAverage = {
  readonly clubId: string;
  readonly clubSpeed?: number | null;
  readonly ballSpeed?: number | null;
  readonly smashFactor?: number | null;
  readonly attackAngle?: number | null;
  readonly clubPath?: number | null;
  readonly launchAngle?: number | null;
  readonly horizontalLaunchAngle?: number | null;
  readonly faceAngle?: number | null;
  readonly spinRate?: number | null;
  readonly spinAxis?: number | null;
  readonly carryDistance?: number | null;
  readonly totalDistance?: number | null;
  readonly side?: number | null;
  readonly sideTotal?: number | null;
  readonly apex?: number | null;
  readonly ballDirection?: number | null;
  readonly ballCurve?: number | null;
  readonly descentAngle?: number | null;
  readonly dynamicLoft?: number | null;
  readonly normalizedCarryDistance?: number | null;
  readonly normalizedTotalDistance?: number | null;
  readonly normalizedSide?: number | null;
  readonly normalizedSideTotal?: number | null;
  readonly normalizedApex?: number | null;
}

export declare type ShotAverage = LazyLoading extends LazyLoadingDisabled ? EagerShotAverage : LazyShotAverage

export declare const ShotAverage: (new (init: ModelInit<ShotAverage>) => ShotAverage)

type EagerRangeCount = {
  readonly range: number;
  readonly count: number;
}

type LazyRangeCount = {
  readonly range: number;
  readonly count: number;
}

export declare type RangeCount = LazyLoading extends LazyLoadingDisabled ? EagerRangeCount : LazyRangeCount

export declare const RangeCount: (new (init: ModelInit<RangeCount>) => RangeCount)

type EagerTimeWindowCount = {
  readonly start: number;
  readonly end: number;
  readonly count: number;
}

type LazyTimeWindowCount = {
  readonly start: number;
  readonly end: number;
  readonly count: number;
}

export declare type TimeWindowCount = LazyLoading extends LazyLoadingDisabled ? EagerTimeWindowCount : LazyTimeWindowCount

export declare const TimeWindowCount: (new (init: ModelInit<TimeWindowCount>) => TimeWindowCount)

type EagerClubCount = {
  readonly clubId: string;
  readonly count: number;
}

type LazyClubCount = {
  readonly clubId: string;
  readonly count: number;
}

export declare type ClubCount = LazyLoading extends LazyLoadingDisabled ? EagerClubCount : LazyClubCount

export declare const ClubCount: (new (init: ModelInit<ClubCount>) => ClubCount)

type EagerClubAverage = {
  readonly clubId: string;
  readonly average: number;
}

type LazyClubAverage = {
  readonly clubId: string;
  readonly average: number;
}

export declare type ClubAverage = LazyLoading extends LazyLoadingDisabled ? EagerClubAverage : LazyClubAverage

export declare const ClubAverage: (new (init: ModelInit<ClubAverage>) => ClubAverage)

type EagerRegisterShadow = {
  readonly regUser: string;
  readonly prodRegDate: string;
}

type LazyRegisterShadow = {
  readonly regUser: string;
  readonly prodRegDate: string;
}

export declare type RegisterShadow = LazyLoading extends LazyLoadingDisabled ? EagerRegisterShadow : LazyRegisterShadow

export declare const RegisterShadow: (new (init: ModelInit<RegisterShadow>) => RegisterShadow)

type EagerGraphQLResult = {
  readonly status: string;
}

type LazyGraphQLResult = {
  readonly status: string;
}

export declare type GraphQLResult = LazyLoading extends LazyLoadingDisabled ? EagerGraphQLResult : LazyGraphQLResult

export declare const GraphQLResult: (new (init: ModelInit<GraphQLResult>) => GraphQLResult)

type EagerCustomError = {
  readonly errorType?: string | null;
  readonly message?: string | null;
}

type LazyCustomError = {
  readonly errorType?: string | null;
  readonly message?: string | null;
}

export declare type CustomError = LazyLoading extends LazyLoadingDisabled ? EagerCustomError : LazyCustomError

export declare const CustomError: (new (init: ModelInit<CustomError>) => CustomError)

type EagerGraphQLResultAuth = {
  readonly status: string;
  readonly errors?: (CustomError | null)[] | null;
}

type LazyGraphQLResultAuth = {
  readonly status: string;
  readonly errors?: (CustomError | null)[] | null;
}

export declare type GraphQLResultAuth = LazyLoading extends LazyLoadingDisabled ? EagerGraphQLResultAuth : LazyGraphQLResultAuth

export declare const GraphQLResultAuth: (new (init: ModelInit<GraphQLResultAuth>) => GraphQLResultAuth)

type EagerGraphQLDeleteResultAuth = {
  readonly status: string;
  readonly errors?: (CustomError | null)[] | null;
  readonly id?: string | null;
}

type LazyGraphQLDeleteResultAuth = {
  readonly status: string;
  readonly errors?: (CustomError | null)[] | null;
  readonly id?: string | null;
}

export declare type GraphQLDeleteResultAuth = LazyLoading extends LazyLoadingDisabled ? EagerGraphQLDeleteResultAuth : LazyGraphQLDeleteResultAuth

export declare const GraphQLDeleteResultAuth: (new (init: ModelInit<GraphQLDeleteResultAuth>) => GraphQLDeleteResultAuth)

type EagerLmDataSessionListResult = {
  readonly nextToken?: string | null;
  readonly items?: (LmDataSession | null)[] | null;
}

type LazyLmDataSessionListResult = {
  readonly nextToken?: string | null;
  readonly items?: (LmDataSession | null)[] | null;
}

export declare type LmDataSessionListResult = LazyLoading extends LazyLoadingDisabled ? EagerLmDataSessionListResult : LazyLmDataSessionListResult

export declare const LmDataSessionListResult: (new (init: ModelInit<LmDataSessionListResult>) => LmDataSessionListResult)

type EagerLmDataResultsListResult = {
  readonly nextToken?: string | null;
  readonly items?: (LmDataResults | null)[] | null;
}

type LazyLmDataResultsListResult = {
  readonly nextToken?: string | null;
  readonly items?: (LmDataResults | null)[] | null;
}

export declare type LmDataResultsListResult = LazyLoading extends LazyLoadingDisabled ? EagerLmDataResultsListResult : LazyLmDataResultsListResult

export declare const LmDataResultsListResult: (new (init: ModelInit<LmDataResultsListResult>) => LmDataResultsListResult)

type EagerLmTeamPlayers = {
  readonly id?: string | null;
}

type LazyLmTeamPlayers = {
  readonly id?: string | null;
}

export declare type LmTeamPlayers = LazyLoading extends LazyLoadingDisabled ? EagerLmTeamPlayers : LazyLmTeamPlayers

export declare const LmTeamPlayers: (new (init: ModelInit<LmTeamPlayers>) => LmTeamPlayers)

type EagerLmTeamCoaches = {
  readonly id?: string | null;
}

type LazyLmTeamCoaches = {
  readonly id?: string | null;
}

export declare type LmTeamCoaches = LazyLoading extends LazyLoadingDisabled ? EagerLmTeamCoaches : LazyLmTeamCoaches

export declare const LmTeamCoaches: (new (init: ModelInit<LmTeamCoaches>) => LmTeamCoaches)

type EagerLmTeam = {
  readonly id?: string | null;
}

type LazyLmTeam = {
  readonly id?: string | null;
}

export declare type LmTeam = LazyLoading extends LazyLoadingDisabled ? EagerLmTeam : LazyLmTeam

export declare const LmTeam: (new (init: ModelInit<LmTeam>) => LmTeam)

type EagerLmDataResults = {
  readonly id?: string | null;
}

type LazyLmDataResults = {
  readonly id?: string | null;
}

export declare type LmDataResults = LazyLoading extends LazyLoadingDisabled ? EagerLmDataResults : LazyLmDataResults

export declare const LmDataResults: (new (init: ModelInit<LmDataResults>) => LmDataResults)

type EagerLmDataSession = {
  readonly id?: string | null;
}

type LazyLmDataSession = {
  readonly id?: string | null;
}

export declare type LmDataSession = LazyLoading extends LazyLoadingDisabled ? EagerLmDataSession : LazyLmDataSession

export declare const LmDataSession: (new (init: ModelInit<LmDataSession>) => LmDataSession)

type EagerLmShareSession = {
  readonly shareUrl?: string | null;
}

type LazyLmShareSession = {
  readonly shareUrl?: string | null;
}

export declare type LmShareSession = LazyLoading extends LazyLoadingDisabled ? EagerLmShareSession : LazyLmShareSession

export declare const LmShareSession: (new (init: ModelInit<LmShareSession>) => LmShareSession)

type LmUserMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type LmProfileMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type LmFeatureMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type LmDeviceMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type LmSessionMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type LmShotMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type LmClubMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type LmDrillTemplateMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type LmDrillMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type LmSessionStatsMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type EagerLmUser = {
  readonly id: string;
  readonly setupComplete: boolean;
  readonly email?: string | null;
  readonly phone?: string | null;
  readonly fullName?: string | null;
  readonly profileImage?: string | null;
  readonly userType?: string | null;
  readonly gender?: string | null;
  readonly handedness?: LmHandedness | keyof typeof LmHandedness | null;
  readonly birthdate?: number | null;
  readonly companyName?: string | null;
  readonly shippingAddressLine1?: string | null;
  readonly shippingAddressLine2?: string | null;
  readonly shippingPostcode?: string | null;
  readonly shippingLocality?: string | null;
  readonly shippingRegion?: string | null;
  readonly shippingCountry?: string | null;
  readonly subscriptions?: string | null;
  readonly competitiveLevel?: CompetitiveLevel | keyof typeof CompetitiveLevel | null;
  readonly teamName?: string | null;
  readonly organizationSchool?: string | null;
  readonly baseballPlayerPosition?: string | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmUser = {
  readonly id: string;
  readonly setupComplete: boolean;
  readonly email?: string | null;
  readonly phone?: string | null;
  readonly fullName?: string | null;
  readonly profileImage?: string | null;
  readonly userType?: string | null;
  readonly gender?: string | null;
  readonly handedness?: LmHandedness | keyof typeof LmHandedness | null;
  readonly birthdate?: number | null;
  readonly companyName?: string | null;
  readonly shippingAddressLine1?: string | null;
  readonly shippingAddressLine2?: string | null;
  readonly shippingPostcode?: string | null;
  readonly shippingLocality?: string | null;
  readonly shippingRegion?: string | null;
  readonly shippingCountry?: string | null;
  readonly subscriptions?: string | null;
  readonly competitiveLevel?: CompetitiveLevel | keyof typeof CompetitiveLevel | null;
  readonly teamName?: string | null;
  readonly organizationSchool?: string | null;
  readonly baseballPlayerPosition?: string | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmUser = LazyLoading extends LazyLoadingDisabled ? EagerLmUser : LazyLmUser

export declare const LmUser: (new (init: ModelInit<LmUser, LmUserMetaData>) => LmUser) & {
  copyOf(source: LmUser, mutator: (draft: MutableModel<LmUser, LmUserMetaData>) => MutableModel<LmUser, LmUserMetaData> | void): LmUser;
}

type EagerLmProfile = {
  readonly id: string;
  readonly userId: string;
  readonly setupComplete: boolean;
  readonly showDevicePlacement?: boolean | null;
  readonly videoCapture?: boolean | null;
  readonly normalized?: boolean | null;
  readonly normalizedElevation?: number | null;
  readonly normalizedTemperature?: number | null;
  readonly normalizedBallType?: LmBallType | keyof typeof LmBallType | null;
  readonly normalizedOutdoorBallType?: LmBallType | keyof typeof LmBallType | null;
  readonly distanceUnits?: LmDistanceUnits | keyof typeof LmDistanceUnits | null;
  readonly speedUnits?: LmSpeedUnits | keyof typeof LmSpeedUnits | null;
  readonly apexUnits?: LmDistanceUnits | keyof typeof LmDistanceUnits | null;
  readonly elevationUnits?: LmDistanceUnits | keyof typeof LmDistanceUnits | null;
  readonly temperatureUnits?: LmTemperatureUnits | keyof typeof LmTemperatureUnits | null;
  readonly deviceDisplayMode?: LmDisplayMode | keyof typeof LmDisplayMode | null;
  readonly language?: string | null;
  readonly displayMode?: LmDisplayMode | keyof typeof LmDisplayMode | null;
  readonly theme?: LmTheme | keyof typeof LmTheme | null;
  readonly chosenPanel?: number | null;
  readonly recordClubs?: boolean | null;
  readonly useTileRangeView?: boolean | null;
  readonly targetOption?: LmTargetOption | keyof typeof LmTargetOption | null;
  readonly cameraView?: LmCameraView | keyof typeof LmCameraView | null;
  readonly ballPath?: LmBallPath | keyof typeof LmBallPath | null;
  readonly appTiles?: (ValueType | null)[] | Array<keyof typeof ValueType> | null;
  readonly watchTiles?: (ValueType | null)[] | Array<keyof typeof ValueType> | null;
  readonly deviceTiles?: (ValueType | null)[] | Array<keyof typeof ValueType> | null;
  readonly appTilesEnabled?: (boolean | null)[] | null;
  readonly watchTilesEnabled?: (boolean | null)[] | null;
  readonly baseballAppData?: (BaseBallValueType | null)[] | Array<keyof typeof BaseBallValueType> | null;
  readonly baseballAppTilesEnabled?: (boolean | null)[] | null;
  readonly baseballDeviceTiles?: (BaseballValueType | null)[] | Array<keyof typeof BaseballValueType> | null;
  readonly baseballWatchTiles?: (BaseballValueType | null)[] | Array<keyof typeof BaseballValueType> | null;
  readonly clubSpeedAudio?: boolean | null;
  readonly ballSpeedAudio?: boolean | null;
  readonly smashFactorAudio?: boolean | null;
  readonly attackAngleAudio?: boolean | null;
  readonly clubPathAudio?: boolean | null;
  readonly launchAngleAudio?: boolean | null;
  readonly horizontalLaunchAngleAudio?: boolean | null;
  readonly faceAngleAudio?: boolean | null;
  readonly spinRateAudio?: boolean | null;
  readonly spinAxisAudio?: boolean | null;
  readonly carryDistanceAudio?: boolean | null;
  readonly totalDistanceAudio?: boolean | null;
  readonly sideAudio?: boolean | null;
  readonly sideTotalAudio?: boolean | null;
  readonly apexAudio?: boolean | null;
  readonly features?: (LmFeature | null)[] | null;
  readonly devices?: (LmDevice | null)[] | null;
  readonly sessions?: (LmSession | null)[] | null;
  readonly clubs?: (LmClub | null)[] | null;
  readonly drills?: (LmDrill | null)[] | null;
  readonly user?: LmUser | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmProfile = {
  readonly id: string;
  readonly userId: string;
  readonly setupComplete: boolean;
  readonly showDevicePlacement?: boolean | null;
  readonly videoCapture?: boolean | null;
  readonly normalized?: boolean | null;
  readonly normalizedElevation?: number | null;
  readonly normalizedTemperature?: number | null;
  readonly normalizedBallType?: LmBallType | keyof typeof LmBallType | null;
  readonly normalizedOutdoorBallType?: LmBallType | keyof typeof LmBallType | null;
  readonly distanceUnits?: LmDistanceUnits | keyof typeof LmDistanceUnits | null;
  readonly speedUnits?: LmSpeedUnits | keyof typeof LmSpeedUnits | null;
  readonly apexUnits?: LmDistanceUnits | keyof typeof LmDistanceUnits | null;
  readonly elevationUnits?: LmDistanceUnits | keyof typeof LmDistanceUnits | null;
  readonly temperatureUnits?: LmTemperatureUnits | keyof typeof LmTemperatureUnits | null;
  readonly deviceDisplayMode?: LmDisplayMode | keyof typeof LmDisplayMode | null;
  readonly language?: string | null;
  readonly displayMode?: LmDisplayMode | keyof typeof LmDisplayMode | null;
  readonly theme?: LmTheme | keyof typeof LmTheme | null;
  readonly chosenPanel?: number | null;
  readonly recordClubs?: boolean | null;
  readonly useTileRangeView?: boolean | null;
  readonly targetOption?: LmTargetOption | keyof typeof LmTargetOption | null;
  readonly cameraView?: LmCameraView | keyof typeof LmCameraView | null;
  readonly ballPath?: LmBallPath | keyof typeof LmBallPath | null;
  readonly appTiles?: (ValueType | null)[] | Array<keyof typeof ValueType> | null;
  readonly watchTiles?: (ValueType | null)[] | Array<keyof typeof ValueType> | null;
  readonly deviceTiles?: (ValueType | null)[] | Array<keyof typeof ValueType> | null;
  readonly appTilesEnabled?: (boolean | null)[] | null;
  readonly watchTilesEnabled?: (boolean | null)[] | null;
  readonly baseballAppData?: (BaseBallValueType | null)[] | Array<keyof typeof BaseBallValueType> | null;
  readonly baseballAppTilesEnabled?: (boolean | null)[] | null;
  readonly baseballDeviceTiles?: (BaseballValueType | null)[] | Array<keyof typeof BaseballValueType> | null;
  readonly baseballWatchTiles?: (BaseballValueType | null)[] | Array<keyof typeof BaseballValueType> | null;
  readonly clubSpeedAudio?: boolean | null;
  readonly ballSpeedAudio?: boolean | null;
  readonly smashFactorAudio?: boolean | null;
  readonly attackAngleAudio?: boolean | null;
  readonly clubPathAudio?: boolean | null;
  readonly launchAngleAudio?: boolean | null;
  readonly horizontalLaunchAngleAudio?: boolean | null;
  readonly faceAngleAudio?: boolean | null;
  readonly spinRateAudio?: boolean | null;
  readonly spinAxisAudio?: boolean | null;
  readonly carryDistanceAudio?: boolean | null;
  readonly totalDistanceAudio?: boolean | null;
  readonly sideAudio?: boolean | null;
  readonly sideTotalAudio?: boolean | null;
  readonly apexAudio?: boolean | null;
  readonly features: AsyncCollection<LmFeature>;
  readonly devices: AsyncCollection<LmDevice>;
  readonly sessions: AsyncCollection<LmSession>;
  readonly clubs: AsyncCollection<LmClub>;
  readonly drills: AsyncCollection<LmDrill>;
  readonly user: AsyncItem<LmUser | undefined>;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmProfile = LazyLoading extends LazyLoadingDisabled ? EagerLmProfile : LazyLmProfile

export declare const LmProfile: (new (init: ModelInit<LmProfile, LmProfileMetaData>) => LmProfile) & {
  copyOf(source: LmProfile, mutator: (draft: MutableModel<LmProfile, LmProfileMetaData>) => MutableModel<LmProfile, LmProfileMetaData> | void): LmProfile;
}

type EagerLmFeature = {
  readonly id: string;
  readonly lmProfileId: string;
  readonly owner?: string | null;
  readonly featureName?: string | null;
  readonly enabled?: boolean | null;
  readonly expiresAt?: string | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmFeature = {
  readonly id: string;
  readonly lmProfileId: string;
  readonly owner?: string | null;
  readonly featureName?: string | null;
  readonly enabled?: boolean | null;
  readonly expiresAt?: string | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmFeature = LazyLoading extends LazyLoadingDisabled ? EagerLmFeature : LazyLmFeature

export declare const LmFeature: (new (init: ModelInit<LmFeature, LmFeatureMetaData>) => LmFeature) & {
  copyOf(source: LmFeature, mutator: (draft: MutableModel<LmFeature, LmFeatureMetaData>) => MutableModel<LmFeature, LmFeatureMetaData> | void): LmFeature;
}

type EagerLmDevice = {
  readonly id: string;
  readonly lmProfileId: string;
  readonly owner?: string | null;
  readonly deviceID?: string | null;
  readonly advertisementName?: string | null;
  readonly connectID?: string | null;
  readonly configuredWifiSSID?: string | null;
  readonly serialNumber?: string | null;
  readonly modelNumber?: string | null;
  readonly firmwareVersion?: string | null;
  readonly autoConnect?: boolean | null;
  readonly registered?: boolean | null;
  readonly registeredUser?: string | null;
  readonly registrationDate?: number | null;
  readonly registrationReminder?: boolean | null;
  readonly registrationReminderTime?: number | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmDevice = {
  readonly id: string;
  readonly lmProfileId: string;
  readonly owner?: string | null;
  readonly deviceID?: string | null;
  readonly advertisementName?: string | null;
  readonly connectID?: string | null;
  readonly configuredWifiSSID?: string | null;
  readonly serialNumber?: string | null;
  readonly modelNumber?: string | null;
  readonly firmwareVersion?: string | null;
  readonly autoConnect?: boolean | null;
  readonly registered?: boolean | null;
  readonly registeredUser?: string | null;
  readonly registrationDate?: number | null;
  readonly registrationReminder?: boolean | null;
  readonly registrationReminderTime?: number | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmDevice = LazyLoading extends LazyLoadingDisabled ? EagerLmDevice : LazyLmDevice

export declare const LmDevice: (new (init: ModelInit<LmDevice, LmDeviceMetaData>) => LmDevice) & {
  copyOf(source: LmDevice, mutator: (draft: MutableModel<LmDevice, LmDeviceMetaData>) => MutableModel<LmDevice, LmDeviceMetaData> | void): LmDevice;
}

type EagerLmSession = {
  readonly id: string;
  readonly owner?: string | null;
  readonly deviceID?: string | null;
  readonly startTimestamp: number;
  readonly endTimestamp: number;
  readonly duration?: number | null;
  readonly name?: string | null;
  readonly city?: string | null;
  readonly state?: string | null;
  readonly country?: string | null;
  readonly address?: string | null;
  readonly courseName?: string | null;
  readonly elevation?: number | null;
  readonly latitude?: number | null;
  readonly longitude?: number | null;
  readonly temperature?: number | null;
  readonly humidity?: number | null;
  readonly location?: LmLocation | keyof typeof LmLocation | null;
  readonly normalizedElevation?: number | null;
  readonly normalizedTemperature?: number | null;
  readonly normalizedBallType?: LmBallType | keyof typeof LmBallType | null;
  readonly normalized?: boolean | null;
  readonly sessionQuality?: number | null;
  readonly drillTargets?: (LmDrillTarget | null)[] | null;
  readonly shots?: (LmShot | null)[] | null;
  readonly profile?: LmProfile | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmSession = {
  readonly id: string;
  readonly owner?: string | null;
  readonly deviceID?: string | null;
  readonly startTimestamp: number;
  readonly endTimestamp: number;
  readonly duration?: number | null;
  readonly name?: string | null;
  readonly city?: string | null;
  readonly state?: string | null;
  readonly country?: string | null;
  readonly address?: string | null;
  readonly courseName?: string | null;
  readonly elevation?: number | null;
  readonly latitude?: number | null;
  readonly longitude?: number | null;
  readonly temperature?: number | null;
  readonly humidity?: number | null;
  readonly location?: LmLocation | keyof typeof LmLocation | null;
  readonly normalizedElevation?: number | null;
  readonly normalizedTemperature?: number | null;
  readonly normalizedBallType?: LmBallType | keyof typeof LmBallType | null;
  readonly normalized?: boolean | null;
  readonly sessionQuality?: number | null;
  readonly drillTargets?: (LmDrillTarget | null)[] | null;
  readonly shots: AsyncCollection<LmShot>;
  readonly profile: AsyncItem<LmProfile | undefined>;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmSession = LazyLoading extends LazyLoadingDisabled ? EagerLmSession : LazyLmSession

export declare const LmSession: (new (init: ModelInit<LmSession, LmSessionMetaData>) => LmSession) & {
  copyOf(source: LmSession, mutator: (draft: MutableModel<LmSession, LmSessionMetaData>) => MutableModel<LmSession, LmSessionMetaData> | void): LmSession;
}

type EagerLmShot = {
  readonly id: string;
  readonly lmSessionId: string;
  readonly lmDrillTargetId?: string | null;
  readonly clubId: string;
  readonly owner?: string | null;
  readonly clubCategory?: string | null;
  readonly pointId?: string | null;
  readonly timestamp: number;
  readonly isFavorite?: boolean | null;
  readonly clubSpeed?: number | null;
  readonly ballSpeed?: number | null;
  readonly smashFactor?: number | null;
  readonly attackAngle?: number | null;
  readonly clubPath?: number | null;
  readonly launchAngle?: number | null;
  readonly horizontalLaunchAngle?: number | null;
  readonly faceAngle?: number | null;
  readonly spinRate?: number | null;
  readonly spinAxis?: number | null;
  readonly carryDistance?: number | null;
  readonly totalDistance?: number | null;
  readonly side?: number | null;
  readonly sideTotal?: number | null;
  readonly apex?: number | null;
  readonly ballDirection?: number | null;
  readonly ballCurve?: number | null;
  readonly descentAngle?: number | null;
  readonly dynamicLoft?: number | null;
  readonly shotQuality?: number | null;
  readonly targetDistance?: number | null;
  readonly distanceToPin?: number | null;
  readonly impactKey?: string | null;
  readonly videoKey?: string | null;
  readonly pointCloudKey?: string | null;
  readonly protobufKey?: string | null;
  readonly clubSpeedValid?: boolean | null;
  readonly ballSpeedValid?: boolean | null;
  readonly smashFactorValid?: boolean | null;
  readonly attackAngleValid?: boolean | null;
  readonly clubPathValid?: boolean | null;
  readonly launchAngleValid?: boolean | null;
  readonly horizontalLaunchAngleValid?: boolean | null;
  readonly faceAngleValid?: boolean | null;
  readonly spinRateValid?: boolean | null;
  readonly spinAxisValid?: boolean | null;
  readonly carryDistanceValid?: boolean | null;
  readonly totalDistanceValid?: boolean | null;
  readonly sideValid?: boolean | null;
  readonly sideTotalValid?: boolean | null;
  readonly apexValid?: boolean | null;
  readonly descentAngleValid?: boolean | null;
  readonly dynamicLoftValid?: boolean | null;
  readonly xFit?: (number | null)[] | null;
  readonly yFit?: (number | null)[] | null;
  readonly zFit?: (number | null)[] | null;
  readonly club?: LmClub | null;
  readonly normalizedValues?: LmShotNormalizedValues | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmShot = {
  readonly id: string;
  readonly lmSessionId: string;
  readonly lmDrillTargetId?: string | null;
  readonly clubId: string;
  readonly owner?: string | null;
  readonly clubCategory?: string | null;
  readonly pointId?: string | null;
  readonly timestamp: number;
  readonly isFavorite?: boolean | null;
  readonly clubSpeed?: number | null;
  readonly ballSpeed?: number | null;
  readonly smashFactor?: number | null;
  readonly attackAngle?: number | null;
  readonly clubPath?: number | null;
  readonly launchAngle?: number | null;
  readonly horizontalLaunchAngle?: number | null;
  readonly faceAngle?: number | null;
  readonly spinRate?: number | null;
  readonly spinAxis?: number | null;
  readonly carryDistance?: number | null;
  readonly totalDistance?: number | null;
  readonly side?: number | null;
  readonly sideTotal?: number | null;
  readonly apex?: number | null;
  readonly ballDirection?: number | null;
  readonly ballCurve?: number | null;
  readonly descentAngle?: number | null;
  readonly dynamicLoft?: number | null;
  readonly shotQuality?: number | null;
  readonly targetDistance?: number | null;
  readonly distanceToPin?: number | null;
  readonly impactKey?: string | null;
  readonly videoKey?: string | null;
  readonly pointCloudKey?: string | null;
  readonly protobufKey?: string | null;
  readonly clubSpeedValid?: boolean | null;
  readonly ballSpeedValid?: boolean | null;
  readonly smashFactorValid?: boolean | null;
  readonly attackAngleValid?: boolean | null;
  readonly clubPathValid?: boolean | null;
  readonly launchAngleValid?: boolean | null;
  readonly horizontalLaunchAngleValid?: boolean | null;
  readonly faceAngleValid?: boolean | null;
  readonly spinRateValid?: boolean | null;
  readonly spinAxisValid?: boolean | null;
  readonly carryDistanceValid?: boolean | null;
  readonly totalDistanceValid?: boolean | null;
  readonly sideValid?: boolean | null;
  readonly sideTotalValid?: boolean | null;
  readonly apexValid?: boolean | null;
  readonly descentAngleValid?: boolean | null;
  readonly dynamicLoftValid?: boolean | null;
  readonly xFit?: (number | null)[] | null;
  readonly yFit?: (number | null)[] | null;
  readonly zFit?: (number | null)[] | null;
  readonly club: AsyncItem<LmClub | undefined>;
  readonly normalizedValues?: LmShotNormalizedValues | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmShot = LazyLoading extends LazyLoadingDisabled ? EagerLmShot : LazyLmShot

export declare const LmShot: (new (init: ModelInit<LmShot, LmShotMetaData>) => LmShot) & {
  copyOf(source: LmShot, mutator: (draft: MutableModel<LmShot, LmShotMetaData>) => MutableModel<LmShot, LmShotMetaData> | void): LmShot;
}

type EagerLmClub = {
  readonly id: string;
  readonly lmProfileId: string;
  readonly owner?: string | null;
  readonly type?: LmClubType | keyof typeof LmClubType | null;
  readonly name?: string | null;
  readonly brand?: string | null;
  readonly model?: string | null;
  readonly shaft?: string | null;
  readonly color?: string | null;
  readonly listOrder?: number | null;
  readonly isActive?: boolean | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmClub = {
  readonly id: string;
  readonly lmProfileId: string;
  readonly owner?: string | null;
  readonly type?: LmClubType | keyof typeof LmClubType | null;
  readonly name?: string | null;
  readonly brand?: string | null;
  readonly model?: string | null;
  readonly shaft?: string | null;
  readonly color?: string | null;
  readonly listOrder?: number | null;
  readonly isActive?: boolean | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmClub = LazyLoading extends LazyLoadingDisabled ? EagerLmClub : LazyLmClub

export declare const LmClub: (new (init: ModelInit<LmClub, LmClubMetaData>) => LmClub) & {
  copyOf(source: LmClub, mutator: (draft: MutableModel<LmClub, LmClubMetaData>) => MutableModel<LmClub, LmClubMetaData> | void): LmClub;
}

type EagerLmDrillTemplate = {
  readonly id: string;
  readonly type?: LmDrillTargetType | keyof typeof LmDrillTargetType | null;
  readonly name?: string | null;
  readonly ownerName?: string | null;
  readonly logoUrl?: string | null;
  readonly targets?: (LmDrillTarget | null)[] | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmDrillTemplate = {
  readonly id: string;
  readonly type?: LmDrillTargetType | keyof typeof LmDrillTargetType | null;
  readonly name?: string | null;
  readonly ownerName?: string | null;
  readonly logoUrl?: string | null;
  readonly targets?: (LmDrillTarget | null)[] | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmDrillTemplate = LazyLoading extends LazyLoadingDisabled ? EagerLmDrillTemplate : LazyLmDrillTemplate

export declare const LmDrillTemplate: (new (init: ModelInit<LmDrillTemplate, LmDrillTemplateMetaData>) => LmDrillTemplate) & {
  copyOf(source: LmDrillTemplate, mutator: (draft: MutableModel<LmDrillTemplate, LmDrillTemplateMetaData>) => MutableModel<LmDrillTemplate, LmDrillTemplateMetaData> | void): LmDrillTemplate;
}

type EagerLmDrill = {
  readonly id: string;
  readonly owner?: string | null;
  readonly templateId?: string | null;
  readonly type?: LmDrillTargetType | keyof typeof LmDrillTargetType | null;
  readonly name?: string | null;
  readonly logoUrl?: string | null;
  readonly targets?: (LmDrillTarget | null)[] | null;
  readonly profile?: LmProfile | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmDrill = {
  readonly id: string;
  readonly owner?: string | null;
  readonly templateId?: string | null;
  readonly type?: LmDrillTargetType | keyof typeof LmDrillTargetType | null;
  readonly name?: string | null;
  readonly logoUrl?: string | null;
  readonly targets?: (LmDrillTarget | null)[] | null;
  readonly profile: AsyncItem<LmProfile | undefined>;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmDrill = LazyLoading extends LazyLoadingDisabled ? EagerLmDrill : LazyLmDrill

export declare const LmDrill: (new (init: ModelInit<LmDrill, LmDrillMetaData>) => LmDrill) & {
  copyOf(source: LmDrill, mutator: (draft: MutableModel<LmDrill, LmDrillMetaData>) => MutableModel<LmDrill, LmDrillMetaData> | void): LmDrill;
}

type EagerLmSessionStats = {
  readonly id: string;
  readonly lmSessionId: string;
  readonly owner?: string | null;
  readonly shotCount?: number | null;
  readonly values?: (ShotValues | null)[] | null;
  readonly averages?: (ShotAverage | null)[] | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmSessionStats = {
  readonly id: string;
  readonly lmSessionId: string;
  readonly owner?: string | null;
  readonly shotCount?: number | null;
  readonly values?: (ShotValues | null)[] | null;
  readonly averages?: (ShotAverage | null)[] | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmSessionStats = LazyLoading extends LazyLoadingDisabled ? EagerLmSessionStats : LazyLmSessionStats

export declare const LmSessionStats: (new (init: ModelInit<LmSessionStats, LmSessionStatsMetaData>) => LmSessionStats) & {
  copyOf(source: LmSessionStats, mutator: (draft: MutableModel<LmSessionStats, LmSessionStatsMetaData>) => MutableModel<LmSessionStats, LmSessionStatsMetaData> | void): LmSessionStats;
}