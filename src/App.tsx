import {Authenticator} from '@aws-amplify/ui-react';
import {useState} from 'react';
import '@aws-amplify/ui-react/styles.css';
import { fetchAuthSession } from 'aws-amplify/auth';

function AppContent() {
    const [file, setFile] = useState(null);
    const [uploading, setUploading] = useState(false);
    const [imageUrl] = useState(null);

    const handleFileChange = (event) => {
        const uploadedFile = event.target.files[0];
        if (uploadedFile) {
            setFile(uploadedFile);
        }
    };

    const handleUpload = async () => {
        await fetchAuthSession({ forceRefresh: true });
    };

    const getRole  = async () => {
        const session = await fetchAuthSession();
        const idToken = session.tokens?.idToken?.payload;
        const roles = idToken?.["cognito:groups"] || [];
        console.log("User roles:", roles);
    }

    return (
        <div>
            <h2>Upload ảnh lên S3</h2>
            <input type="file" onChange={handleFileChange} />
            <button onClick={handleUpload} disabled={uploading}>
                {uploading ? 'Đang upload...' : 'Upload'}
            </button>
            <button onClick={getRole}>
                Test
            </button>

            {imageUrl && (
                <div style={{ marginTop: 20 }}>
                    <p>Ảnh đã upload:</p>
                    <img src={imageUrl} alt="uploaded" style={{ maxWidth: 300 }} />
                </div>
            )}
        </div>
    );
}

export default function App() {
    const [role, setRole] = useState<string>('');
    const [code, setCode] = useState<string>('df08ebec-6003-4a25-9801-10b76c84a8ad');
    const handle = (event: any) => setRole(event.target.value);
    const handleCode = (event: any) => setCode(event.target.value);
    return (
        <Authenticator
            components={{
                SignUp: {
                    FormFields() {
                        return (
                            <>
                                <Authenticator.SignUp.FormFields/>

                                {/*<div className="amplify-field">*/}
                                {/*    <input*/}
                                {/*        name="custom:role"*/}
                                {/*        type="text"*/}
                                {/*        value={role}*/}
                                {/*        onChange={handle}*/}
                                {/*    />*/}
                                {/*</div>*/}
                                <div className="amplify-field">
                                    <input
                                        name="custom:invite_code"
                                        type="text"
                                        value={code}
                                        onChange={handleCode}
                                    />
                                </div>
                            </>
                        );
                    },
                },
            }}
            signUpAttributes={['email', "name"]}
        >
            <AppContent/>
        </Authenticator>
    );
}
