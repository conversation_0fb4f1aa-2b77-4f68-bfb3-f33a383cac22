export const schema = {
    "models": {
        "LmTeamInviteCodes": {
            "name": "LmTeamInviteCodes",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "link": {
                    "name": "link",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "sport": {
                    "name": "sport",
                    "isArray": false,
                    "type": {
                        "enum": "LmSportType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "type": {
                    "name": "type",
                    "isArray": false,
                    "type": {
                        "enum": "LmInviteCodeType"
                    },
                    "isRequired": true,
                    "attributes": []
                },
                "email": {
                    "name": "email",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "lmPlayerName": {
                    "name": "lmPlayerName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "lmTeamId": {
                    "name": "lmTeamId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": false,
                    "attributes": []
                },
                "expired_at": {
                    "name": "expired_at",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": []
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmTeamInviteCodes",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byTeam",
                        "queryField": "teamInviteCodesByTeam",
                        "fields": [
                            "lmTeamId"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "allow": "private",
                                "provider": "iam",
                                "operations": [
                                    "create"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmTeamPlayers": {
            "name": "LmTeamPlayers",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "readers": {
                    "name": "readers",
                    "isArray": true,
                    "type": "String",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "type": {
                    "name": "type",
                    "isArray": false,
                    "type": {
                        "enum": "LmTeamPlayerType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "status": {
                    "name": "status",
                    "isArray": false,
                    "type": {
                        "enum": "LmTeamPlayerStatus"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "email": {
                    "name": "email",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "joined_at": {
                    "name": "joined_at",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": []
                },
                "lmPlayerId": {
                    "name": "lmPlayerId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": false,
                    "attributes": []
                },
                "lmPlayerName": {
                    "name": "lmPlayerName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "lineupOrderBatting": {
                    "name": "lineupOrderBatting",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "lineupOrderPitching": {
                    "name": "lineupOrderPitching",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "team": {
                    "name": "team",
                    "isArray": false,
                    "type": {
                        "model": "LmTeam"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "association": {
                        "connectionType": "BELONGS_TO",
                        "targetName": "lmTeamId"
                    }
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmTeamPlayers",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byOwner",
                        "queryField": "teamPlayersByUser",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byEmail",
                        "queryField": "teamPlayersByEmail",
                        "fields": [
                            "email"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byTeam",
                        "queryField": "teamPlayersByTeam",
                        "fields": [
                            "lmTeamId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byTeamAndJoinedAt",
                        "queryField": "teamPlayersByTeamAndJoinedAt",
                        "fields": [
                            "lmTeamId",
                            "joined_at"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byPlayer",
                        "queryField": "teamPlayersByPlayer",
                        "fields": [
                            "lmPlayerId"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "readers",
                                "allow": "owner",
                                "operations": [
                                    "read"
                                ],
                                "identityClaim": "cognito:username"
                            },
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "allow": "private",
                                "provider": "iam",
                                "operations": [
                                    "create",
                                    "update",
                                    "read",
                                    "delete"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmTeamCoaches": {
            "name": "LmTeamCoaches",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "lmTeamId": {
                    "name": "lmTeamId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": false,
                    "attributes": []
                },
                "lmCoachId": {
                    "name": "lmCoachId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": false,
                    "attributes": []
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmTeamCoaches",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byOwner",
                        "queryField": "teamCoachesByUser",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byTeam",
                        "queryField": "teamCoachesByTeam",
                        "fields": [
                            "lmTeamId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byCoach",
                        "queryField": "teamCoachesByCoach",
                        "fields": [
                            "lmCoachId"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "allow": "private",
                                "operations": [
                                    "read"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmTeam": {
            "name": "LmTeam",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "name": {
                    "name": "name",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "profileImage": {
                    "name": "profileImage",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "pitcherId": {
                    "name": "pitcherId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": false,
                    "attributes": []
                },
                "readers": {
                    "name": "readers",
                    "isArray": true,
                    "type": "String",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "players": {
                    "name": "players",
                    "isArray": true,
                    "type": {
                        "model": "LmTeamPlayers"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "team"
                    }
                },
                "coaches": {
                    "name": "coaches",
                    "isArray": true,
                    "type": {
                        "model": "LmTeamCoaches"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "lmTeamId"
                    }
                },
                "dataSessions": {
                    "name": "dataSessions",
                    "isArray": true,
                    "type": {
                        "model": "LmDataSession"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "team"
                    }
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmTeams",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byOwner",
                        "queryField": "teamsByOwner",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "readers",
                                "allow": "owner",
                                "operations": [
                                    "read"
                                ],
                                "identityClaim": "cognito:username"
                            },
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "read",
                                    "update"
                                ]
                            },
                            {
                                "allow": "private",
                                "provider": "iam",
                                "operations": [
                                    "delete",
                                    "update"
                                ]
                            },
                            {
                                "allow": "private",
                                "operations": [
                                    "get"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Coaches"
                                ],
                                "operations": [
                                    "create"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmCoachPlayers": {
            "name": "LmCoachPlayers",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "lmCoachId": {
                    "name": "lmCoachId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "lmPlayerId": {
                    "name": "lmPlayerId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": false,
                    "attributes": []
                },
                "lmPlayerName": {
                    "name": "lmPlayerName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "status": {
                    "name": "status",
                    "isArray": false,
                    "type": {
                        "enum": "LmTeamPlayerStatus"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "lmPlayerEmail": {
                    "name": "lmPlayerEmail",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "teamAssigned": {
                    "name": "teamAssigned",
                    "isArray": false,
                    "type": {
                        "enum": "LmCoachPlayersTeamAssigned"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmCoachPlayers",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byCoach",
                        "queryField": "coachPlayersByCoach",
                        "fields": [
                            "lmCoachId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byLmPlayerEmail",
                        "queryField": "coachPlayersLmPlayerEmail",
                        "fields": [
                            "lmPlayerEmail"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byTeamAssigned",
                        "queryField": "coachPlayersByTeamAssigned",
                        "fields": [
                            "teamAssigned"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "allow": "private",
                                "provider": "iam",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmUser": {
            "name": "LmUser",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "setupComplete": {
                    "name": "setupComplete",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": true,
                    "attributes": []
                },
                "email": {
                    "name": "email",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "phone": {
                    "name": "phone",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "fullName": {
                    "name": "fullName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "profileImage": {
                    "name": "profileImage",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "userType": {
                    "name": "userType",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "gender": {
                    "name": "gender",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "handedness": {
                    "name": "handedness",
                    "isArray": false,
                    "type": {
                        "enum": "LmHandedness"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "birthdate": {
                    "name": "birthdate",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "companyName": {
                    "name": "companyName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "shippingAddressLine1": {
                    "name": "shippingAddressLine1",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "shippingAddressLine2": {
                    "name": "shippingAddressLine2",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "shippingPostcode": {
                    "name": "shippingPostcode",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "shippingLocality": {
                    "name": "shippingLocality",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "shippingRegion": {
                    "name": "shippingRegion",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "shippingCountry": {
                    "name": "shippingCountry",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "subscriptions": {
                    "name": "subscriptions",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "competitiveLevel": {
                    "name": "competitiveLevel",
                    "isArray": false,
                    "type": {
                        "enum": "CompetitiveLevel"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "teamName": {
                    "name": "teamName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "organizationSchool": {
                    "name": "organizationSchool",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "baseballPlayerPosition": {
                    "name": "baseballPlayerPosition",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "coachedTeams": {
                    "name": "coachedTeams",
                    "isArray": true,
                    "type": {
                        "model": "LmTeamCoaches"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "lmCoachId"
                    }
                },
                "playerTeams": {
                    "name": "playerTeams",
                    "isArray": true,
                    "type": {
                        "model": "LmTeamPlayers"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "lmPlayerId"
                    }
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmUsers",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "allow": "private",
                                "provider": "iam",
                                "operations": [
                                    "update",
                                    "create",
                                    "get"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmProfile": {
            "name": "LmProfile",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "userId": {
                    "name": "userId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "setupComplete": {
                    "name": "setupComplete",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": true,
                    "attributes": []
                },
                "showDevicePlacement": {
                    "name": "showDevicePlacement",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "videoCapture": {
                    "name": "videoCapture",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "normalized": {
                    "name": "normalized",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "normalizedElevation": {
                    "name": "normalizedElevation",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "normalizedTemperature": {
                    "name": "normalizedTemperature",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "normalizedBallType": {
                    "name": "normalizedBallType",
                    "isArray": false,
                    "type": {
                        "enum": "LmBallType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "normalizedOutdoorBallType": {
                    "name": "normalizedOutdoorBallType",
                    "isArray": false,
                    "type": {
                        "enum": "LmBallType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "distanceUnits": {
                    "name": "distanceUnits",
                    "isArray": false,
                    "type": {
                        "enum": "LmDistanceUnits"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "speedUnits": {
                    "name": "speedUnits",
                    "isArray": false,
                    "type": {
                        "enum": "LmSpeedUnits"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "apexUnits": {
                    "name": "apexUnits",
                    "isArray": false,
                    "type": {
                        "enum": "LmDistanceUnits"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "elevationUnits": {
                    "name": "elevationUnits",
                    "isArray": false,
                    "type": {
                        "enum": "LmDistanceUnits"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "temperatureUnits": {
                    "name": "temperatureUnits",
                    "isArray": false,
                    "type": {
                        "enum": "LmTemperatureUnits"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "deviceDisplayMode": {
                    "name": "deviceDisplayMode",
                    "isArray": false,
                    "type": {
                        "enum": "LmDisplayMode"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "language": {
                    "name": "language",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "displayMode": {
                    "name": "displayMode",
                    "isArray": false,
                    "type": {
                        "enum": "LmDisplayMode"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "theme": {
                    "name": "theme",
                    "isArray": false,
                    "type": {
                        "enum": "LmTheme"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "chosenPanel": {
                    "name": "chosenPanel",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "recordClubs": {
                    "name": "recordClubs",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "useTileRangeView": {
                    "name": "useTileRangeView",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "targetOption": {
                    "name": "targetOption",
                    "isArray": false,
                    "type": {
                        "enum": "LmTargetOption"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "cameraView": {
                    "name": "cameraView",
                    "isArray": false,
                    "type": {
                        "enum": "LmCameraView"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "ballPath": {
                    "name": "ballPath",
                    "isArray": false,
                    "type": {
                        "enum": "LmBallPath"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "appTiles": {
                    "name": "appTiles",
                    "isArray": true,
                    "type": {
                        "enum": "ValueType"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "watchTiles": {
                    "name": "watchTiles",
                    "isArray": true,
                    "type": {
                        "enum": "ValueType"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "deviceTiles": {
                    "name": "deviceTiles",
                    "isArray": true,
                    "type": {
                        "enum": "ValueType"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "appTilesEnabled": {
                    "name": "appTilesEnabled",
                    "isArray": true,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "watchTilesEnabled": {
                    "name": "watchTilesEnabled",
                    "isArray": true,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "baseballAppData": {
                    "name": "baseballAppData",
                    "isArray": true,
                    "type": {
                        "enum": "BaseBallValueType"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "baseballAppTilesEnabled": {
                    "name": "baseballAppTilesEnabled",
                    "isArray": true,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "baseballDeviceTiles": {
                    "name": "baseballDeviceTiles",
                    "isArray": true,
                    "type": {
                        "enum": "BaseBallValueType"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "baseballWatchTiles": {
                    "name": "baseballWatchTiles",
                    "isArray": true,
                    "type": {
                        "enum": "BaseBallValueType"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "clubSpeedAudio": {
                    "name": "clubSpeedAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "ballSpeedAudio": {
                    "name": "ballSpeedAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "smashFactorAudio": {
                    "name": "smashFactorAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "attackAngleAudio": {
                    "name": "attackAngleAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "clubPathAudio": {
                    "name": "clubPathAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "launchAngleAudio": {
                    "name": "launchAngleAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "horizontalLaunchAngleAudio": {
                    "name": "horizontalLaunchAngleAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "faceAngleAudio": {
                    "name": "faceAngleAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "spinRateAudio": {
                    "name": "spinRateAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "spinAxisAudio": {
                    "name": "spinAxisAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "carryDistanceAudio": {
                    "name": "carryDistanceAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "totalDistanceAudio": {
                    "name": "totalDistanceAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "sideAudio": {
                    "name": "sideAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "sideTotalAudio": {
                    "name": "sideTotalAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "apexAudio": {
                    "name": "apexAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "features": {
                    "name": "features",
                    "isArray": true,
                    "type": {
                        "model": "LmFeature"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "lmProfileId"
                    }
                },
                "devices": {
                    "name": "devices",
                    "isArray": true,
                    "type": {
                        "model": "LmDevice"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "lmProfileId"
                    }
                },
                "sessions": {
                    "name": "sessions",
                    "isArray": true,
                    "type": {
                        "model": "LmSession"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "profile"
                    }
                },
                "dataSessions": {
                    "name": "dataSessions",
                    "isArray": true,
                    "type": {
                        "model": "LmDataSession"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "lmProfileId"
                    }
                },
                "clubs": {
                    "name": "clubs",
                    "isArray": true,
                    "type": {
                        "model": "LmClub"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "lmProfileId"
                    }
                },
                "drills": {
                    "name": "drills",
                    "isArray": true,
                    "type": {
                        "model": "LmDrill"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "profile"
                    }
                },
                "user": {
                    "name": "user",
                    "isArray": false,
                    "type": {
                        "model": "LmUser"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "association": {
                        "connectionType": "HAS_ONE",
                        "associatedWith": "id",
                        "targetName": "userId"
                    }
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmProfiles",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUser",
                        "queryField": "profileByOwner",
                        "fields": [
                            "userId"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "allow": "private",
                                "provider": "iam",
                                "operations": [
                                    "update",
                                    "create"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmFeature": {
            "name": "LmFeature",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "lmProfileId": {
                    "name": "lmProfileId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "featureName": {
                    "name": "featureName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "enabled": {
                    "name": "enabled",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "expiresAt": {
                    "name": "expiresAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": []
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmFeatures",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byProfile",
                        "queryField": "featuresByProfile",
                        "fields": [
                            "lmProfileId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUser",
                        "queryField": "featuresByUser",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "read"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmDevice": {
            "name": "LmDevice",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "lmProfileId": {
                    "name": "lmProfileId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "deviceID": {
                    "name": "deviceID",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "advertisementName": {
                    "name": "advertisementName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "connectID": {
                    "name": "connectID",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "configuredWifiSSID": {
                    "name": "configuredWifiSSID",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "serialNumber": {
                    "name": "serialNumber",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "modelNumber": {
                    "name": "modelNumber",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "firmwareVersion": {
                    "name": "firmwareVersion",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "autoConnect": {
                    "name": "autoConnect",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "registered": {
                    "name": "registered",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "registeredUser": {
                    "name": "registeredUser",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "registrationDate": {
                    "name": "registrationDate",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "registrationReminder": {
                    "name": "registrationReminder",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "registrationReminderTime": {
                    "name": "registrationReminderTime",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmDevices",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byProfile",
                        "queryField": "devicesByProfile",
                        "fields": [
                            "lmProfileId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUser",
                        "queryField": "devicesByUser",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmUserStats": {
            "name": "LmUserStats",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "lmProfileId": {
                    "name": "lmProfileId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "sessionCount": {
                    "name": "sessionCount",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "shotCount": {
                    "name": "shotCount",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "clubCount": {
                    "name": "clubCount",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmUserStats",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byProfile",
                        "queryField": "statsByProfile",
                        "fields": [
                            "lmProfileId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUser",
                        "queryField": "statsByOwner",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "allow": "private",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmSession": {
            "name": "LmSession",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "deviceID": {
                    "name": "deviceID",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "startTimestamp": {
                    "name": "startTimestamp",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                },
                "endTimestamp": {
                    "name": "endTimestamp",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                },
                "duration": {
                    "name": "duration",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "name": {
                    "name": "name",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "city": {
                    "name": "city",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "state": {
                    "name": "state",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "country": {
                    "name": "country",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "address": {
                    "name": "address",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "courseName": {
                    "name": "courseName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "elevation": {
                    "name": "elevation",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "latitude": {
                    "name": "latitude",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "longitude": {
                    "name": "longitude",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "temperature": {
                    "name": "temperature",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "humidity": {
                    "name": "humidity",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "location": {
                    "name": "location",
                    "isArray": false,
                    "type": {
                        "enum": "LmLocation"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "normalizedElevation": {
                    "name": "normalizedElevation",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "normalizedTemperature": {
                    "name": "normalizedTemperature",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "normalizedBallType": {
                    "name": "normalizedBallType",
                    "isArray": false,
                    "type": {
                        "enum": "LmBallType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "normalized": {
                    "name": "normalized",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "sessionQuality": {
                    "name": "sessionQuality",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "drillTargets": {
                    "name": "drillTargets",
                    "isArray": true,
                    "type": {
                        "nonModel": "LmDrillTarget"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "shots": {
                    "name": "shots",
                    "isArray": true,
                    "type": {
                        "model": "LmShot"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "lmSessionId"
                    }
                },
                "profile": {
                    "name": "profile",
                    "isArray": false,
                    "type": {
                        "model": "LmProfile"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "association": {
                        "connectionType": "BELONGS_TO",
                        "targetName": "lmProfileId"
                    }
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmSessions",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byProfile",
                        "queryField": "sessionsByProfile",
                        "fields": [
                            "lmProfileId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUser",
                        "queryField": "sessionsByUser",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUserInRange",
                        "queryField": "sessionsByUserInRange",
                        "fields": [
                            "owner",
                            "startTimestamp"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmShot": {
            "name": "LmShot",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "lmSessionId": {
                    "name": "lmSessionId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "lmDrillTargetId": {
                    "name": "lmDrillTargetId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": false,
                    "attributes": []
                },
                "clubId": {
                    "name": "clubId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "clubCategory": {
                    "name": "clubCategory",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "pointId": {
                    "name": "pointId",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "timestamp": {
                    "name": "timestamp",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                },
                "isFavorite": {
                    "name": "isFavorite",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "clubSpeed": {
                    "name": "clubSpeed",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "ballSpeed": {
                    "name": "ballSpeed",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "smashFactor": {
                    "name": "smashFactor",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "attackAngle": {
                    "name": "attackAngle",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "clubPath": {
                    "name": "clubPath",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "launchAngle": {
                    "name": "launchAngle",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "horizontalLaunchAngle": {
                    "name": "horizontalLaunchAngle",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "faceAngle": {
                    "name": "faceAngle",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "spinRate": {
                    "name": "spinRate",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "spinAxis": {
                    "name": "spinAxis",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "carryDistance": {
                    "name": "carryDistance",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "totalDistance": {
                    "name": "totalDistance",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "side": {
                    "name": "side",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "sideTotal": {
                    "name": "sideTotal",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "apex": {
                    "name": "apex",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "ballDirection": {
                    "name": "ballDirection",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "ballCurve": {
                    "name": "ballCurve",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "descentAngle": {
                    "name": "descentAngle",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "dynamicLoft": {
                    "name": "dynamicLoft",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "shotQuality": {
                    "name": "shotQuality",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "targetDistance": {
                    "name": "targetDistance",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "distanceToPin": {
                    "name": "distanceToPin",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "impactKey": {
                    "name": "impactKey",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "videoKey": {
                    "name": "videoKey",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "pointCloudKey": {
                    "name": "pointCloudKey",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "protobufKey": {
                    "name": "protobufKey",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "clubSpeedValid": {
                    "name": "clubSpeedValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "ballSpeedValid": {
                    "name": "ballSpeedValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "smashFactorValid": {
                    "name": "smashFactorValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "attackAngleValid": {
                    "name": "attackAngleValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "clubPathValid": {
                    "name": "clubPathValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "launchAngleValid": {
                    "name": "launchAngleValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "horizontalLaunchAngleValid": {
                    "name": "horizontalLaunchAngleValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "faceAngleValid": {
                    "name": "faceAngleValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "spinRateValid": {
                    "name": "spinRateValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "spinAxisValid": {
                    "name": "spinAxisValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "carryDistanceValid": {
                    "name": "carryDistanceValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "totalDistanceValid": {
                    "name": "totalDistanceValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "sideValid": {
                    "name": "sideValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "sideTotalValid": {
                    "name": "sideTotalValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "apexValid": {
                    "name": "apexValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "descentAngleValid": {
                    "name": "descentAngleValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "dynamicLoftValid": {
                    "name": "dynamicLoftValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "xFit": {
                    "name": "xFit",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "yFit": {
                    "name": "yFit",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "zFit": {
                    "name": "zFit",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "club": {
                    "name": "club",
                    "isArray": false,
                    "type": {
                        "model": "LmClub"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "association": {
                        "connectionType": "HAS_ONE",
                        "associatedWith": "id",
                        "targetName": "clubId"
                    }
                },
                "normalizedValues": {
                    "name": "normalizedValues",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmShotNormalizedValues"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmShots",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "bySession",
                        "queryField": "shotsBySession",
                        "fields": [
                            "lmSessionId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "bySessionInRange",
                        "queryField": "shotsBySessionInRange",
                        "fields": [
                            "lmSessionId",
                            "timestamp"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byDrillTarget",
                        "queryField": "shotsByDrillTarget",
                        "fields": [
                            "lmDrillTargetId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byDrillTargetInRange",
                        "queryField": "shotsByDrillTargetInRange",
                        "fields": [
                            "lmDrillTargetId",
                            "timestamp"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byClub",
                        "queryField": "shotsByClub",
                        "fields": [
                            "clubId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byClubInRange",
                        "queryField": "shotsByClubInRange",
                        "fields": [
                            "clubId",
                            "timestamp"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUser",
                        "queryField": "shotsByUser",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUserInRange",
                        "queryField": "shotsByUserInRange",
                        "fields": [
                            "owner",
                            "timestamp"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byClubCategoryInRange",
                        "queryField": "shotsByClubCategoryInRange",
                        "fields": [
                            "clubCategory",
                            "timestamp"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmClub": {
            "name": "LmClub",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "lmProfileId": {
                    "name": "lmProfileId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "type": {
                    "name": "type",
                    "isArray": false,
                    "type": {
                        "enum": "LmClubType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "name": {
                    "name": "name",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "brand": {
                    "name": "brand",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "model": {
                    "name": "model",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "shaft": {
                    "name": "shaft",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "color": {
                    "name": "color",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "listOrder": {
                    "name": "listOrder",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "isActive": {
                    "name": "isActive",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmClubs",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byProfile",
                        "queryField": "clubsByProfile",
                        "fields": [
                            "lmProfileId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUser",
                        "queryField": "clubsByOwner",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmDrillTemplate": {
            "name": "LmDrillTemplate",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "type": {
                    "name": "type",
                    "isArray": false,
                    "type": {
                        "enum": "LmDrillTargetType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "name": {
                    "name": "name",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "ownerName": {
                    "name": "ownerName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "logoUrl": {
                    "name": "logoUrl",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "targets": {
                    "name": "targets",
                    "isArray": true,
                    "type": {
                        "nonModel": "LmDrillTarget"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmDrillTemplates",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byType",
                        "queryField": "drillTemplatesByType",
                        "fields": [
                            "type"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byName",
                        "queryField": "drillTemplatesByName",
                        "fields": [
                            "name"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byNameAndOwner",
                        "queryField": "drillTemplatesByNameAndOwner",
                        "fields": [
                            "name",
                            "ownerName"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "allow": "private",
                                "operations": [
                                    "read"
                                ]
                            },
                            {
                                "allow": "public",
                                "operations": [
                                    "get"
                                ],
                                "provider": "iam"
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmDrill": {
            "name": "LmDrill",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "templateId": {
                    "name": "templateId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": false,
                    "attributes": []
                },
                "type": {
                    "name": "type",
                    "isArray": false,
                    "type": {
                        "enum": "LmDrillTargetType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "name": {
                    "name": "name",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "logoUrl": {
                    "name": "logoUrl",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "targets": {
                    "name": "targets",
                    "isArray": true,
                    "type": {
                        "nonModel": "LmDrillTarget"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "profile": {
                    "name": "profile",
                    "isArray": false,
                    "type": {
                        "model": "LmProfile"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "association": {
                        "connectionType": "BELONGS_TO",
                        "targetName": "lmProfileId"
                    }
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmDrills",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUser",
                        "queryField": "drillsByUser",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byProfile",
                        "queryField": "drillsByProfile",
                        "fields": [
                            "lmProfileId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byTemplate",
                        "queryField": "drillsByTempate",
                        "fields": [
                            "templateId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byType",
                        "queryField": "drillsByType",
                        "fields": [
                            "type"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmSessionStats": {
            "name": "LmSessionStats",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "lmSessionId": {
                    "name": "lmSessionId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "shotCount": {
                    "name": "shotCount",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "values": {
                    "name": "values",
                    "isArray": true,
                    "type": {
                        "nonModel": "ShotValues"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "averages": {
                    "name": "averages",
                    "isArray": true,
                    "type": {
                        "nonModel": "ShotAverage"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmSessionStats",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "bySession",
                        "queryField": "statsBySession",
                        "fields": [
                            "lmSessionId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUser",
                        "queryField": "sessionStatsByOwner",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmShareSession": {
            "name": "LmShareSession",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "shareUrl": {
                    "name": "shareUrl",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "lmSessionId": {
                    "name": "lmSessionId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "shareVideo": {
                    "name": "shareVideo",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "expiresAt": {
                    "name": "expiresAt",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "session": {
                    "name": "session",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmSessionData"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmShareSessions",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "shareUrl"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "bySession",
                        "queryField": "shareBySession",
                        "fields": [
                            "lmSessionId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUser",
                        "queryField": "shareByUser",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "allow": "public",
                                "operations": [
                                    "get"
                                ],
                                "provider": "iam"
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmDataResults": {
            "name": "LmDataResults",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "resultId": {
                    "name": "resultId",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "sequenceNumber": {
                    "name": "sequenceNumber",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "lmSessionId": {
                    "name": "lmSessionId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "sport": {
                    "name": "sport",
                    "isArray": false,
                    "type": {
                        "enum": "LmSportType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "readers": {
                    "name": "readers",
                    "isArray": true,
                    "type": "String",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "timestamp": {
                    "name": "timestamp",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                },
                "isFavorite": {
                    "name": "isFavorite",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "baseballResults": {
                    "name": "baseballResults",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmBaseballResults"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "videoKey": {
                    "name": "videoKey",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "pointCloudKey": {
                    "name": "pointCloudKey",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmDataResults",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "bySession",
                        "queryField": "dataResultsBySession",
                        "fields": [
                            "lmSessionId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "bySessionInRange",
                        "queryField": "dataResultsBySessionInRange",
                        "fields": [
                            "lmSessionId",
                            "timestamp"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUser",
                        "queryField": "dataResultsByUser",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUserInRange",
                        "queryField": "dataResultsByUserInRange",
                        "fields": [
                            "owner",
                            "timestamp"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "bySport",
                        "queryField": "dataResultsBySport",
                        "fields": [
                            "sport"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "readers",
                                "allow": "owner",
                                "operations": [
                                    "read"
                                ],
                                "identityClaim": "cognito:username"
                            },
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmDataSession": {
            "name": "LmDataSession",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "deviceID": {
                    "name": "deviceID",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "lmProfileId": {
                    "name": "lmProfileId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "sport": {
                    "name": "sport",
                    "isArray": false,
                    "type": {
                        "enum": "LmSportType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "startTimestamp": {
                    "name": "startTimestamp",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                },
                "endTimestamp": {
                    "name": "endTimestamp",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                },
                "duration": {
                    "name": "duration",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "readers": {
                    "name": "readers",
                    "isArray": true,
                    "type": "String",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "name": {
                    "name": "name",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "details": {
                    "name": "details",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "city": {
                    "name": "city",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "state": {
                    "name": "state",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "country": {
                    "name": "country",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "address": {
                    "name": "address",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "locationName": {
                    "name": "locationName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "elevation": {
                    "name": "elevation",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "latitude": {
                    "name": "latitude",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "longitude": {
                    "name": "longitude",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "temperature": {
                    "name": "temperature",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "humidity": {
                    "name": "humidity",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "location": {
                    "name": "location",
                    "isArray": false,
                    "type": {
                        "enum": "LmLocation"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "playMode": {
                    "name": "playMode",
                    "isArray": false,
                    "type": {
                        "enum": "LmDataSessionPlayMode"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "results": {
                    "name": "results",
                    "isArray": true,
                    "type": {
                        "model": "LmDataResults"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "lmSessionId"
                    }
                },
                "team": {
                    "name": "team",
                    "isArray": false,
                    "type": {
                        "model": "LmTeam"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "association": {
                        "connectionType": "BELONGS_TO",
                        "targetName": "lmTeamId"
                    }
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmDataSessions",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byProfile",
                        "queryField": "dataSessionsByProfile",
                        "fields": [
                            "lmProfileId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byTeam",
                        "queryField": "dataSessionsByTeam",
                        "fields": [
                            "lmTeamId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUser",
                        "queryField": "sportSessionsByUser",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUserInRange",
                        "queryField": "dataSessionsByUserInRange",
                        "fields": [
                            "owner",
                            "startTimestamp"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "bySport",
                        "queryField": "dataSessionBySport",
                        "fields": [
                            "sport"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "readers",
                                "allow": "owner",
                                "operations": [
                                    "read"
                                ],
                                "identityClaim": "cognito:username"
                            },
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        }
    },
    "enums": {
        "LmInviteCodeType": {
            "name": "LmInviteCodeType",
            "values": [
                "ShareInvitation",
                "AddPlayerInvitation",
                "AddPlayerInvitationWithoutTeam"
            ]
        },
        "LmCoachPlayersTeamAssigned": {
            "name": "LmCoachPlayersTeamAssigned",
            "values": [
                "Y",
                "N"
            ]
        },
        "LmTeamPlayerStatus": {
            "name": "LmTeamPlayerStatus",
            "values": [
                "Pending",
                "Accepted"
            ]
        },
        "CompetitiveLevel": {
            "name": "CompetitiveLevel",
            "values": [
                "YouthLessThan13",
                "HighSchool",
                "College",
                "Professional",
                "Amateur"
            ]
        },
        "LmDistanceUnits": {
            "name": "LmDistanceUnits",
            "values": [
                "Feet",
                "Yards",
                "Meters"
            ]
        },
        "LmSpeedUnits": {
            "name": "LmSpeedUnits",
            "values": [
                "Mph",
                "Kph",
                "Mps"
            ]
        },
        "LmTemperatureUnits": {
            "name": "LmTemperatureUnits",
            "values": [
                "Fahrenheit",
                "Celcius"
            ]
        },
        "LmDisplayMode": {
            "name": "LmDisplayMode",
            "values": [
                "Graphic",
                "Tiles",
                "MultiData",
                "SingleData"
            ]
        },
        "LmTheme": {
            "name": "LmTheme",
            "values": [
                "Light",
                "Dark"
            ]
        },
        "LmTargetOption": {
            "name": "LmTargetOption",
            "values": [
                "None",
                "Basket",
                "Green",
                "Circle"
            ]
        },
        "LmCameraView": {
            "name": "LmCameraView",
            "values": [
                "Stationary",
                "Ball",
                "Flight"
            ]
        },
        "LmBallPath": {
            "name": "LmBallPath",
            "values": [
                "Simulated",
                "Raw"
            ]
        },
        "LmBallType": {
            "name": "LmBallType",
            "values": [
                "Premium",
                "TitleistRct"
            ]
        },
        "LmHandedness": {
            "name": "LmHandedness",
            "values": [
                "Left",
                "Right"
            ]
        },
        "DataType": {
            "name": "DataType",
            "values": [
                "Int",
                "Float"
            ]
        },
        "LmLocation": {
            "name": "LmLocation",
            "values": [
                "Net",
                "Screen",
                "Simulator",
                "OutdoorRange",
                "IndoorRange",
                "Course",
                "Field"
            ]
        },
        "ValueType": {
            "name": "ValueType",
            "values": [
                "ClubSpeed",
                "BallSpeed",
                "SmashFactor",
                "AttackAngle",
                "ClubPath",
                "LaunchAngle",
                "HorizontalLaunchAngle",
                "FaceAngle",
                "FaceToPath",
                "SpinRate",
                "SpinAxis",
                "CarryDistance",
                "TotalDistance",
                "Side",
                "SideTotal",
                "Apex",
                "BallDirection",
                "BallCurve",
                "DescentAngle"
            ]
        },
        "BaseBallValueType": {
            "name": "BaseBallValueType",
            "values": [
                "PitchSpin",
                "PitchSpeed",
                "ExitVelocity",
                "LaunchAngle",
                "HorizontalLaunchAngle",
                "Distance",
                "Apex",
                "BatSpeed"
            ]
        },
        "BaseballValueType": {
            "name": "BaseballValueType",
            "values": [
                "pitchReleaseTimestamp",
                "pitchReleaseWorld3dPositionMeters",
                "pitchReleaseRadar3dPositionMetersDeg",
                "pitchReleaseVelocityMps",
                "pitchReleaseArmSlotDeg",
                "pitchReleaseHorizontalAngleDeg",
                "pitchReleaseVerticalAngleDeg",
                "pitchReleaseBackwardExtensionMeters",
                "pitchBreakHorizontalMeters",
                "pitchBreakVerticalMeters",
                "pitchBreakInducedVerticalMeters",
                "pitchSpinTotalRpm",
                "pitchSpinActiveRpm",
                "pitchSpinBackRpm",
                "pitchSpinSideRpm",
                "pitchSpinTopRpm",
                "pitchSpinDirectionClockBearing",
                "pitchSpinDirectionEfficiencyPct",
                "pitchApproachVelocityMps",
                "pitchApproachPlateTimestamp",
                "pitchApproachPlateWorld3dPositionMeters",
                "pitchCrossPlateTimestamp",
                "pitchCrossPlateWorld3dPositionMeters",
                "hitDirectionDeg",
                "hitDistanceMeters",
                "hitExitVelocityMps",
                "hitLaunchAngleDeg",
                "hitSpinTotalRpm",
                "hitSpinActiveRpm",
                "hitSpinBackRpm",
                "hitSpinSideRpm",
                "hitSpinTopRpm",
                "hitSpinDirectionClockBearing",
                "hitSpinDirectionEfficiencyPct",
                "hitBallContactPresence",
                "hitBallContactTimestamp",
                "hitBallContactWorld3dPositionMeters",
                "hitBallContactRadar3dPositionMetersDeg",
                "batSpeedMps",
                "batRotationalAccelerationGs",
                "batAttackAngleDeg",
                "batEarlyConnectionDeg",
                "batConnectionAtImpactDeg",
                "batVerticalAngleDeg",
                "batSwingStartPresence",
                "batSwingStartTimestamp",
                "batPeakHandSpeedMps",
                "teedBallLocationWorld3dPositionMeters"
            ]
        },
        "LmGolfTrajectoryType": {
            "name": "LmGolfTrajectoryType",
            "values": [
                "Flight",
                "Normalized"
            ]
        },
        "LmBaseballTrajectoryType": {
            "name": "LmBaseballTrajectoryType",
            "values": [
                "Unknown",
                "Pitch",
                "Hit"
            ]
        },
        "LmUserRole": {
            "name": "LmUserRole",
            "values": [
                "Users",
                "Coaches",
                "Admin"
            ]
        },
        "LmTeamPlayerType": {
            "name": "LmTeamPlayerType",
            "values": [
                "Batting",
                "Pitching",
                "Both"
            ]
        },
        "LmDataSessionPlayMode": {
            "name": "LmDataSessionPlayMode",
            "values": [
                "LiveAtBat",
                "BattingPract",
                "TeeMode"
            ]
        },
        "LmClubType": {
            "name": "LmClubType",
            "values": [
                "Unknown",
                "Driver",
                "Wood2",
                "Wood3",
                "Wood4",
                "Wood5",
                "Wood6",
                "Wood7",
                "Wood8",
                "Wood9",
                "Iron1",
                "Iron2",
                "Iron3",
                "Iron4",
                "Iron5",
                "Iron6",
                "Iron7",
                "Iron8",
                "Iron9",
                "PitchingWedge",
                "SandWedge",
                "LobWedge",
                "ApproachWedge",
                "GapWedge",
                "Wedge46",
                "Wedge48",
                "Wedge50",
                "Wedge52",
                "Wedge54",
                "Wedge56",
                "Wedge58",
                "Wedge60",
                "Wedge62",
                "Wedge64",
                "Putter",
                "Hybrid",
                "Hybrid1",
                "Hybrid2",
                "Hybrid3",
                "Hybrid4",
                "Hybrid5",
                "Hybrid6",
                "Hybrid7",
                "Hybrid8",
                "Hybrid9",
                "Other"
            ]
        },
        "LmClubCategory": {
            "name": "LmClubCategory",
            "values": [
                "All",
                "Drivers",
                "Woods",
                "Hybrids",
                "Irons",
                "Wedges"
            ]
        },
        "LmShotType": {
            "name": "LmShotType",
            "values": [
                "OffTee",
                "Approach",
                "AroundGreen",
                "Putt"
            ]
        },
        "LmShotShapeType": {
            "name": "LmShotShapeType",
            "values": [
                "Straight",
                "Fade",
                "Draw"
            ]
        },
        "LmShotTrajectoryType": {
            "name": "LmShotTrajectoryType",
            "values": [
                "High",
                "Normal",
                "Low"
            ]
        },
        "LmDrillTargetType": {
            "name": "LmDrillTargetType",
            "values": [
                "Distance",
                "ShotShaping",
                "UpDown"
            ]
        },
        "DateResolution": {
            "name": "DateResolution",
            "values": [
                "Day",
                "Week",
                "Month"
            ]
        },
        "LmSportType": {
            "name": "LmSportType",
            "values": [
                "Golf",
                "Baseball"
            ]
        }
    },
    "nonModels": {
        "BaseballBatDataPoints": {
            "name": "BaseballBatDataPoints",
            "fields": {
                "hitDirectionDeg": {
                    "name": "hitDirectionDeg",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitDistanceMeters": {
                    "name": "hitDistanceMeters",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitApexMeters": {
                    "name": "hitApexMeters",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitExitVelocityMps": {
                    "name": "hitExitVelocityMps",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitLaunchAngleDeg": {
                    "name": "hitLaunchAngleDeg",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitSpinTotalRpm": {
                    "name": "hitSpinTotalRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitSpinActiveRpm": {
                    "name": "hitSpinActiveRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitSpinBackRpm": {
                    "name": "hitSpinBackRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitSpinSideRpm": {
                    "name": "hitSpinSideRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitSpinTopRpm": {
                    "name": "hitSpinTopRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitSpinDirectionClockBearing": {
                    "name": "hitSpinDirectionClockBearing",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmClockBearing"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "hitSpinDirectionEfficiencyPct": {
                    "name": "hitSpinDirectionEfficiencyPct",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitBallContactPresence": {
                    "name": "hitBallContactPresence",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "hitBallContactTimestamp": {
                    "name": "hitBallContactTimestamp",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmTimestamp"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "hitBallContactWorld3dPositionMeters": {
                    "name": "hitBallContactWorld3dPositionMeters",
                    "isArray": false,
                    "type": {
                        "nonModel": "Lm3dPosition"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "hitBallContactRadar3dPositionMetersDeg": {
                    "name": "hitBallContactRadar3dPositionMetersDeg",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmSphericalPoint3d"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "batSpeedMps": {
                    "name": "batSpeedMps",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "apex": {
                    "name": "apex",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "batRotationalAccelerationGs": {
                    "name": "batRotationalAccelerationGs",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "batAttackAngleDeg": {
                    "name": "batAttackAngleDeg",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "batEarlyConnectionDeg": {
                    "name": "batEarlyConnectionDeg",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "batConnectionAtImpactDeg": {
                    "name": "batConnectionAtImpactDeg",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "batVerticalAngleDeg": {
                    "name": "batVerticalAngleDeg",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "batSwingStartPresence": {
                    "name": "batSwingStartPresence",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "batSwingStartTimestamp": {
                    "name": "batSwingStartTimestamp",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmTimestamp"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "batPeakHandSpeedMps": {
                    "name": "batPeakHandSpeedMps",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "teedBallLocationWorld3dPositionMeters": {
                    "name": "teedBallLocationWorld3dPositionMeters",
                    "isArray": false,
                    "type": {
                        "nonModel": "Lm3dPosition"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "trajectory": {
                    "name": "trajectory",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmTrajectory"
                    },
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "BaseballPitchDataPoints": {
            "name": "BaseballPitchDataPoints",
            "fields": {
                "pitchReleaseTimestamp": {
                    "name": "pitchReleaseTimestamp",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmTimestamp"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "pitchReleaseWorld3dPositionMeters": {
                    "name": "pitchReleaseWorld3dPositionMeters",
                    "isArray": false,
                    "type": {
                        "nonModel": "Lm3dPosition"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "pitchReleaseRadar3dPositionMetersDeg": {
                    "name": "pitchReleaseRadar3dPositionMetersDeg",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmSphericalPoint3d"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "pitchReleaseVelocityMps": {
                    "name": "pitchReleaseVelocityMps",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchReleaseArmSlotDeg": {
                    "name": "pitchReleaseArmSlotDeg",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchReleaseHorizontalAngleDeg": {
                    "name": "pitchReleaseHorizontalAngleDeg",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchReleaseVerticalAngleDeg": {
                    "name": "pitchReleaseVerticalAngleDeg",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchReleaseBackwardExtensionMeters": {
                    "name": "pitchReleaseBackwardExtensionMeters",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchBreakHorizontalMeters": {
                    "name": "pitchBreakHorizontalMeters",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchBreakVerticalMeters": {
                    "name": "pitchBreakVerticalMeters",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchBreakInducedVerticalMeters": {
                    "name": "pitchBreakInducedVerticalMeters",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchSpinTotalRpm": {
                    "name": "pitchSpinTotalRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchSpinActiveRpm": {
                    "name": "pitchSpinActiveRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchSpinBackRpm": {
                    "name": "pitchSpinBackRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchSpinSideRpm": {
                    "name": "pitchSpinSideRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchSpinTopRpm": {
                    "name": "pitchSpinTopRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchSpinDirectionClockBearing": {
                    "name": "pitchSpinDirectionClockBearing",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmClockBearing"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "pitchSpinDirectionEfficiencyPct": {
                    "name": "pitchSpinDirectionEfficiencyPct",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchApproachVelocityMps": {
                    "name": "pitchApproachVelocityMps",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchApproachPlateTimestamp": {
                    "name": "pitchApproachPlateTimestamp",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmTimestamp"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "pitchApproachPlateWorld3dPositionMeters": {
                    "name": "pitchApproachPlateWorld3dPositionMeters",
                    "isArray": false,
                    "type": {
                        "nonModel": "Lm3dPosition"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "pitchCrossPlateTimestamp": {
                    "name": "pitchCrossPlateTimestamp",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmTimestamp"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "pitchCrossPlateWorld3dPositionMeters": {
                    "name": "pitchCrossPlateWorld3dPositionMeters",
                    "isArray": false,
                    "type": {
                        "nonModel": "Lm3dPosition"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "trajectory": {
                    "name": "trajectory",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmTrajectory"
                    },
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmBaseballResults": {
            "name": "LmBaseballResults",
            "fields": {
                "pitcher": {
                    "name": "pitcher",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "hitter": {
                    "name": "hitter",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "pitcherName": {
                    "name": "pitcherName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "hitterName": {
                    "name": "hitterName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchData": {
                    "name": "pitchData",
                    "isArray": false,
                    "type": {
                        "nonModel": "BaseballPitchDataPoints"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "batData": {
                    "name": "batData",
                    "isArray": false,
                    "type": {
                        "nonModel": "BaseballBatDataPoints"
                    },
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmBaseballStatistics": {
            "name": "LmBaseballStatistics",
            "fields": {
                "whiffPercent": {
                    "name": "whiffPercent",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "swingAndMissPercent": {
                    "name": "swingAndMissPercent",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmTeamInviteLinkResult": {
            "name": "LmTeamInviteLinkResult",
            "fields": {
                "status": {
                    "name": "status",
                    "isArray": false,
                    "type": "String",
                    "isRequired": true,
                    "attributes": []
                },
                "link": {
                    "name": "link",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "errors": {
                    "name": "errors",
                    "isArray": true,
                    "type": {
                        "nonModel": "CustomError"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                }
            }
        },
        "LmTeamPlayerDeleteResult": {
            "name": "LmTeamPlayerDeleteResult",
            "fields": {
                "status": {
                    "name": "status",
                    "isArray": false,
                    "type": "String",
                    "isRequired": true,
                    "attributes": []
                },
                "teamAssigned": {
                    "name": "teamAssigned",
                    "isArray": false,
                    "type": {
                        "enum": "LmCoachPlayersTeamAssigned"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "errors": {
                    "name": "errors",
                    "isArray": true,
                    "type": {
                        "nonModel": "CustomError"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                }
            }
        },
        "LmTeamPlayerAddResult": {
            "name": "LmTeamPlayerAddResult",
            "fields": {
                "status": {
                    "name": "status",
                    "isArray": false,
                    "type": "String",
                    "isRequired": true,
                    "attributes": []
                },
                "isExist": {
                    "name": "isExist",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "teamPlayer": {
                    "name": "teamPlayer",
                    "isArray": false,
                    "type": {
                        "model": "LmTeamPlayers"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "coachPlayer": {
                    "name": "coachPlayer",
                    "isArray": false,
                    "type": {
                        "model": "LmCoachPlayers"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "errors": {
                    "name": "errors",
                    "isArray": true,
                    "type": {
                        "nonModel": "CustomError"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                }
            }
        },
        "LmTeamDeleteResult": {
            "name": "LmTeamDeleteResult",
            "fields": {
                "status": {
                    "name": "status",
                    "isArray": false,
                    "type": "String",
                    "isRequired": true,
                    "attributes": []
                },
                "teamDeleted": {
                    "name": "teamDeleted",
                    "isArray": false,
                    "type": {
                        "model": "LmTeam"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "teamPlayersDeleted": {
                    "name": "teamPlayersDeleted",
                    "isArray": true,
                    "type": {
                        "model": "LmTeamPlayers"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "coachPlayersNotAssigned": {
                    "name": "coachPlayersNotAssigned",
                    "isArray": true,
                    "type": {
                        "model": "LmCoachPlayers"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "errors": {
                    "name": "errors",
                    "isArray": true,
                    "type": {
                        "nonModel": "CustomError"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                }
            }
        },
        "LmClockBearing": {
            "name": "LmClockBearing",
            "fields": {
                "hours": {
                    "name": "hours",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "minutes": {
                    "name": "minutes",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmTimestamp": {
            "name": "LmTimestamp",
            "fields": {
                "seconds": {
                    "name": "seconds",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "nanos": {
                    "name": "nanos",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "Lm2dPosition": {
            "name": "Lm2dPosition",
            "fields": {
                "x_pos": {
                    "name": "x_pos",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "y_pos": {
                    "name": "y_pos",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "Lm3dPosition": {
            "name": "Lm3dPosition",
            "fields": {
                "x_pos": {
                    "name": "x_pos",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "y_pos": {
                    "name": "y_pos",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "z_pos": {
                    "name": "z_pos",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmSphericalPoint3d": {
            "name": "LmSphericalPoint3d",
            "fields": {
                "r": {
                    "name": "r",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "theta": {
                    "name": "theta",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "phi": {
                    "name": "phi",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmOrientation3d": {
            "name": "LmOrientation3d",
            "fields": {
                "pitch": {
                    "name": "pitch",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "yaw": {
                    "name": "yaw",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "roll": {
                    "name": "roll",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmPolynomial": {
            "name": "LmPolynomial",
            "fields": {
                "x_coefficients": {
                    "name": "x_coefficients",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "y_coefficients": {
                    "name": "y_coefficients",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "z_coefficients": {
                    "name": "z_coefficients",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                }
            }
        },
        "LmTrajectory": {
            "name": "LmTrajectory",
            "fields": {
                "baseballType": {
                    "name": "baseballType",
                    "isArray": false,
                    "type": {
                        "enum": "LmBaseballTrajectoryType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "golfType": {
                    "name": "golfType",
                    "isArray": false,
                    "type": {
                        "enum": "LmGolfTrajectoryType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "polynomial": {
                    "name": "polynomial",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmPolynomial"
                    },
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmShotNormalizedValues": {
            "name": "LmShotNormalizedValues",
            "fields": {
                "carryDistance": {
                    "name": "carryDistance",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "totalDistance": {
                    "name": "totalDistance",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "side": {
                    "name": "side",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "sideTotal": {
                    "name": "sideTotal",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "apex": {
                    "name": "apex",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "distanceToPin": {
                    "name": "distanceToPin",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmDrillTarget": {
            "name": "LmDrillTarget",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": false,
                    "attributes": []
                },
                "type": {
                    "name": "type",
                    "isArray": false,
                    "type": {
                        "enum": "LmDrillTargetType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "targetQuality": {
                    "name": "targetQuality",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "shotCount": {
                    "name": "shotCount",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "shotType": {
                    "name": "shotType",
                    "isArray": false,
                    "type": {
                        "enum": "LmShotType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "clubCategory": {
                    "name": "clubCategory",
                    "isArray": false,
                    "type": {
                        "enum": "LmClubCategory"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "distance": {
                    "name": "distance",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmDistanceTarget"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "shape": {
                    "name": "shape",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmShotShapeTarget"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "upDown": {
                    "name": "upDown",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmUpDownTarget"
                    },
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmDistanceRange": {
            "name": "LmDistanceRange",
            "fields": {
                "min": {
                    "name": "min",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "max": {
                    "name": "max",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmDistanceTarget": {
            "name": "LmDistanceTarget",
            "fields": {
                "maxDistance": {
                    "name": "maxDistance",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "distance": {
                    "name": "distance",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "range": {
                    "name": "range",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmDistanceRange"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "fairwayWidth": {
                    "name": "fairwayWidth",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "targetWidth": {
                    "name": "targetWidth",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmShotShapeTarget": {
            "name": "LmShotShapeTarget",
            "fields": {
                "shape": {
                    "name": "shape",
                    "isArray": false,
                    "type": {
                        "enum": "LmShotShapeType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "trajectory": {
                    "name": "trajectory",
                    "isArray": false,
                    "type": {
                        "enum": "LmShotTrajectoryType"
                    },
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmUpDownTarget": {
            "name": "LmUpDownTarget",
            "fields": {
                "numBalls": {
                    "name": "numBalls",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "swingCount": {
                    "name": "swingCount",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "ShotValues": {
            "name": "ShotValues",
            "fields": {
                "clubId": {
                    "name": "clubId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "clubSpeed": {
                    "name": "clubSpeed",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "ballSpeed": {
                    "name": "ballSpeed",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "smashFactor": {
                    "name": "smashFactor",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "attackAngle": {
                    "name": "attackAngle",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "clubPath": {
                    "name": "clubPath",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "launchAngle": {
                    "name": "launchAngle",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "horizontalLaunchAngle": {
                    "name": "horizontalLaunchAngle",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "faceAngle": {
                    "name": "faceAngle",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "spinRate": {
                    "name": "spinRate",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "spinAxis": {
                    "name": "spinAxis",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "carryDistance": {
                    "name": "carryDistance",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "totalDistance": {
                    "name": "totalDistance",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "side": {
                    "name": "side",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "sideTotal": {
                    "name": "sideTotal",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "apex": {
                    "name": "apex",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "ballDirection": {
                    "name": "ballDirection",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "ballCurve": {
                    "name": "ballCurve",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "descentAngle": {
                    "name": "descentAngle",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "dynamicLoft": {
                    "name": "dynamicLoft",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "shotQuality": {
                    "name": "shotQuality",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "normalizedCarryDistance": {
                    "name": "normalizedCarryDistance",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "normalizedTotalDistance": {
                    "name": "normalizedTotalDistance",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "normalizedSide": {
                    "name": "normalizedSide",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "normalizedSideTotal": {
                    "name": "normalizedSideTotal",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "normalizedApex": {
                    "name": "normalizedApex",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                }
            }
        },
        "ShotAverage": {
            "name": "ShotAverage",
            "fields": {
                "clubId": {
                    "name": "clubId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "clubSpeed": {
                    "name": "clubSpeed",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "ballSpeed": {
                    "name": "ballSpeed",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "smashFactor": {
                    "name": "smashFactor",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "attackAngle": {
                    "name": "attackAngle",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "clubPath": {
                    "name": "clubPath",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "launchAngle": {
                    "name": "launchAngle",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "horizontalLaunchAngle": {
                    "name": "horizontalLaunchAngle",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "faceAngle": {
                    "name": "faceAngle",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "spinRate": {
                    "name": "spinRate",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "spinAxis": {
                    "name": "spinAxis",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "carryDistance": {
                    "name": "carryDistance",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "totalDistance": {
                    "name": "totalDistance",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "side": {
                    "name": "side",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "sideTotal": {
                    "name": "sideTotal",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "apex": {
                    "name": "apex",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "ballDirection": {
                    "name": "ballDirection",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "ballCurve": {
                    "name": "ballCurve",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "descentAngle": {
                    "name": "descentAngle",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "dynamicLoft": {
                    "name": "dynamicLoft",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "normalizedCarryDistance": {
                    "name": "normalizedCarryDistance",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "normalizedTotalDistance": {
                    "name": "normalizedTotalDistance",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "normalizedSide": {
                    "name": "normalizedSide",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "normalizedSideTotal": {
                    "name": "normalizedSideTotal",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "normalizedApex": {
                    "name": "normalizedApex",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmSessionData": {
            "name": "LmSessionData",
            "fields": {
                "startTimestamp": {
                    "name": "startTimestamp",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "endTimestamp": {
                    "name": "endTimestamp",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "duration": {
                    "name": "duration",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "name": {
                    "name": "name",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "address": {
                    "name": "address",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "elevation": {
                    "name": "elevation",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "temperature": {
                    "name": "temperature",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "humidity": {
                    "name": "humidity",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "location": {
                    "name": "location",
                    "isArray": false,
                    "type": {
                        "enum": "LmLocation"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "normalizedElevation": {
                    "name": "normalizedElevation",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "normalizedTemperature": {
                    "name": "normalizedTemperature",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "normalizedBallType": {
                    "name": "normalizedBallType",
                    "isArray": false,
                    "type": {
                        "enum": "LmBallType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "shots": {
                    "name": "shots",
                    "isArray": true,
                    "type": {
                        "nonModel": "LmShotData"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "sessionQuality": {
                    "name": "sessionQuality",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "drillTargets": {
                    "name": "drillTargets",
                    "isArray": true,
                    "type": {
                        "nonModel": "LmDrillTarget"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                }
            }
        },
        "LmShotData": {
            "name": "LmShotData",
            "fields": {
                "pointId": {
                    "name": "pointId",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "lmDrillTargetId": {
                    "name": "lmDrillTargetId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": false,
                    "attributes": []
                },
                "clubId": {
                    "name": "clubId",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "clubColor": {
                    "name": "clubColor",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "clubCategory": {
                    "name": "clubCategory",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "clubName": {
                    "name": "clubName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "clubType": {
                    "name": "clubType",
                    "isArray": false,
                    "type": {
                        "enum": "LmClubType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "timestamp": {
                    "name": "timestamp",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "isFavorite": {
                    "name": "isFavorite",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "impactUrl": {
                    "name": "impactUrl",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "videoUrl": {
                    "name": "videoUrl",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "clubSpeed": {
                    "name": "clubSpeed",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "ballSpeed": {
                    "name": "ballSpeed",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "smashFactor": {
                    "name": "smashFactor",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "attackAngle": {
                    "name": "attackAngle",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "clubPath": {
                    "name": "clubPath",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "launchAngle": {
                    "name": "launchAngle",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "horizontalLaunchAngle": {
                    "name": "horizontalLaunchAngle",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "faceAngle": {
                    "name": "faceAngle",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "spinRate": {
                    "name": "spinRate",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "spinAxis": {
                    "name": "spinAxis",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "carryDistance": {
                    "name": "carryDistance",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "totalDistance": {
                    "name": "totalDistance",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "side": {
                    "name": "side",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "sideTotal": {
                    "name": "sideTotal",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "apex": {
                    "name": "apex",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "descentAngle": {
                    "name": "descentAngle",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "dynamicLoft": {
                    "name": "dynamicLoft",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "clubSpeedValid": {
                    "name": "clubSpeedValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "ballSpeedValid": {
                    "name": "ballSpeedValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "smashFactorValid": {
                    "name": "smashFactorValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "attackAngleValid": {
                    "name": "attackAngleValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "clubPathValid": {
                    "name": "clubPathValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "launchAngleValid": {
                    "name": "launchAngleValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "horizontalLaunchAngleValid": {
                    "name": "horizontalLaunchAngleValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "faceAngleValid": {
                    "name": "faceAngleValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "spinRateValid": {
                    "name": "spinRateValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "spinAxisValid": {
                    "name": "spinAxisValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "carryDistanceValid": {
                    "name": "carryDistanceValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "totalDistanceValid": {
                    "name": "totalDistanceValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "sideValid": {
                    "name": "sideValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "sideTotalValid": {
                    "name": "sideTotalValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "apexValid": {
                    "name": "apexValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "descentAngleValid": {
                    "name": "descentAngleValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "dynamicLoftValid": {
                    "name": "dynamicLoftValid",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "xFit": {
                    "name": "xFit",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "yFit": {
                    "name": "yFit",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "zFit": {
                    "name": "zFit",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "normalizedValues": {
                    "name": "normalizedValues",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmShotNormalizedValues"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "shotQuality": {
                    "name": "shotQuality",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "targetDistance": {
                    "name": "targetDistance",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "distanceToPin": {
                    "name": "distanceToPin",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "RangeCount": {
            "name": "RangeCount",
            "fields": {
                "range": {
                    "name": "range",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                },
                "count": {
                    "name": "count",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                }
            }
        },
        "TimeWindowCount": {
            "name": "TimeWindowCount",
            "fields": {
                "start": {
                    "name": "start",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                },
                "end": {
                    "name": "end",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                },
                "count": {
                    "name": "count",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                }
            }
        },
        "ClubCount": {
            "name": "ClubCount",
            "fields": {
                "clubId": {
                    "name": "clubId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "count": {
                    "name": "count",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                }
            }
        },
        "ClubAverage": {
            "name": "ClubAverage",
            "fields": {
                "clubId": {
                    "name": "clubId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "average": {
                    "name": "average",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": true,
                    "attributes": []
                }
            }
        },
        "RegisterShadow": {
            "name": "RegisterShadow",
            "fields": {
                "regUser": {
                    "name": "regUser",
                    "isArray": false,
                    "type": "String",
                    "isRequired": true,
                    "attributes": []
                },
                "prodRegDate": {
                    "name": "prodRegDate",
                    "isArray": false,
                    "type": "String",
                    "isRequired": true,
                    "attributes": []
                }
            }
        },
        "GraphQLResult": {
            "name": "GraphQLResult",
            "fields": {
                "status": {
                    "name": "status",
                    "isArray": false,
                    "type": "String",
                    "isRequired": true,
                    "attributes": []
                }
            }
        },
        "CustomError": {
            "name": "CustomError",
            "fields": {
                "errorType": {
                    "name": "errorType",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "message": {
                    "name": "message",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "GraphQLResultAuth": {
            "name": "GraphQLResultAuth",
            "fields": {
                "status": {
                    "name": "status",
                    "isArray": false,
                    "type": "String",
                    "isRequired": true,
                    "attributes": []
                },
                "errors": {
                    "name": "errors",
                    "isArray": true,
                    "type": {
                        "nonModel": "CustomError"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                }
            }
        },
        "GraphQLDeleteResultAuth": {
            "name": "GraphQLDeleteResultAuth",
            "fields": {
                "status": {
                    "name": "status",
                    "isArray": false,
                    "type": "String",
                    "isRequired": true,
                    "attributes": []
                },
                "errors": {
                    "name": "errors",
                    "isArray": true,
                    "type": {
                        "nonModel": "CustomError"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmDataSessionListResult": {
            "name": "LmDataSessionListResult",
            "fields": {
                "nextToken": {
                    "name": "nextToken",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "items": {
                    "name": "items",
                    "isArray": true,
                    "type": {
                        "model": "LmDataSession"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                }
            }
        },
        "LmDataResultsListResult": {
            "name": "LmDataResultsListResult",
            "fields": {
                "nextToken": {
                    "name": "nextToken",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "items": {
                    "name": "items",
                    "isArray": true,
                    "type": {
                        "model": "LmDataResults"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                }
            }
        }
    },
    "codegenVersion": "3.4.4",
    "version": "875c98926aac02b22c21f44ad118f1a9"
};