export const schema = {
    "models": {
        "LmUser": {
            "name": "LmUser",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "setupComplete": {
                    "name": "setupComplete",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": true,
                    "attributes": []
                },
                "email": {
                    "name": "email",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "phone": {
                    "name": "phone",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "fullName": {
                    "name": "fullName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "profileImage": {
                    "name": "profileImage",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "userType": {
                    "name": "userType",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "gender": {
                    "name": "gender",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "handedness": {
                    "name": "handedness",
                    "isArray": false,
                    "type": {
                        "enum": "LmHandedness"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "birthdate": {
                    "name": "birthdate",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "companyName": {
                    "name": "companyName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "shippingAddressLine1": {
                    "name": "shippingAddressLine1",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "shippingAddressLine2": {
                    "name": "shippingAddressLine2",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "shippingPostcode": {
                    "name": "shippingPostcode",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "shippingLocality": {
                    "name": "shippingLocality",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "shippingRegion": {
                    "name": "shippingRegion",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "shippingCountry": {
                    "name": "shippingCountry",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "subscriptions": {
                    "name": "subscriptions",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "competitiveLevel": {
                    "name": "competitiveLevel",
                    "isArray": false,
                    "type": {
                        "enum": "CompetitiveLevel"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "teamName": {
                    "name": "teamName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "organizationSchool": {
                    "name": "organizationSchool",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "baseballPlayerPosition": {
                    "name": "baseballPlayerPosition",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "coachedTeams": {
                    "name": "coachedTeams",
                    "isArray": true,
                    "type": {
                        "model": "LmTeamCoaches"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "lmCoachId"
                    }
                },
                "playerTeams": {
                    "name": "playerTeams",
                    "isArray": true,
                    "type": {
                        "model": "LmTeamPlayers"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "lmPlayerId"
                    }
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmUsers",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "allow": "private",
                                "provider": "iam",
                                "operations": [
                                    "update",
                                    "create",
                                    "get"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmProfile": {
            "name": "LmProfile",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "userId": {
                    "name": "userId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "setupComplete": {
                    "name": "setupComplete",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": true,
                    "attributes": []
                },
                "showDevicePlacement": {
                    "name": "showDevicePlacement",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "videoCapture": {
                    "name": "videoCapture",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "normalized": {
                    "name": "normalized",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "normalizedElevation": {
                    "name": "normalizedElevation",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "normalizedTemperature": {
                    "name": "normalizedTemperature",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "normalizedBallType": {
                    "name": "normalizedBallType",
                    "isArray": false,
                    "type": {
                        "enum": "LmBallType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "normalizedOutdoorBallType": {
                    "name": "normalizedOutdoorBallType",
                    "isArray": false,
                    "type": {
                        "enum": "LmBallType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "distanceUnits": {
                    "name": "distanceUnits",
                    "isArray": false,
                    "type": {
                        "enum": "LmDistanceUnits"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "speedUnits": {
                    "name": "speedUnits",
                    "isArray": false,
                    "type": {
                        "enum": "LmSpeedUnits"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "apexUnits": {
                    "name": "apexUnits",
                    "isArray": false,
                    "type": {
                        "enum": "LmDistanceUnits"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "elevationUnits": {
                    "name": "elevationUnits",
                    "isArray": false,
                    "type": {
                        "enum": "LmDistanceUnits"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "temperatureUnits": {
                    "name": "temperatureUnits",
                    "isArray": false,
                    "type": {
                        "enum": "LmTemperatureUnits"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "deviceDisplayMode": {
                    "name": "deviceDisplayMode",
                    "isArray": false,
                    "type": {
                        "enum": "LmDisplayMode"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "language": {
                    "name": "language",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "displayMode": {
                    "name": "displayMode",
                    "isArray": false,
                    "type": {
                        "enum": "LmDisplayMode"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "theme": {
                    "name": "theme",
                    "isArray": false,
                    "type": {
                        "enum": "LmTheme"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "chosenPanel": {
                    "name": "chosenPanel",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "recordClubs": {
                    "name": "recordClubs",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "useTileRangeView": {
                    "name": "useTileRangeView",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "targetOption": {
                    "name": "targetOption",
                    "isArray": false,
                    "type": {
                        "enum": "LmTargetOption"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "cameraView": {
                    "name": "cameraView",
                    "isArray": false,
                    "type": {
                        "enum": "LmCameraView"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "ballPath": {
                    "name": "ballPath",
                    "isArray": false,
                    "type": {
                        "enum": "LmBallPath"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "appTiles": {
                    "name": "appTiles",
                    "isArray": true,
                    "type": {
                        "enum": "ValueType"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "watchTiles": {
                    "name": "watchTiles",
                    "isArray": true,
                    "type": {
                        "enum": "ValueType"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "deviceTiles": {
                    "name": "deviceTiles",
                    "isArray": true,
                    "type": {
                        "enum": "ValueType"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "appTilesEnabled": {
                    "name": "appTilesEnabled",
                    "isArray": true,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "watchTilesEnabled": {
                    "name": "watchTilesEnabled",
                    "isArray": true,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "baseballAppData": {
                    "name": "baseballAppData",
                    "isArray": true,
                    "type": {
                        "enum": "BaseBallValueType"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "baseballAppTilesEnabled": {
                    "name": "baseballAppTilesEnabled",
                    "isArray": true,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "baseballDeviceTiles": {
                    "name": "baseballDeviceTiles",
                    "isArray": true,
                    "type": {
                        "enum": "BaseBallValueType"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "baseballWatchTiles": {
                    "name": "baseballWatchTiles",
                    "isArray": true,
                    "type": {
                        "enum": "BaseBallValueType"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "clubSpeedAudio": {
                    "name": "clubSpeedAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "ballSpeedAudio": {
                    "name": "ballSpeedAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "smashFactorAudio": {
                    "name": "smashFactorAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "attackAngleAudio": {
                    "name": "attackAngleAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "clubPathAudio": {
                    "name": "clubPathAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "launchAngleAudio": {
                    "name": "launchAngleAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "horizontalLaunchAngleAudio": {
                    "name": "horizontalLaunchAngleAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "faceAngleAudio": {
                    "name": "faceAngleAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "spinRateAudio": {
                    "name": "spinRateAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "spinAxisAudio": {
                    "name": "spinAxisAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "carryDistanceAudio": {
                    "name": "carryDistanceAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "totalDistanceAudio": {
                    "name": "totalDistanceAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "sideAudio": {
                    "name": "sideAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "sideTotalAudio": {
                    "name": "sideTotalAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "apexAudio": {
                    "name": "apexAudio",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "features": {
                    "name": "features",
                    "isArray": true,
                    "type": {
                        "model": "LmFeature"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "lmProfileId"
                    }
                },
                "devices": {
                    "name": "devices",
                    "isArray": true,
                    "type": {
                        "model": "LmDevice"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "lmProfileId"
                    }
                },
                "dataSessions": {
                    "name": "dataSessions",
                    "isArray": true,
                    "type": {
                        "model": "LmDataSession"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "profile"
                    }
                },
                "user": {
                    "name": "user",
                    "isArray": false,
                    "type": {
                        "model": "LmUser"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "association": {
                        "connectionType": "HAS_ONE",
                        "associatedWith": "id",
                        "targetName": "userId"
                    }
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmProfiles",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUser",
                        "queryField": "profileByOwner",
                        "fields": [
                            "userId"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "allow": "private",
                                "provider": "iam",
                                "operations": [
                                    "update",
                                    "create"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmFeature": {
            "name": "LmFeature",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "lmProfileId": {
                    "name": "lmProfileId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "featureName": {
                    "name": "featureName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "enabled": {
                    "name": "enabled",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "expiresAt": {
                    "name": "expiresAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": []
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmFeatures",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byProfile",
                        "queryField": "featuresByProfile",
                        "fields": [
                            "lmProfileId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUser",
                        "queryField": "featuresByUser",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "read"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmDevice": {
            "name": "LmDevice",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "lmProfileId": {
                    "name": "lmProfileId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "deviceID": {
                    "name": "deviceID",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "advertisementName": {
                    "name": "advertisementName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "connectID": {
                    "name": "connectID",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "configuredWifiSSID": {
                    "name": "configuredWifiSSID",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "serialNumber": {
                    "name": "serialNumber",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "modelNumber": {
                    "name": "modelNumber",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "firmwareVersion": {
                    "name": "firmwareVersion",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "autoConnect": {
                    "name": "autoConnect",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "registered": {
                    "name": "registered",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "registeredUser": {
                    "name": "registeredUser",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "registrationDate": {
                    "name": "registrationDate",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "registrationReminder": {
                    "name": "registrationReminder",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "registrationReminderTime": {
                    "name": "registrationReminderTime",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmDevices",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byProfile",
                        "queryField": "devicesByProfile",
                        "fields": [
                            "lmProfileId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUser",
                        "queryField": "devicesByUser",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmDataResults": {
            "name": "LmDataResults",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "resultId": {
                    "name": "resultId",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "sequenceNumber": {
                    "name": "sequenceNumber",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "lmSessionId": {
                    "name": "lmSessionId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "sport": {
                    "name": "sport",
                    "isArray": false,
                    "type": {
                        "enum": "LmSportType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "readers": {
                    "name": "readers",
                    "isArray": true,
                    "type": "String",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "timestamp": {
                    "name": "timestamp",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                },
                "isFavorite": {
                    "name": "isFavorite",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "baseballResults": {
                    "name": "baseballResults",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmBaseballResults"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "videoKey": {
                    "name": "videoKey",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "pointCloudKey": {
                    "name": "pointCloudKey",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmDataResults",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "bySession",
                        "queryField": "dataResultsBySession",
                        "fields": [
                            "lmSessionId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "bySessionInRange",
                        "queryField": "dataResultsBySessionInRange",
                        "fields": [
                            "lmSessionId",
                            "timestamp"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUser",
                        "queryField": "dataResultsByUser",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUserInRange",
                        "queryField": "dataResultsByUserInRange",
                        "fields": [
                            "owner",
                            "timestamp"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "bySport",
                        "queryField": "dataResultsBySport",
                        "fields": [
                            "sport"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "readers",
                                "allow": "owner",
                                "operations": [
                                    "read"
                                ],
                                "identityClaim": "cognito:username"
                            },
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "allow": "custom",
                                "operations": [
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmDataSession": {
            "name": "LmDataSession",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "deviceID": {
                    "name": "deviceID",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "sport": {
                    "name": "sport",
                    "isArray": false,
                    "type": {
                        "enum": "LmSportType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "startTimestamp": {
                    "name": "startTimestamp",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                },
                "endTimestamp": {
                    "name": "endTimestamp",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                },
                "duration": {
                    "name": "duration",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "readers": {
                    "name": "readers",
                    "isArray": true,
                    "type": "String",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "name": {
                    "name": "name",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "details": {
                    "name": "details",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "city": {
                    "name": "city",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "state": {
                    "name": "state",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "country": {
                    "name": "country",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "address": {
                    "name": "address",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "locationName": {
                    "name": "locationName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "elevation": {
                    "name": "elevation",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "latitude": {
                    "name": "latitude",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "longitude": {
                    "name": "longitude",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "temperature": {
                    "name": "temperature",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "humidity": {
                    "name": "humidity",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "location": {
                    "name": "location",
                    "isArray": false,
                    "type": {
                        "enum": "LmLocation"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "playMode": {
                    "name": "playMode",
                    "isArray": false,
                    "type": {
                        "enum": "LmDataSessionPlayMode"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "results": {
                    "name": "results",
                    "isArray": true,
                    "type": {
                        "model": "LmDataResults"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "lmSessionId"
                    }
                },
                "profile": {
                    "name": "profile",
                    "isArray": false,
                    "type": {
                        "model": "LmProfile"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "association": {
                        "connectionType": "BELONGS_TO",
                        "targetName": "lmProfileId"
                    }
                },
                "team": {
                    "name": "team",
                    "isArray": false,
                    "type": {
                        "model": "LmTeam"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "association": {
                        "connectionType": "BELONGS_TO",
                        "targetName": "lmTeamId"
                    }
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmDataSessions",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byProfile",
                        "queryField": "dataSessionsByProfile",
                        "fields": [
                            "lmProfileId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byTeam",
                        "queryField": "dataSessionsByTeam",
                        "fields": [
                            "lmTeamId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUser",
                        "queryField": "sportSessionsByUser",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byUserInRange",
                        "queryField": "dataSessionsByUserInRange",
                        "fields": [
                            "owner",
                            "startTimestamp"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "bySport",
                        "queryField": "dataSessionBySport",
                        "fields": [
                            "sport"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "readers",
                                "allow": "owner",
                                "operations": [
                                    "read"
                                ],
                                "identityClaim": "cognito:username"
                            },
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "allow": "custom",
                                "operations": [
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmTeamPlayers": {
            "name": "LmTeamPlayers",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "readers": {
                    "name": "readers",
                    "isArray": true,
                    "type": "String",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "type": {
                    "name": "type",
                    "isArray": false,
                    "type": {
                        "enum": "LmTeamPlayerType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "status": {
                    "name": "status",
                    "isArray": false,
                    "type": {
                        "enum": "LmTeamPlayerStatus"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "email": {
                    "name": "email",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "joined_at": {
                    "name": "joined_at",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": []
                },
                "lmPlayerId": {
                    "name": "lmPlayerId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": false,
                    "attributes": []
                },
                "lmPlayerName": {
                    "name": "lmPlayerName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "lineupOrderBatting": {
                    "name": "lineupOrderBatting",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "lineupOrderPitching": {
                    "name": "lineupOrderPitching",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "team": {
                    "name": "team",
                    "isArray": false,
                    "type": {
                        "model": "LmTeam"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "association": {
                        "connectionType": "BELONGS_TO",
                        "targetName": "lmTeamId"
                    }
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmTeamPlayers",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byOwner",
                        "queryField": "teamPlayersByUser",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byEmail",
                        "queryField": "teamPlayersByEmail",
                        "fields": [
                            "email"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byTeam",
                        "queryField": "teamPlayersByTeam",
                        "fields": [
                            "lmTeamId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byTeamAndJoinedAt",
                        "queryField": "teamPlayersByTeamAndJoinedAt",
                        "fields": [
                            "lmTeamId",
                            "joined_at"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byPlayer",
                        "queryField": "teamPlayersByPlayer",
                        "fields": [
                            "lmPlayerId"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "readers",
                                "allow": "owner",
                                "operations": [
                                    "read"
                                ],
                                "identityClaim": "cognito:username"
                            },
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "allow": "private",
                                "provider": "iam",
                                "operations": [
                                    "create",
                                    "update",
                                    "read",
                                    "delete"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmTeamCoaches": {
            "name": "LmTeamCoaches",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "lmTeamId": {
                    "name": "lmTeamId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": false,
                    "attributes": []
                },
                "lmCoachId": {
                    "name": "lmCoachId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": false,
                    "attributes": []
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmTeamCoaches",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byOwner",
                        "queryField": "teamCoachesByUser",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byTeam",
                        "queryField": "teamCoachesByTeam",
                        "fields": [
                            "lmTeamId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byCoach",
                        "queryField": "teamCoachesByCoach",
                        "fields": [
                            "lmCoachId"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "allow": "private",
                                "operations": [
                                    "read"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmTeam": {
            "name": "LmTeam",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "name": {
                    "name": "name",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "profileImage": {
                    "name": "profileImage",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "pitcherId": {
                    "name": "pitcherId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": false,
                    "attributes": []
                },
                "readers": {
                    "name": "readers",
                    "isArray": true,
                    "type": "String",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "players": {
                    "name": "players",
                    "isArray": true,
                    "type": {
                        "model": "LmTeamPlayers"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "team"
                    }
                },
                "coaches": {
                    "name": "coaches",
                    "isArray": true,
                    "type": {
                        "model": "LmTeamCoaches"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "lmTeamId"
                    }
                },
                "dataSessions": {
                    "name": "dataSessions",
                    "isArray": true,
                    "type": {
                        "model": "LmDataSession"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true,
                    "association": {
                        "connectionType": "HAS_MANY",
                        "associatedWith": "team"
                    }
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmTeams",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byOwner",
                        "queryField": "teamsByOwner",
                        "fields": [
                            "owner"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "readers",
                                "allow": "owner",
                                "operations": [
                                    "read"
                                ],
                                "identityClaim": "cognito:username"
                            },
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "read",
                                    "update"
                                ]
                            },
                            {
                                "allow": "private",
                                "provider": "iam",
                                "operations": [
                                    "delete",
                                    "update"
                                ]
                            },
                            {
                                "allow": "private",
                                "operations": [
                                    "get"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Coaches"
                                ],
                                "operations": [
                                    "create"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        },
        "LmCoachPlayers": {
            "name": "LmCoachPlayers",
            "fields": {
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "lmCoachId": {
                    "name": "lmCoachId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "owner": {
                    "name": "owner",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "lmPlayerId": {
                    "name": "lmPlayerId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": false,
                    "attributes": []
                },
                "lmPlayerName": {
                    "name": "lmPlayerName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "status": {
                    "name": "status",
                    "isArray": false,
                    "type": {
                        "enum": "LmTeamPlayerStatus"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "lmPlayerEmail": {
                    "name": "lmPlayerEmail",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "teamAssigned": {
                    "name": "teamAssigned",
                    "isArray": false,
                    "type": {
                        "enum": "LmCoachPlayersTeamAssigned"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "createdAt": {
                    "name": "createdAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                },
                "updatedAt": {
                    "name": "updatedAt",
                    "isArray": false,
                    "type": "AWSDateTime",
                    "isRequired": false,
                    "attributes": [],
                    "isReadOnly": true
                }
            },
            "syncable": true,
            "pluralName": "LmCoachPlayers",
            "attributes": [
                {
                    "type": "model",
                    "properties": {}
                },
                {
                    "type": "key",
                    "properties": {
                        "fields": [
                            "id"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byCoach",
                        "queryField": "coachPlayersByCoach",
                        "fields": [
                            "lmCoachId"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byLmPlayerEmail",
                        "queryField": "coachPlayersLmPlayerEmail",
                        "fields": [
                            "lmPlayerEmail"
                        ]
                    }
                },
                {
                    "type": "key",
                    "properties": {
                        "name": "byTeamAssigned",
                        "queryField": "coachPlayersByTeamAssigned",
                        "fields": [
                            "teamAssigned"
                        ]
                    }
                },
                {
                    "type": "auth",
                    "properties": {
                        "rules": [
                            {
                                "provider": "userPools",
                                "ownerField": "owner",
                                "allow": "owner",
                                "identityClaim": "cognito:username",
                                "operations": [
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            },
                            {
                                "allow": "private",
                                "provider": "iam",
                                "operations": [
                                    "create",
                                    "update",
                                    "delete"
                                ]
                            },
                            {
                                "groupClaim": "cognito:groups",
                                "provider": "userPools",
                                "allow": "groups",
                                "groups": [
                                    "Admin"
                                ],
                                "operations": [
                                    "create",
                                    "update",
                                    "delete",
                                    "read"
                                ]
                            }
                        ]
                    }
                }
            ]
        }
    },
    "enums": {
        "CompetitiveLevel": {
            "name": "CompetitiveLevel",
            "values": [
                "YouthLessThan13",
                "HighSchool",
                "College",
                "Professional",
                "Amateur"
            ]
        },
        "LmDistanceUnits": {
            "name": "LmDistanceUnits",
            "values": [
                "Feet",
                "Yards",
                "Meters"
            ]
        },
        "LmSpeedUnits": {
            "name": "LmSpeedUnits",
            "values": [
                "Mph",
                "Kph",
                "Mps"
            ]
        },
        "LmTemperatureUnits": {
            "name": "LmTemperatureUnits",
            "values": [
                "Fahrenheit",
                "Celcius"
            ]
        },
        "LmDisplayMode": {
            "name": "LmDisplayMode",
            "values": [
                "Graphic",
                "Tiles",
                "MultiData",
                "SingleData"
            ]
        },
        "LmTheme": {
            "name": "LmTheme",
            "values": [
                "Light",
                "Dark"
            ]
        },
        "LmTargetOption": {
            "name": "LmTargetOption",
            "values": [
                "None",
                "Basket",
                "Green",
                "Circle"
            ]
        },
        "LmCameraView": {
            "name": "LmCameraView",
            "values": [
                "Stationary",
                "Ball",
                "Flight"
            ]
        },
        "LmBallPath": {
            "name": "LmBallPath",
            "values": [
                "Simulated",
                "Raw"
            ]
        },
        "LmBallType": {
            "name": "LmBallType",
            "values": [
                "Premium",
                "TitleistRct"
            ]
        },
        "LmHandedness": {
            "name": "LmHandedness",
            "values": [
                "Left",
                "Right"
            ]
        },
        "DataType": {
            "name": "DataType",
            "values": [
                "Int",
                "Float"
            ]
        },
        "LmLocation": {
            "name": "LmLocation",
            "values": [
                "Net",
                "Screen",
                "Simulator",
                "OutdoorRange",
                "IndoorRange",
                "Course",
                "Field"
            ]
        },
        "ValueType": {
            "name": "ValueType",
            "values": [
                "ClubSpeed",
                "BallSpeed",
                "SmashFactor",
                "AttackAngle",
                "ClubPath",
                "LaunchAngle",
                "HorizontalLaunchAngle",
                "FaceAngle",
                "FaceToPath",
                "SpinRate",
                "SpinAxis",
                "CarryDistance",
                "TotalDistance",
                "Side",
                "SideTotal",
                "Apex",
                "BallDirection",
                "BallCurve",
                "DescentAngle"
            ]
        },
        "BaseBallValueType": {
            "name": "BaseBallValueType",
            "values": [
                "PitchSpin",
                "PitchSpeed",
                "ExitVelocity",
                "LaunchAngle",
                "HorizontalLaunchAngle",
                "Distance",
                "Apex",
                "BatSpeed"
            ]
        },
        "BaseballValueType": {
            "name": "BaseballValueType",
            "values": [
                "pitchReleaseTimestamp",
                "pitchReleaseWorld3dPositionMeters",
                "pitchReleaseRadar3dPositionMetersDeg",
                "pitchReleaseVelocityMps",
                "pitchReleaseArmSlotDeg",
                "pitchReleaseHorizontalAngleDeg",
                "pitchReleaseVerticalAngleDeg",
                "pitchReleaseBackwardExtensionMeters",
                "pitchBreakHorizontalMeters",
                "pitchBreakVerticalMeters",
                "pitchBreakInducedVerticalMeters",
                "pitchSpinTotalRpm",
                "pitchSpinActiveRpm",
                "pitchSpinBackRpm",
                "pitchSpinSideRpm",
                "pitchSpinTopRpm",
                "pitchSpinDirectionClockBearing",
                "pitchSpinDirectionEfficiencyPct",
                "pitchApproachVelocityMps",
                "pitchApproachPlateTimestamp",
                "pitchApproachPlateWorld3dPositionMeters",
                "pitchCrossPlateTimestamp",
                "pitchCrossPlateWorld3dPositionMeters",
                "hitDirectionDeg",
                "hitDistanceMeters",
                "hitExitVelocityMps",
                "hitLaunchAngleDeg",
                "hitSpinTotalRpm",
                "hitSpinActiveRpm",
                "hitSpinBackRpm",
                "hitSpinSideRpm",
                "hitSpinTopRpm",
                "hitSpinDirectionClockBearing",
                "hitSpinDirectionEfficiencyPct",
                "hitBallContactPresence",
                "hitBallContactTimestamp",
                "hitBallContactWorld3dPositionMeters",
                "hitBallContactRadar3dPositionMetersDeg",
                "batSpeedMps",
                "batRotationalAccelerationGs",
                "batAttackAngleDeg",
                "batEarlyConnectionDeg",
                "batConnectionAtImpactDeg",
                "batVerticalAngleDeg",
                "batSwingStartPresence",
                "batSwingStartTimestamp",
                "batPeakHandSpeedMps",
                "teedBallLocationWorld3dPositionMeters"
            ]
        },
        "LmGolfTrajectoryType": {
            "name": "LmGolfTrajectoryType",
            "values": [
                "Flight",
                "Normalized"
            ]
        },
        "LmBaseballTrajectoryType": {
            "name": "LmBaseballTrajectoryType",
            "values": [
                "Unknown",
                "Pitch",
                "Hit"
            ]
        },
        "LmUserRole": {
            "name": "LmUserRole",
            "values": [
                "Users",
                "Coaches",
                "Admin"
            ]
        },
        "LmTeamPlayerType": {
            "name": "LmTeamPlayerType",
            "values": [
                "Batting",
                "Pitching",
                "Both"
            ]
        },
        "LmDataSessionPlayMode": {
            "name": "LmDataSessionPlayMode",
            "values": [
                "LiveAtBat",
                "BattingPract",
                "TeeMode"
            ]
        },
        "LmSportType": {
            "name": "LmSportType",
            "values": [
                "Golf",
                "Baseball"
            ]
        },
        "LmCoachPlayersTeamAssigned": {
            "name": "LmCoachPlayersTeamAssigned",
            "values": [
                "Y",
                "N"
            ]
        },
        "LmTeamPlayerStatus": {
            "name": "LmTeamPlayerStatus",
            "values": [
                "Pending",
                "Accepted"
            ]
        },
        "DateResolution": {
            "name": "DateResolution",
            "values": [
                "Day",
                "Week",
                "Month"
            ]
        }
    },
    "nonModels": {
        "LmClockBearing": {
            "name": "LmClockBearing",
            "fields": {
                "hours": {
                    "name": "hours",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "minutes": {
                    "name": "minutes",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmTimestamp": {
            "name": "LmTimestamp",
            "fields": {
                "seconds": {
                    "name": "seconds",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                },
                "nanos": {
                    "name": "nanos",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "Lm2dPosition": {
            "name": "Lm2dPosition",
            "fields": {
                "x_pos": {
                    "name": "x_pos",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "y_pos": {
                    "name": "y_pos",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "Lm3dPosition": {
            "name": "Lm3dPosition",
            "fields": {
                "x_pos": {
                    "name": "x_pos",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "y_pos": {
                    "name": "y_pos",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "z_pos": {
                    "name": "z_pos",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmSphericalPoint3d": {
            "name": "LmSphericalPoint3d",
            "fields": {
                "r": {
                    "name": "r",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "theta": {
                    "name": "theta",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "phi": {
                    "name": "phi",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmOrientation3d": {
            "name": "LmOrientation3d",
            "fields": {
                "pitch": {
                    "name": "pitch",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "yaw": {
                    "name": "yaw",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "roll": {
                    "name": "roll",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmPolynomial": {
            "name": "LmPolynomial",
            "fields": {
                "x_coefficients": {
                    "name": "x_coefficients",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "y_coefficients": {
                    "name": "y_coefficients",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "z_coefficients": {
                    "name": "z_coefficients",
                    "isArray": true,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                }
            }
        },
        "LmTrajectory": {
            "name": "LmTrajectory",
            "fields": {
                "baseballType": {
                    "name": "baseballType",
                    "isArray": false,
                    "type": {
                        "enum": "LmBaseballTrajectoryType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "golfType": {
                    "name": "golfType",
                    "isArray": false,
                    "type": {
                        "enum": "LmGolfTrajectoryType"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "polynomial": {
                    "name": "polynomial",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmPolynomial"
                    },
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "BaseballBatDataPoints": {
            "name": "BaseballBatDataPoints",
            "fields": {
                "hitDirectionDeg": {
                    "name": "hitDirectionDeg",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitDistanceMeters": {
                    "name": "hitDistanceMeters",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitApexMeters": {
                    "name": "hitApexMeters",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitExitVelocityMps": {
                    "name": "hitExitVelocityMps",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitLaunchAngleDeg": {
                    "name": "hitLaunchAngleDeg",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitSpinTotalRpm": {
                    "name": "hitSpinTotalRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitSpinActiveRpm": {
                    "name": "hitSpinActiveRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitSpinBackRpm": {
                    "name": "hitSpinBackRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitSpinSideRpm": {
                    "name": "hitSpinSideRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitSpinTopRpm": {
                    "name": "hitSpinTopRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitSpinDirectionClockBearing": {
                    "name": "hitSpinDirectionClockBearing",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmClockBearing"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "hitSpinDirectionEfficiencyPct": {
                    "name": "hitSpinDirectionEfficiencyPct",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "hitBallContactPresence": {
                    "name": "hitBallContactPresence",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "hitBallContactTimestamp": {
                    "name": "hitBallContactTimestamp",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmTimestamp"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "hitBallContactWorld3dPositionMeters": {
                    "name": "hitBallContactWorld3dPositionMeters",
                    "isArray": false,
                    "type": {
                        "nonModel": "Lm3dPosition"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "hitBallContactRadar3dPositionMetersDeg": {
                    "name": "hitBallContactRadar3dPositionMetersDeg",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmSphericalPoint3d"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "batSpeedMps": {
                    "name": "batSpeedMps",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "apex": {
                    "name": "apex",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "batRotationalAccelerationGs": {
                    "name": "batRotationalAccelerationGs",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "batAttackAngleDeg": {
                    "name": "batAttackAngleDeg",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "batEarlyConnectionDeg": {
                    "name": "batEarlyConnectionDeg",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "batConnectionAtImpactDeg": {
                    "name": "batConnectionAtImpactDeg",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "batVerticalAngleDeg": {
                    "name": "batVerticalAngleDeg",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "batSwingStartPresence": {
                    "name": "batSwingStartPresence",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "batSwingStartTimestamp": {
                    "name": "batSwingStartTimestamp",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmTimestamp"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "batPeakHandSpeedMps": {
                    "name": "batPeakHandSpeedMps",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "teedBallLocationWorld3dPositionMeters": {
                    "name": "teedBallLocationWorld3dPositionMeters",
                    "isArray": false,
                    "type": {
                        "nonModel": "Lm3dPosition"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "trajectory": {
                    "name": "trajectory",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmTrajectory"
                    },
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "BaseballPitchDataPoints": {
            "name": "BaseballPitchDataPoints",
            "fields": {
                "pitchReleaseTimestamp": {
                    "name": "pitchReleaseTimestamp",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmTimestamp"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "pitchReleaseWorld3dPositionMeters": {
                    "name": "pitchReleaseWorld3dPositionMeters",
                    "isArray": false,
                    "type": {
                        "nonModel": "Lm3dPosition"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "pitchReleaseRadar3dPositionMetersDeg": {
                    "name": "pitchReleaseRadar3dPositionMetersDeg",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmSphericalPoint3d"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "pitchReleaseVelocityMps": {
                    "name": "pitchReleaseVelocityMps",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchReleaseArmSlotDeg": {
                    "name": "pitchReleaseArmSlotDeg",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchReleaseHorizontalAngleDeg": {
                    "name": "pitchReleaseHorizontalAngleDeg",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchReleaseVerticalAngleDeg": {
                    "name": "pitchReleaseVerticalAngleDeg",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchReleaseBackwardExtensionMeters": {
                    "name": "pitchReleaseBackwardExtensionMeters",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchBreakHorizontalMeters": {
                    "name": "pitchBreakHorizontalMeters",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchBreakVerticalMeters": {
                    "name": "pitchBreakVerticalMeters",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchBreakInducedVerticalMeters": {
                    "name": "pitchBreakInducedVerticalMeters",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchSpinTotalRpm": {
                    "name": "pitchSpinTotalRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchSpinActiveRpm": {
                    "name": "pitchSpinActiveRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchSpinBackRpm": {
                    "name": "pitchSpinBackRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchSpinSideRpm": {
                    "name": "pitchSpinSideRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchSpinTopRpm": {
                    "name": "pitchSpinTopRpm",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchSpinDirectionClockBearing": {
                    "name": "pitchSpinDirectionClockBearing",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmClockBearing"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "pitchSpinDirectionEfficiencyPct": {
                    "name": "pitchSpinDirectionEfficiencyPct",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchApproachVelocityMps": {
                    "name": "pitchApproachVelocityMps",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchApproachPlateTimestamp": {
                    "name": "pitchApproachPlateTimestamp",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmTimestamp"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "pitchApproachPlateWorld3dPositionMeters": {
                    "name": "pitchApproachPlateWorld3dPositionMeters",
                    "isArray": false,
                    "type": {
                        "nonModel": "Lm3dPosition"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "pitchCrossPlateTimestamp": {
                    "name": "pitchCrossPlateTimestamp",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmTimestamp"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "pitchCrossPlateWorld3dPositionMeters": {
                    "name": "pitchCrossPlateWorld3dPositionMeters",
                    "isArray": false,
                    "type": {
                        "nonModel": "Lm3dPosition"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "trajectory": {
                    "name": "trajectory",
                    "isArray": false,
                    "type": {
                        "nonModel": "LmTrajectory"
                    },
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmBaseballResults": {
            "name": "LmBaseballResults",
            "fields": {
                "pitcher": {
                    "name": "pitcher",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "hitter": {
                    "name": "hitter",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "pitcherName": {
                    "name": "pitcherName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "hitterName": {
                    "name": "hitterName",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "pitchData": {
                    "name": "pitchData",
                    "isArray": false,
                    "type": {
                        "nonModel": "BaseballPitchDataPoints"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "batData": {
                    "name": "batData",
                    "isArray": false,
                    "type": {
                        "nonModel": "BaseballBatDataPoints"
                    },
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmBaseballStatistics": {
            "name": "LmBaseballStatistics",
            "fields": {
                "whiffPercent": {
                    "name": "whiffPercent",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                },
                "swingAndMissPercent": {
                    "name": "swingAndMissPercent",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmTeamInviteLinkResult": {
            "name": "LmTeamInviteLinkResult",
            "fields": {
                "status": {
                    "name": "status",
                    "isArray": false,
                    "type": "String",
                    "isRequired": true,
                    "attributes": []
                },
                "link": {
                    "name": "link",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "errors": {
                    "name": "errors",
                    "isArray": true,
                    "type": {
                        "nonModel": "CustomError"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                }
            }
        },
        "LmTeamPlayerDeleteResult": {
            "name": "LmTeamPlayerDeleteResult",
            "fields": {
                "status": {
                    "name": "status",
                    "isArray": false,
                    "type": "String",
                    "isRequired": true,
                    "attributes": []
                },
                "teamAssigned": {
                    "name": "teamAssigned",
                    "isArray": false,
                    "type": {
                        "enum": "LmCoachPlayersTeamAssigned"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "errors": {
                    "name": "errors",
                    "isArray": true,
                    "type": {
                        "nonModel": "CustomError"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                }
            }
        },
        "LmTeamPlayerAddResult": {
            "name": "LmTeamPlayerAddResult",
            "fields": {
                "status": {
                    "name": "status",
                    "isArray": false,
                    "type": "String",
                    "isRequired": true,
                    "attributes": []
                },
                "isExist": {
                    "name": "isExist",
                    "isArray": false,
                    "type": "Boolean",
                    "isRequired": false,
                    "attributes": []
                },
                "teamPlayer": {
                    "name": "teamPlayer",
                    "isArray": false,
                    "type": {
                        "model": "LmTeamPlayers"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "coachPlayer": {
                    "name": "coachPlayer",
                    "isArray": false,
                    "type": {
                        "model": "LmCoachPlayers"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "errors": {
                    "name": "errors",
                    "isArray": true,
                    "type": {
                        "nonModel": "CustomError"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                }
            }
        },
        "LmTeamDeleteResult": {
            "name": "LmTeamDeleteResult",
            "fields": {
                "status": {
                    "name": "status",
                    "isArray": false,
                    "type": "String",
                    "isRequired": true,
                    "attributes": []
                },
                "teamDeleted": {
                    "name": "teamDeleted",
                    "isArray": false,
                    "type": {
                        "model": "LmTeam"
                    },
                    "isRequired": false,
                    "attributes": []
                },
                "teamPlayersDeleted": {
                    "name": "teamPlayersDeleted",
                    "isArray": true,
                    "type": {
                        "model": "LmTeamPlayers"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "coachPlayersNotAssigned": {
                    "name": "coachPlayersNotAssigned",
                    "isArray": true,
                    "type": {
                        "model": "LmCoachPlayers"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "errors": {
                    "name": "errors",
                    "isArray": true,
                    "type": {
                        "nonModel": "CustomError"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                }
            }
        },
        "RangeCount": {
            "name": "RangeCount",
            "fields": {
                "range": {
                    "name": "range",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                },
                "count": {
                    "name": "count",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                }
            }
        },
        "TimeWindowCount": {
            "name": "TimeWindowCount",
            "fields": {
                "start": {
                    "name": "start",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                },
                "end": {
                    "name": "end",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                },
                "count": {
                    "name": "count",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                }
            }
        },
        "ClubCount": {
            "name": "ClubCount",
            "fields": {
                "clubId": {
                    "name": "clubId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "count": {
                    "name": "count",
                    "isArray": false,
                    "type": "Int",
                    "isRequired": true,
                    "attributes": []
                }
            }
        },
        "ClubAverage": {
            "name": "ClubAverage",
            "fields": {
                "clubId": {
                    "name": "clubId",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": true,
                    "attributes": []
                },
                "average": {
                    "name": "average",
                    "isArray": false,
                    "type": "Float",
                    "isRequired": true,
                    "attributes": []
                }
            }
        },
        "RegisterShadow": {
            "name": "RegisterShadow",
            "fields": {
                "regUser": {
                    "name": "regUser",
                    "isArray": false,
                    "type": "String",
                    "isRequired": true,
                    "attributes": []
                },
                "prodRegDate": {
                    "name": "prodRegDate",
                    "isArray": false,
                    "type": "String",
                    "isRequired": true,
                    "attributes": []
                }
            }
        },
        "GraphQLResult": {
            "name": "GraphQLResult",
            "fields": {
                "status": {
                    "name": "status",
                    "isArray": false,
                    "type": "String",
                    "isRequired": true,
                    "attributes": []
                }
            }
        },
        "CustomError": {
            "name": "CustomError",
            "fields": {
                "errorType": {
                    "name": "errorType",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "message": {
                    "name": "message",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "GraphQLResultAuth": {
            "name": "GraphQLResultAuth",
            "fields": {
                "status": {
                    "name": "status",
                    "isArray": false,
                    "type": "String",
                    "isRequired": true,
                    "attributes": []
                },
                "errors": {
                    "name": "errors",
                    "isArray": true,
                    "type": {
                        "nonModel": "CustomError"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                }
            }
        },
        "GraphQLDeleteResultAuth": {
            "name": "GraphQLDeleteResultAuth",
            "fields": {
                "status": {
                    "name": "status",
                    "isArray": false,
                    "type": "String",
                    "isRequired": true,
                    "attributes": []
                },
                "errors": {
                    "name": "errors",
                    "isArray": true,
                    "type": {
                        "nonModel": "CustomError"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                },
                "id": {
                    "name": "id",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": false,
                    "attributes": []
                }
            }
        },
        "LmDataSessionListResult": {
            "name": "LmDataSessionListResult",
            "fields": {
                "nextToken": {
                    "name": "nextToken",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "items": {
                    "name": "items",
                    "isArray": true,
                    "type": {
                        "model": "LmDataSession"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                }
            }
        },
        "LmDataResultsListResult": {
            "name": "LmDataResultsListResult",
            "fields": {
                "nextToken": {
                    "name": "nextToken",
                    "isArray": false,
                    "type": "String",
                    "isRequired": false,
                    "attributes": []
                },
                "items": {
                    "name": "items",
                    "isArray": true,
                    "type": {
                        "model": "LmDataResults"
                    },
                    "isRequired": false,
                    "attributes": [],
                    "isArrayNullable": true
                }
            }
        },
        "LmShareSession": {
            "name": "LmShareSession",
            "fields": {
                "shareUrl": {
                    "name": "shareUrl",
                    "isArray": false,
                    "type": "ID",
                    "isRequired": false,
                    "attributes": []
                }
            }
        }
    },
    "codegenVersion": "3.4.4",
    "version": "c52bb1c0aead5edc43d148fde1b4a064"
};