// @ts-check
import { initSchema } from '@aws-amplify/datastore';
import { schema } from './schema';

const CompetitiveLevel = {
  "YOUTH_LESS_THAN13": "YouthLessThan13",
  "HIGH_SCHOOL": "HighSchool",
  "COLLEGE": "College",
  "PROFESSIONAL": "Professional",
  "AMATEUR": "Amateur"
};

const LmDistanceUnits = {
  "FEET": "Feet",
  "YARDS": "Yards",
  "METERS": "Meters"
};

const LmSpeedUnits = {
  "MPH": "Mph",
  "KPH": "Kph",
  "MPS": "Mps"
};

const LmTemperatureUnits = {
  "FAHRENHEIT": "Fahrenheit",
  "CELCIUS": "Celcius"
};

const LmDisplayMode = {
  "GRAPHIC": "Graphic",
  "TILES": "Tiles",
  "MULTI_DATA": "MultiData",
  "SINGLE_DATA": "SingleData"
};

const LmTheme = {
  "LIGHT": "Light",
  "DARK": "Dark"
};

const LmTargetOption = {
  "NONE": "None",
  "BASKET": "Basket",
  "GREEN": "Green",
  "CIRCLE": "Circle"
};

const LmCameraView = {
  "STATIONARY": "Stationary",
  "BALL": "Ball",
  "FLIGHT": "Flight"
};

const LmBallPath = {
  "SIMULATED": "Simulated",
  "RAW": "Raw"
};

const LmBallType = {
  "PREMIUM": "Premium",
  "TITLEIST_RCT": "TitleistRct"
};

const LmHandedness = {
  "LEFT": "Left",
  "RIGHT": "Right"
};

const DataType = {
  "INT": "Int",
  "FLOAT": "Float"
};

const LmLocation = {
  "NET": "Net",
  "SCREEN": "Screen",
  "SIMULATOR": "Simulator",
  "OUTDOOR_RANGE": "OutdoorRange",
  "INDOOR_RANGE": "IndoorRange",
  "COURSE": "Course",
  "FIELD": "Field"
};

const ValueType = {
  "CLUB_SPEED": "ClubSpeed",
  "BALL_SPEED": "BallSpeed",
  "SMASH_FACTOR": "SmashFactor",
  "ATTACK_ANGLE": "AttackAngle",
  "CLUB_PATH": "ClubPath",
  "LAUNCH_ANGLE": "LaunchAngle",
  "HORIZONTAL_LAUNCH_ANGLE": "HorizontalLaunchAngle",
  "FACE_ANGLE": "FaceAngle",
  "FACE_TO_PATH": "FaceToPath",
  "SPIN_RATE": "SpinRate",
  "SPIN_AXIS": "SpinAxis",
  "CARRY_DISTANCE": "CarryDistance",
  "TOTAL_DISTANCE": "TotalDistance",
  "SIDE": "Side",
  "SIDE_TOTAL": "SideTotal",
  "APEX": "Apex",
  "BALL_DIRECTION": "BallDirection",
  "BALL_CURVE": "BallCurve",
  "DESCENT_ANGLE": "DescentAngle"
};

const BaseBallValueType = {
  "PITCH_SPIN": "PitchSpin",
  "PITCH_SPEED": "PitchSpeed",
  "EXIT_VELOCITY": "ExitVelocity",
  "LAUNCH_ANGLE": "LaunchAngle",
  "HORIZONTAL_LAUNCH_ANGLE": "HorizontalLaunchAngle",
  "DISTANCE": "Distance",
  "APEX": "Apex",
  "BAT_SPEED": "BatSpeed"
};

const BaseballValueType = {
  "PITCH_RELEASE_TIMESTAMP": "pitchReleaseTimestamp",
  "PITCH_RELEASE_WORLD3D_POSITION_METERS": "pitchReleaseWorld3dPositionMeters",
  "PITCH_RELEASE_RADAR3D_POSITION_METERS_DEG": "pitchReleaseRadar3dPositionMetersDeg",
  "PITCH_RELEASE_VELOCITY_MPS": "pitchReleaseVelocityMps",
  "PITCH_RELEASE_ARM_SLOT_DEG": "pitchReleaseArmSlotDeg",
  "PITCH_RELEASE_HORIZONTAL_ANGLE_DEG": "pitchReleaseHorizontalAngleDeg",
  "PITCH_RELEASE_VERTICAL_ANGLE_DEG": "pitchReleaseVerticalAngleDeg",
  "PITCH_RELEASE_BACKWARD_EXTENSION_METERS": "pitchReleaseBackwardExtensionMeters",
  "PITCH_BREAK_HORIZONTAL_METERS": "pitchBreakHorizontalMeters",
  "PITCH_BREAK_VERTICAL_METERS": "pitchBreakVerticalMeters",
  "PITCH_BREAK_INDUCED_VERTICAL_METERS": "pitchBreakInducedVerticalMeters",
  "PITCH_SPIN_TOTAL_RPM": "pitchSpinTotalRpm",
  "PITCH_SPIN_ACTIVE_RPM": "pitchSpinActiveRpm",
  "PITCH_SPIN_BACK_RPM": "pitchSpinBackRpm",
  "PITCH_SPIN_SIDE_RPM": "pitchSpinSideRpm",
  "PITCH_SPIN_TOP_RPM": "pitchSpinTopRpm",
  "PITCH_SPIN_DIRECTION_CLOCK_BEARING": "pitchSpinDirectionClockBearing",
  "PITCH_SPIN_DIRECTION_EFFICIENCY_PCT": "pitchSpinDirectionEfficiencyPct",
  "PITCH_APPROACH_VELOCITY_MPS": "pitchApproachVelocityMps",
  "PITCH_APPROACH_PLATE_TIMESTAMP": "pitchApproachPlateTimestamp",
  "PITCH_APPROACH_PLATE_WORLD3D_POSITION_METERS": "pitchApproachPlateWorld3dPositionMeters",
  "PITCH_CROSS_PLATE_TIMESTAMP": "pitchCrossPlateTimestamp",
  "PITCH_CROSS_PLATE_WORLD3D_POSITION_METERS": "pitchCrossPlateWorld3dPositionMeters",
  "HIT_DIRECTION_DEG": "hitDirectionDeg",
  "HIT_DISTANCE_METERS": "hitDistanceMeters",
  "HIT_EXIT_VELOCITY_MPS": "hitExitVelocityMps",
  "HIT_LAUNCH_ANGLE_DEG": "hitLaunchAngleDeg",
  "HIT_SPIN_TOTAL_RPM": "hitSpinTotalRpm",
  "HIT_SPIN_ACTIVE_RPM": "hitSpinActiveRpm",
  "HIT_SPIN_BACK_RPM": "hitSpinBackRpm",
  "HIT_SPIN_SIDE_RPM": "hitSpinSideRpm",
  "HIT_SPIN_TOP_RPM": "hitSpinTopRpm",
  "HIT_SPIN_DIRECTION_CLOCK_BEARING": "hitSpinDirectionClockBearing",
  "HIT_SPIN_DIRECTION_EFFICIENCY_PCT": "hitSpinDirectionEfficiencyPct",
  "HIT_BALL_CONTACT_PRESENCE": "hitBallContactPresence",
  "HIT_BALL_CONTACT_TIMESTAMP": "hitBallContactTimestamp",
  "HIT_BALL_CONTACT_WORLD3D_POSITION_METERS": "hitBallContactWorld3dPositionMeters",
  "HIT_BALL_CONTACT_RADAR3D_POSITION_METERS_DEG": "hitBallContactRadar3dPositionMetersDeg",
  "BAT_SPEED_MPS": "batSpeedMps",
  "BAT_ROTATIONAL_ACCELERATION_GS": "batRotationalAccelerationGs",
  "BAT_ATTACK_ANGLE_DEG": "batAttackAngleDeg",
  "BAT_EARLY_CONNECTION_DEG": "batEarlyConnectionDeg",
  "BAT_CONNECTION_AT_IMPACT_DEG": "batConnectionAtImpactDeg",
  "BAT_VERTICAL_ANGLE_DEG": "batVerticalAngleDeg",
  "BAT_SWING_START_PRESENCE": "batSwingStartPresence",
  "BAT_SWING_START_TIMESTAMP": "batSwingStartTimestamp",
  "BAT_PEAK_HAND_SPEED_MPS": "batPeakHandSpeedMps",
  "TEED_BALL_LOCATION_WORLD3D_POSITION_METERS": "teedBallLocationWorld3dPositionMeters"
};

const LmGolfTrajectoryType = {
  "FLIGHT": "Flight",
  "NORMALIZED": "Normalized"
};

const LmBaseballTrajectoryType = {
  "UNKNOWN": "Unknown",
  "PITCH": "Pitch",
  "HIT": "Hit"
};

const LmUserRole = {
  "USERS": "Users",
  "COACHES": "Coaches",
  "ADMIN": "Admin"
};

const LmTeamPlayerType = {
  "BATTING": "Batting",
  "PITCHING": "Pitching",
  "BOTH": "Both"
};

const LmDataSessionPlayMode = {
  "LIVE_AT_BAT": "LiveAtBat",
  "BATTING_PRACT": "BattingPract",
  "TEE_MODE": "TeeMode"
};

const LmSportType = {
  "GOLF": "Golf",
  "BASEBALL": "Baseball"
};

const LmCoachPlayersTeamAssigned = {
  "Y": "Y",
  "N": "N"
};

const LmTeamPlayerStatus = {
  "PENDING": "Pending",
  "ACCEPTED": "Accepted"
};

const DateResolution = {
  "DAY": "Day",
  "WEEK": "Week",
  "MONTH": "Month"
};

const { LmUser, LmProfile, LmFeature, LmDevice, LmDataResults, LmDataSession, LmTeamPlayers, LmTeamCoaches, LmTeam, LmCoachPlayers, LmClockBearing, LmTimestamp, Lm2dPosition, Lm3dPosition, LmSphericalPoint3d, LmOrientation3d, LmPolynomial, LmTrajectory, BaseballBatDataPoints, BaseballPitchDataPoints, LmBaseballResults, LmBaseballStatistics, LmTeamInviteLinkResult, LmTeamPlayerDeleteResult, LmTeamPlayerAddResult, LmTeamDeleteResult, RangeCount, TimeWindowCount, ClubCount, ClubAverage, RegisterShadow, GraphQLResult, CustomError, GraphQLResultAuth, GraphQLDeleteResultAuth, LmDataSessionListResult, LmDataResultsListResult, LmShareSession } = initSchema(schema);

export {
  LmUser,
  LmProfile,
  LmFeature,
  LmDevice,
  LmDataResults,
  LmDataSession,
  LmTeamPlayers,
  LmTeamCoaches,
  LmTeam,
  LmCoachPlayers,
  CompetitiveLevel,
  LmDistanceUnits,
  LmSpeedUnits,
  LmTemperatureUnits,
  LmDisplayMode,
  LmTheme,
  LmTargetOption,
  LmCameraView,
  LmBallPath,
  LmBallType,
  LmHandedness,
  DataType,
  LmLocation,
  ValueType,
  BaseBallValueType,
  BaseballValueType,
  LmGolfTrajectoryType,
  LmBaseballTrajectoryType,
  LmUserRole,
  LmTeamPlayerType,
  LmDataSessionPlayMode,
  LmSportType,
  LmCoachPlayersTeamAssigned,
  LmTeamPlayerStatus,
  DateResolution,
  LmClockBearing,
  LmTimestamp,
  Lm2dPosition,
  Lm3dPosition,
  LmSphericalPoint3d,
  LmOrientation3d,
  LmPolynomial,
  LmTrajectory,
  BaseballBatDataPoints,
  BaseballPitchDataPoints,
  LmBaseballResults,
  LmBaseballStatistics,
  LmTeamInviteLinkResult,
  LmTeamPlayerDeleteResult,
  LmTeamPlayerAddResult,
  LmTeamDeleteResult,
  RangeCount,
  TimeWindowCount,
  ClubCount,
  ClubAverage,
  RegisterShadow,
  GraphQLResult,
  CustomError,
  GraphQLResultAuth,
  GraphQLDeleteResultAuth,
  LmDataSessionListResult,
  LmDataResultsListResult,
  LmShareSession
};