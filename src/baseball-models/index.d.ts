import { ModelInit, MutableModel } from "@aws-amplify/datastore";
// @ts-ignore
import { LazyLoading, LazyLoadingDisabled, AsyncCollection, AsyncItem } from "@aws-amplify/datastore";

export enum CompetitiveLevel {
  YOUTH_LESS_THAN13 = "YouthLessThan13",
  HIGH_SCHOOL = "HighSchool",
  COLLEGE = "College",
  PROFESSIONAL = "Professional",
  AMATEUR = "Amateur"
}

export enum LmDistanceUnits {
  FEET = "Feet",
  YARDS = "Yards",
  METERS = "Meters"
}

export enum LmSpeedUnits {
  MPH = "Mph",
  KPH = "Kph",
  MPS = "Mps"
}

export enum LmTemperatureUnits {
  FAHRENHEIT = "Fahrenheit",
  CELCIUS = "Celcius"
}

export enum LmDisplayMode {
  GRAPHIC = "Graphic",
  TILES = "Tiles",
  MULTI_DATA = "MultiData",
  SINGLE_DATA = "SingleData"
}

export enum LmTheme {
  LIGHT = "Light",
  DARK = "Dark"
}

export enum LmTargetOption {
  NONE = "None",
  BASKET = "Basket",
  GREEN = "Green",
  CIRCLE = "Circle"
}

export enum LmCameraView {
  STATIONARY = "Stationary",
  BALL = "Ball",
  FLIGHT = "Flight"
}

export enum LmBallPath {
  SIMULATED = "Simulated",
  RAW = "Raw"
}

export enum LmBallType {
  PREMIUM = "Premium",
  TITLEIST_RCT = "TitleistRct"
}

export enum LmHandedness {
  LEFT = "Left",
  RIGHT = "Right"
}

export enum DataType {
  INT = "Int",
  FLOAT = "Float"
}

export enum LmLocation {
  NET = "Net",
  SCREEN = "Screen",
  SIMULATOR = "Simulator",
  OUTDOOR_RANGE = "OutdoorRange",
  INDOOR_RANGE = "IndoorRange",
  COURSE = "Course",
  FIELD = "Field"
}

export enum ValueType {
  CLUB_SPEED = "ClubSpeed",
  BALL_SPEED = "BallSpeed",
  SMASH_FACTOR = "SmashFactor",
  ATTACK_ANGLE = "AttackAngle",
  CLUB_PATH = "ClubPath",
  LAUNCH_ANGLE = "LaunchAngle",
  HORIZONTAL_LAUNCH_ANGLE = "HorizontalLaunchAngle",
  FACE_ANGLE = "FaceAngle",
  FACE_TO_PATH = "FaceToPath",
  SPIN_RATE = "SpinRate",
  SPIN_AXIS = "SpinAxis",
  CARRY_DISTANCE = "CarryDistance",
  TOTAL_DISTANCE = "TotalDistance",
  SIDE = "Side",
  SIDE_TOTAL = "SideTotal",
  APEX = "Apex",
  BALL_DIRECTION = "BallDirection",
  BALL_CURVE = "BallCurve",
  DESCENT_ANGLE = "DescentAngle"
}

export enum BaseBallValueType {
  PITCH_SPIN = "PitchSpin",
  PITCH_SPEED = "PitchSpeed",
  EXIT_VELOCITY = "ExitVelocity",
  LAUNCH_ANGLE = "LaunchAngle",
  HORIZONTAL_LAUNCH_ANGLE = "HorizontalLaunchAngle",
  DISTANCE = "Distance",
  APEX = "Apex",
  BAT_SPEED = "BatSpeed"
}

export enum BaseballValueType {
  PITCH_RELEASE_TIMESTAMP = "pitchReleaseTimestamp",
  PITCH_RELEASE_WORLD3D_POSITION_METERS = "pitchReleaseWorld3dPositionMeters",
  PITCH_RELEASE_RADAR3D_POSITION_METERS_DEG = "pitchReleaseRadar3dPositionMetersDeg",
  PITCH_RELEASE_VELOCITY_MPS = "pitchReleaseVelocityMps",
  PITCH_RELEASE_ARM_SLOT_DEG = "pitchReleaseArmSlotDeg",
  PITCH_RELEASE_HORIZONTAL_ANGLE_DEG = "pitchReleaseHorizontalAngleDeg",
  PITCH_RELEASE_VERTICAL_ANGLE_DEG = "pitchReleaseVerticalAngleDeg",
  PITCH_RELEASE_BACKWARD_EXTENSION_METERS = "pitchReleaseBackwardExtensionMeters",
  PITCH_BREAK_HORIZONTAL_METERS = "pitchBreakHorizontalMeters",
  PITCH_BREAK_VERTICAL_METERS = "pitchBreakVerticalMeters",
  PITCH_BREAK_INDUCED_VERTICAL_METERS = "pitchBreakInducedVerticalMeters",
  PITCH_SPIN_TOTAL_RPM = "pitchSpinTotalRpm",
  PITCH_SPIN_ACTIVE_RPM = "pitchSpinActiveRpm",
  PITCH_SPIN_BACK_RPM = "pitchSpinBackRpm",
  PITCH_SPIN_SIDE_RPM = "pitchSpinSideRpm",
  PITCH_SPIN_TOP_RPM = "pitchSpinTopRpm",
  PITCH_SPIN_DIRECTION_CLOCK_BEARING = "pitchSpinDirectionClockBearing",
  PITCH_SPIN_DIRECTION_EFFICIENCY_PCT = "pitchSpinDirectionEfficiencyPct",
  PITCH_APPROACH_VELOCITY_MPS = "pitchApproachVelocityMps",
  PITCH_APPROACH_PLATE_TIMESTAMP = "pitchApproachPlateTimestamp",
  PITCH_APPROACH_PLATE_WORLD3D_POSITION_METERS = "pitchApproachPlateWorld3dPositionMeters",
  PITCH_CROSS_PLATE_TIMESTAMP = "pitchCrossPlateTimestamp",
  PITCH_CROSS_PLATE_WORLD3D_POSITION_METERS = "pitchCrossPlateWorld3dPositionMeters",
  HIT_DIRECTION_DEG = "hitDirectionDeg",
  HIT_DISTANCE_METERS = "hitDistanceMeters",
  HIT_EXIT_VELOCITY_MPS = "hitExitVelocityMps",
  HIT_LAUNCH_ANGLE_DEG = "hitLaunchAngleDeg",
  HIT_SPIN_TOTAL_RPM = "hitSpinTotalRpm",
  HIT_SPIN_ACTIVE_RPM = "hitSpinActiveRpm",
  HIT_SPIN_BACK_RPM = "hitSpinBackRpm",
  HIT_SPIN_SIDE_RPM = "hitSpinSideRpm",
  HIT_SPIN_TOP_RPM = "hitSpinTopRpm",
  HIT_SPIN_DIRECTION_CLOCK_BEARING = "hitSpinDirectionClockBearing",
  HIT_SPIN_DIRECTION_EFFICIENCY_PCT = "hitSpinDirectionEfficiencyPct",
  HIT_BALL_CONTACT_PRESENCE = "hitBallContactPresence",
  HIT_BALL_CONTACT_TIMESTAMP = "hitBallContactTimestamp",
  HIT_BALL_CONTACT_WORLD3D_POSITION_METERS = "hitBallContactWorld3dPositionMeters",
  HIT_BALL_CONTACT_RADAR3D_POSITION_METERS_DEG = "hitBallContactRadar3dPositionMetersDeg",
  BAT_SPEED_MPS = "batSpeedMps",
  BAT_ROTATIONAL_ACCELERATION_GS = "batRotationalAccelerationGs",
  BAT_ATTACK_ANGLE_DEG = "batAttackAngleDeg",
  BAT_EARLY_CONNECTION_DEG = "batEarlyConnectionDeg",
  BAT_CONNECTION_AT_IMPACT_DEG = "batConnectionAtImpactDeg",
  BAT_VERTICAL_ANGLE_DEG = "batVerticalAngleDeg",
  BAT_SWING_START_PRESENCE = "batSwingStartPresence",
  BAT_SWING_START_TIMESTAMP = "batSwingStartTimestamp",
  BAT_PEAK_HAND_SPEED_MPS = "batPeakHandSpeedMps",
  TEED_BALL_LOCATION_WORLD3D_POSITION_METERS = "teedBallLocationWorld3dPositionMeters"
}

export enum LmGolfTrajectoryType {
  FLIGHT = "Flight",
  NORMALIZED = "Normalized"
}

export enum LmBaseballTrajectoryType {
  UNKNOWN = "Unknown",
  PITCH = "Pitch",
  HIT = "Hit"
}

export enum LmUserRole {
  USERS = "Users",
  COACHES = "Coaches",
  ADMIN = "Admin"
}

export enum LmTeamPlayerType {
  BATTING = "Batting",
  PITCHING = "Pitching",
  BOTH = "Both"
}

export enum LmDataSessionPlayMode {
  LIVE_AT_BAT = "LiveAtBat",
  BATTING_PRACT = "BattingPract",
  TEE_MODE = "TeeMode"
}

export enum LmSportType {
  GOLF = "Golf",
  BASEBALL = "Baseball"
}

export enum LmCoachPlayersTeamAssigned {
  Y = "Y",
  N = "N"
}

export enum LmTeamPlayerStatus {
  PENDING = "Pending",
  ACCEPTED = "Accepted"
}

export enum DateResolution {
  DAY = "Day",
  WEEK = "Week",
  MONTH = "Month"
}

type EagerLmClockBearing = {
  readonly hours?: number | null;
  readonly minutes?: number | null;
}

type LazyLmClockBearing = {
  readonly hours?: number | null;
  readonly minutes?: number | null;
}

export declare type LmClockBearing = LazyLoading extends LazyLoadingDisabled ? EagerLmClockBearing : LazyLmClockBearing

export declare const LmClockBearing: (new (init: ModelInit<LmClockBearing>) => LmClockBearing)

type EagerLmTimestamp = {
  readonly seconds?: number | null;
  readonly nanos?: number | null;
}

type LazyLmTimestamp = {
  readonly seconds?: number | null;
  readonly nanos?: number | null;
}

export declare type LmTimestamp = LazyLoading extends LazyLoadingDisabled ? EagerLmTimestamp : LazyLmTimestamp

export declare const LmTimestamp: (new (init: ModelInit<LmTimestamp>) => LmTimestamp)

type EagerLm2dPosition = {
  readonly x_pos?: number | null;
  readonly y_pos?: number | null;
}

type LazyLm2dPosition = {
  readonly x_pos?: number | null;
  readonly y_pos?: number | null;
}

export declare type Lm2dPosition = LazyLoading extends LazyLoadingDisabled ? EagerLm2dPosition : LazyLm2dPosition

export declare const Lm2dPosition: (new (init: ModelInit<Lm2dPosition>) => Lm2dPosition)

type EagerLm3dPosition = {
  readonly x_pos?: number | null;
  readonly y_pos?: number | null;
  readonly z_pos?: number | null;
}

type LazyLm3dPosition = {
  readonly x_pos?: number | null;
  readonly y_pos?: number | null;
  readonly z_pos?: number | null;
}

export declare type Lm3dPosition = LazyLoading extends LazyLoadingDisabled ? EagerLm3dPosition : LazyLm3dPosition

export declare const Lm3dPosition: (new (init: ModelInit<Lm3dPosition>) => Lm3dPosition)

type EagerLmSphericalPoint3d = {
  readonly r?: number | null;
  readonly theta?: number | null;
  readonly phi?: number | null;
}

type LazyLmSphericalPoint3d = {
  readonly r?: number | null;
  readonly theta?: number | null;
  readonly phi?: number | null;
}

export declare type LmSphericalPoint3d = LazyLoading extends LazyLoadingDisabled ? EagerLmSphericalPoint3d : LazyLmSphericalPoint3d

export declare const LmSphericalPoint3d: (new (init: ModelInit<LmSphericalPoint3d>) => LmSphericalPoint3d)

type EagerLmOrientation3d = {
  readonly pitch?: number | null;
  readonly yaw?: number | null;
  readonly roll?: number | null;
}

type LazyLmOrientation3d = {
  readonly pitch?: number | null;
  readonly yaw?: number | null;
  readonly roll?: number | null;
}

export declare type LmOrientation3d = LazyLoading extends LazyLoadingDisabled ? EagerLmOrientation3d : LazyLmOrientation3d

export declare const LmOrientation3d: (new (init: ModelInit<LmOrientation3d>) => LmOrientation3d)

type EagerLmPolynomial = {
  readonly x_coefficients?: (number | null)[] | null;
  readonly y_coefficients?: (number | null)[] | null;
  readonly z_coefficients?: (number | null)[] | null;
}

type LazyLmPolynomial = {
  readonly x_coefficients?: (number | null)[] | null;
  readonly y_coefficients?: (number | null)[] | null;
  readonly z_coefficients?: (number | null)[] | null;
}

export declare type LmPolynomial = LazyLoading extends LazyLoadingDisabled ? EagerLmPolynomial : LazyLmPolynomial

export declare const LmPolynomial: (new (init: ModelInit<LmPolynomial>) => LmPolynomial)

type EagerLmTrajectory = {
  readonly baseballType?: LmBaseballTrajectoryType | keyof typeof LmBaseballTrajectoryType | null;
  readonly golfType?: LmGolfTrajectoryType | keyof typeof LmGolfTrajectoryType | null;
  readonly polynomial?: LmPolynomial | null;
}

type LazyLmTrajectory = {
  readonly baseballType?: LmBaseballTrajectoryType | keyof typeof LmBaseballTrajectoryType | null;
  readonly golfType?: LmGolfTrajectoryType | keyof typeof LmGolfTrajectoryType | null;
  readonly polynomial?: LmPolynomial | null;
}

export declare type LmTrajectory = LazyLoading extends LazyLoadingDisabled ? EagerLmTrajectory : LazyLmTrajectory

export declare const LmTrajectory: (new (init: ModelInit<LmTrajectory>) => LmTrajectory)

type EagerBaseballBatDataPoints = {
  readonly hitDirectionDeg?: number | null;
  readonly hitDistanceMeters?: number | null;
  readonly hitApexMeters?: number | null;
  readonly hitExitVelocityMps?: number | null;
  readonly hitLaunchAngleDeg?: number | null;
  readonly hitSpinTotalRpm?: number | null;
  readonly hitSpinActiveRpm?: number | null;
  readonly hitSpinBackRpm?: number | null;
  readonly hitSpinSideRpm?: number | null;
  readonly hitSpinTopRpm?: number | null;
  readonly hitSpinDirectionClockBearing?: LmClockBearing | null;
  readonly hitSpinDirectionEfficiencyPct?: number | null;
  readonly hitBallContactPresence?: boolean | null;
  readonly hitBallContactTimestamp?: LmTimestamp | null;
  readonly hitBallContactWorld3dPositionMeters?: Lm3dPosition | null;
  readonly hitBallContactRadar3dPositionMetersDeg?: LmSphericalPoint3d | null;
  readonly batSpeedMps?: number | null;
  readonly apex?: number | null;
  readonly batRotationalAccelerationGs?: number | null;
  readonly batAttackAngleDeg?: number | null;
  readonly batEarlyConnectionDeg?: number | null;
  readonly batConnectionAtImpactDeg?: number | null;
  readonly batVerticalAngleDeg?: number | null;
  readonly batSwingStartPresence?: boolean | null;
  readonly batSwingStartTimestamp?: LmTimestamp | null;
  readonly batPeakHandSpeedMps?: number | null;
  readonly teedBallLocationWorld3dPositionMeters?: Lm3dPosition | null;
  readonly trajectory?: LmTrajectory | null;
}

type LazyBaseballBatDataPoints = {
  readonly hitDirectionDeg?: number | null;
  readonly hitDistanceMeters?: number | null;
  readonly hitApexMeters?: number | null;
  readonly hitExitVelocityMps?: number | null;
  readonly hitLaunchAngleDeg?: number | null;
  readonly hitSpinTotalRpm?: number | null;
  readonly hitSpinActiveRpm?: number | null;
  readonly hitSpinBackRpm?: number | null;
  readonly hitSpinSideRpm?: number | null;
  readonly hitSpinTopRpm?: number | null;
  readonly hitSpinDirectionClockBearing?: LmClockBearing | null;
  readonly hitSpinDirectionEfficiencyPct?: number | null;
  readonly hitBallContactPresence?: boolean | null;
  readonly hitBallContactTimestamp?: LmTimestamp | null;
  readonly hitBallContactWorld3dPositionMeters?: Lm3dPosition | null;
  readonly hitBallContactRadar3dPositionMetersDeg?: LmSphericalPoint3d | null;
  readonly batSpeedMps?: number | null;
  readonly apex?: number | null;
  readonly batRotationalAccelerationGs?: number | null;
  readonly batAttackAngleDeg?: number | null;
  readonly batEarlyConnectionDeg?: number | null;
  readonly batConnectionAtImpactDeg?: number | null;
  readonly batVerticalAngleDeg?: number | null;
  readonly batSwingStartPresence?: boolean | null;
  readonly batSwingStartTimestamp?: LmTimestamp | null;
  readonly batPeakHandSpeedMps?: number | null;
  readonly teedBallLocationWorld3dPositionMeters?: Lm3dPosition | null;
  readonly trajectory?: LmTrajectory | null;
}

export declare type BaseballBatDataPoints = LazyLoading extends LazyLoadingDisabled ? EagerBaseballBatDataPoints : LazyBaseballBatDataPoints

export declare const BaseballBatDataPoints: (new (init: ModelInit<BaseballBatDataPoints>) => BaseballBatDataPoints)

type EagerBaseballPitchDataPoints = {
  readonly pitchReleaseTimestamp?: LmTimestamp | null;
  readonly pitchReleaseWorld3dPositionMeters?: Lm3dPosition | null;
  readonly pitchReleaseRadar3dPositionMetersDeg?: LmSphericalPoint3d | null;
  readonly pitchReleaseVelocityMps?: number | null;
  readonly pitchReleaseArmSlotDeg?: number | null;
  readonly pitchReleaseHorizontalAngleDeg?: number | null;
  readonly pitchReleaseVerticalAngleDeg?: number | null;
  readonly pitchReleaseBackwardExtensionMeters?: number | null;
  readonly pitchBreakHorizontalMeters?: number | null;
  readonly pitchBreakVerticalMeters?: number | null;
  readonly pitchBreakInducedVerticalMeters?: number | null;
  readonly pitchSpinTotalRpm?: number | null;
  readonly pitchSpinActiveRpm?: number | null;
  readonly pitchSpinBackRpm?: number | null;
  readonly pitchSpinSideRpm?: number | null;
  readonly pitchSpinTopRpm?: number | null;
  readonly pitchSpinDirectionClockBearing?: LmClockBearing | null;
  readonly pitchSpinDirectionEfficiencyPct?: number | null;
  readonly pitchApproachVelocityMps?: number | null;
  readonly pitchApproachPlateTimestamp?: LmTimestamp | null;
  readonly pitchApproachPlateWorld3dPositionMeters?: Lm3dPosition | null;
  readonly pitchCrossPlateTimestamp?: LmTimestamp | null;
  readonly pitchCrossPlateWorld3dPositionMeters?: Lm3dPosition | null;
  readonly trajectory?: LmTrajectory | null;
}

type LazyBaseballPitchDataPoints = {
  readonly pitchReleaseTimestamp?: LmTimestamp | null;
  readonly pitchReleaseWorld3dPositionMeters?: Lm3dPosition | null;
  readonly pitchReleaseRadar3dPositionMetersDeg?: LmSphericalPoint3d | null;
  readonly pitchReleaseVelocityMps?: number | null;
  readonly pitchReleaseArmSlotDeg?: number | null;
  readonly pitchReleaseHorizontalAngleDeg?: number | null;
  readonly pitchReleaseVerticalAngleDeg?: number | null;
  readonly pitchReleaseBackwardExtensionMeters?: number | null;
  readonly pitchBreakHorizontalMeters?: number | null;
  readonly pitchBreakVerticalMeters?: number | null;
  readonly pitchBreakInducedVerticalMeters?: number | null;
  readonly pitchSpinTotalRpm?: number | null;
  readonly pitchSpinActiveRpm?: number | null;
  readonly pitchSpinBackRpm?: number | null;
  readonly pitchSpinSideRpm?: number | null;
  readonly pitchSpinTopRpm?: number | null;
  readonly pitchSpinDirectionClockBearing?: LmClockBearing | null;
  readonly pitchSpinDirectionEfficiencyPct?: number | null;
  readonly pitchApproachVelocityMps?: number | null;
  readonly pitchApproachPlateTimestamp?: LmTimestamp | null;
  readonly pitchApproachPlateWorld3dPositionMeters?: Lm3dPosition | null;
  readonly pitchCrossPlateTimestamp?: LmTimestamp | null;
  readonly pitchCrossPlateWorld3dPositionMeters?: Lm3dPosition | null;
  readonly trajectory?: LmTrajectory | null;
}

export declare type BaseballPitchDataPoints = LazyLoading extends LazyLoadingDisabled ? EagerBaseballPitchDataPoints : LazyBaseballPitchDataPoints

export declare const BaseballPitchDataPoints: (new (init: ModelInit<BaseballPitchDataPoints>) => BaseballPitchDataPoints)

type EagerLmBaseballResults = {
  readonly pitcher?: string | null;
  readonly hitter?: string | null;
  readonly pitcherName?: string | null;
  readonly hitterName?: string | null;
  readonly pitchData?: BaseballPitchDataPoints | null;
  readonly batData?: BaseballBatDataPoints | null;
}

type LazyLmBaseballResults = {
  readonly pitcher?: string | null;
  readonly hitter?: string | null;
  readonly pitcherName?: string | null;
  readonly hitterName?: string | null;
  readonly pitchData?: BaseballPitchDataPoints | null;
  readonly batData?: BaseballBatDataPoints | null;
}

export declare type LmBaseballResults = LazyLoading extends LazyLoadingDisabled ? EagerLmBaseballResults : LazyLmBaseballResults

export declare const LmBaseballResults: (new (init: ModelInit<LmBaseballResults>) => LmBaseballResults)

type EagerLmBaseballStatistics = {
  readonly whiffPercent?: number | null;
  readonly swingAndMissPercent?: number | null;
}

type LazyLmBaseballStatistics = {
  readonly whiffPercent?: number | null;
  readonly swingAndMissPercent?: number | null;
}

export declare type LmBaseballStatistics = LazyLoading extends LazyLoadingDisabled ? EagerLmBaseballStatistics : LazyLmBaseballStatistics

export declare const LmBaseballStatistics: (new (init: ModelInit<LmBaseballStatistics>) => LmBaseballStatistics)

type EagerLmTeamInviteLinkResult = {
  readonly status: string;
  readonly link?: string | null;
  readonly errors?: (CustomError | null)[] | null;
}

type LazyLmTeamInviteLinkResult = {
  readonly status: string;
  readonly link?: string | null;
  readonly errors?: (CustomError | null)[] | null;
}

export declare type LmTeamInviteLinkResult = LazyLoading extends LazyLoadingDisabled ? EagerLmTeamInviteLinkResult : LazyLmTeamInviteLinkResult

export declare const LmTeamInviteLinkResult: (new (init: ModelInit<LmTeamInviteLinkResult>) => LmTeamInviteLinkResult)

type EagerLmTeamPlayerDeleteResult = {
  readonly status: string;
  readonly teamAssigned?: LmCoachPlayersTeamAssigned | keyof typeof LmCoachPlayersTeamAssigned | null;
  readonly errors?: (CustomError | null)[] | null;
}

type LazyLmTeamPlayerDeleteResult = {
  readonly status: string;
  readonly teamAssigned?: LmCoachPlayersTeamAssigned | keyof typeof LmCoachPlayersTeamAssigned | null;
  readonly errors?: (CustomError | null)[] | null;
}

export declare type LmTeamPlayerDeleteResult = LazyLoading extends LazyLoadingDisabled ? EagerLmTeamPlayerDeleteResult : LazyLmTeamPlayerDeleteResult

export declare const LmTeamPlayerDeleteResult: (new (init: ModelInit<LmTeamPlayerDeleteResult>) => LmTeamPlayerDeleteResult)

type EagerLmTeamPlayerAddResult = {
  readonly status: string;
  readonly isExist?: boolean | null;
  readonly teamPlayer?: LmTeamPlayers | null;
  readonly coachPlayer?: LmCoachPlayers | null;
  readonly errors?: (CustomError | null)[] | null;
}

type LazyLmTeamPlayerAddResult = {
  readonly status: string;
  readonly isExist?: boolean | null;
  readonly teamPlayer: AsyncItem<LmTeamPlayers | undefined>;
  readonly coachPlayer: AsyncItem<LmCoachPlayers | undefined>;
  readonly errors?: (CustomError | null)[] | null;
}

export declare type LmTeamPlayerAddResult = LazyLoading extends LazyLoadingDisabled ? EagerLmTeamPlayerAddResult : LazyLmTeamPlayerAddResult

export declare const LmTeamPlayerAddResult: (new (init: ModelInit<LmTeamPlayerAddResult>) => LmTeamPlayerAddResult)

type EagerLmTeamDeleteResult = {
  readonly status: string;
  readonly teamDeleted?: LmTeam | null;
  readonly teamPlayersDeleted?: (LmTeamPlayers | null)[] | null;
  readonly coachPlayersNotAssigned?: (LmCoachPlayers | null)[] | null;
  readonly errors?: (CustomError | null)[] | null;
}

type LazyLmTeamDeleteResult = {
  readonly status: string;
  readonly teamDeleted: AsyncItem<LmTeam | undefined>;
  readonly teamPlayersDeleted: AsyncCollection<LmTeamPlayers>;
  readonly coachPlayersNotAssigned: AsyncCollection<LmCoachPlayers>;
  readonly errors?: (CustomError | null)[] | null;
}

export declare type LmTeamDeleteResult = LazyLoading extends LazyLoadingDisabled ? EagerLmTeamDeleteResult : LazyLmTeamDeleteResult

export declare const LmTeamDeleteResult: (new (init: ModelInit<LmTeamDeleteResult>) => LmTeamDeleteResult)

type EagerRangeCount = {
  readonly range: number;
  readonly count: number;
}

type LazyRangeCount = {
  readonly range: number;
  readonly count: number;
}

export declare type RangeCount = LazyLoading extends LazyLoadingDisabled ? EagerRangeCount : LazyRangeCount

export declare const RangeCount: (new (init: ModelInit<RangeCount>) => RangeCount)

type EagerTimeWindowCount = {
  readonly start: number;
  readonly end: number;
  readonly count: number;
}

type LazyTimeWindowCount = {
  readonly start: number;
  readonly end: number;
  readonly count: number;
}

export declare type TimeWindowCount = LazyLoading extends LazyLoadingDisabled ? EagerTimeWindowCount : LazyTimeWindowCount

export declare const TimeWindowCount: (new (init: ModelInit<TimeWindowCount>) => TimeWindowCount)

type EagerClubCount = {
  readonly clubId: string;
  readonly count: number;
}

type LazyClubCount = {
  readonly clubId: string;
  readonly count: number;
}

export declare type ClubCount = LazyLoading extends LazyLoadingDisabled ? EagerClubCount : LazyClubCount

export declare const ClubCount: (new (init: ModelInit<ClubCount>) => ClubCount)

type EagerClubAverage = {
  readonly clubId: string;
  readonly average: number;
}

type LazyClubAverage = {
  readonly clubId: string;
  readonly average: number;
}

export declare type ClubAverage = LazyLoading extends LazyLoadingDisabled ? EagerClubAverage : LazyClubAverage

export declare const ClubAverage: (new (init: ModelInit<ClubAverage>) => ClubAverage)

type EagerRegisterShadow = {
  readonly regUser: string;
  readonly prodRegDate: string;
}

type LazyRegisterShadow = {
  readonly regUser: string;
  readonly prodRegDate: string;
}

export declare type RegisterShadow = LazyLoading extends LazyLoadingDisabled ? EagerRegisterShadow : LazyRegisterShadow

export declare const RegisterShadow: (new (init: ModelInit<RegisterShadow>) => RegisterShadow)

type EagerGraphQLResult = {
  readonly status: string;
}

type LazyGraphQLResult = {
  readonly status: string;
}

export declare type GraphQLResult = LazyLoading extends LazyLoadingDisabled ? EagerGraphQLResult : LazyGraphQLResult

export declare const GraphQLResult: (new (init: ModelInit<GraphQLResult>) => GraphQLResult)

type EagerCustomError = {
  readonly errorType?: string | null;
  readonly message?: string | null;
}

type LazyCustomError = {
  readonly errorType?: string | null;
  readonly message?: string | null;
}

export declare type CustomError = LazyLoading extends LazyLoadingDisabled ? EagerCustomError : LazyCustomError

export declare const CustomError: (new (init: ModelInit<CustomError>) => CustomError)

type EagerGraphQLResultAuth = {
  readonly status: string;
  readonly errors?: (CustomError | null)[] | null;
}

type LazyGraphQLResultAuth = {
  readonly status: string;
  readonly errors?: (CustomError | null)[] | null;
}

export declare type GraphQLResultAuth = LazyLoading extends LazyLoadingDisabled ? EagerGraphQLResultAuth : LazyGraphQLResultAuth

export declare const GraphQLResultAuth: (new (init: ModelInit<GraphQLResultAuth>) => GraphQLResultAuth)

type EagerGraphQLDeleteResultAuth = {
  readonly status: string;
  readonly errors?: (CustomError | null)[] | null;
  readonly id?: string | null;
}

type LazyGraphQLDeleteResultAuth = {
  readonly status: string;
  readonly errors?: (CustomError | null)[] | null;
  readonly id?: string | null;
}

export declare type GraphQLDeleteResultAuth = LazyLoading extends LazyLoadingDisabled ? EagerGraphQLDeleteResultAuth : LazyGraphQLDeleteResultAuth

export declare const GraphQLDeleteResultAuth: (new (init: ModelInit<GraphQLDeleteResultAuth>) => GraphQLDeleteResultAuth)

type EagerLmDataSessionListResult = {
  readonly nextToken?: string | null;
  readonly items?: (LmDataSession | null)[] | null;
}

type LazyLmDataSessionListResult = {
  readonly nextToken?: string | null;
  readonly items: AsyncCollection<LmDataSession>;
}

export declare type LmDataSessionListResult = LazyLoading extends LazyLoadingDisabled ? EagerLmDataSessionListResult : LazyLmDataSessionListResult

export declare const LmDataSessionListResult: (new (init: ModelInit<LmDataSessionListResult>) => LmDataSessionListResult)

type EagerLmDataResultsListResult = {
  readonly nextToken?: string | null;
  readonly items?: (LmDataResults | null)[] | null;
}

type LazyLmDataResultsListResult = {
  readonly nextToken?: string | null;
  readonly items: AsyncCollection<LmDataResults>;
}

export declare type LmDataResultsListResult = LazyLoading extends LazyLoadingDisabled ? EagerLmDataResultsListResult : LazyLmDataResultsListResult

export declare const LmDataResultsListResult: (new (init: ModelInit<LmDataResultsListResult>) => LmDataResultsListResult)

type EagerLmShareSession = {
  readonly shareUrl?: string | null;
}

type LazyLmShareSession = {
  readonly shareUrl?: string | null;
}

export declare type LmShareSession = LazyLoading extends LazyLoadingDisabled ? EagerLmShareSession : LazyLmShareSession

export declare const LmShareSession: (new (init: ModelInit<LmShareSession>) => LmShareSession)

type LmUserMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type LmProfileMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type LmFeatureMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type LmDeviceMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type LmDataResultsMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type LmDataSessionMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type LmTeamPlayersMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type LmTeamCoachesMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type LmTeamMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type LmCoachPlayersMetaData = {
  readOnlyFields: 'createdAt' | 'updatedAt';
}

type EagerLmUser = {
  readonly id: string;
  readonly setupComplete: boolean;
  readonly email?: string | null;
  readonly phone?: string | null;
  readonly fullName?: string | null;
  readonly profileImage?: string | null;
  readonly userType?: string | null;
  readonly gender?: string | null;
  readonly handedness?: LmHandedness | keyof typeof LmHandedness | null;
  readonly birthdate?: number | null;
  readonly companyName?: string | null;
  readonly shippingAddressLine1?: string | null;
  readonly shippingAddressLine2?: string | null;
  readonly shippingPostcode?: string | null;
  readonly shippingLocality?: string | null;
  readonly shippingRegion?: string | null;
  readonly shippingCountry?: string | null;
  readonly subscriptions?: string | null;
  readonly competitiveLevel?: CompetitiveLevel | keyof typeof CompetitiveLevel | null;
  readonly teamName?: string | null;
  readonly organizationSchool?: string | null;
  readonly baseballPlayerPosition?: string | null;
  readonly coachedTeams?: (LmTeamCoaches | null)[] | null;
  readonly playerTeams?: (LmTeamPlayers | null)[] | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmUser = {
  readonly id: string;
  readonly setupComplete: boolean;
  readonly email?: string | null;
  readonly phone?: string | null;
  readonly fullName?: string | null;
  readonly profileImage?: string | null;
  readonly userType?: string | null;
  readonly gender?: string | null;
  readonly handedness?: LmHandedness | keyof typeof LmHandedness | null;
  readonly birthdate?: number | null;
  readonly companyName?: string | null;
  readonly shippingAddressLine1?: string | null;
  readonly shippingAddressLine2?: string | null;
  readonly shippingPostcode?: string | null;
  readonly shippingLocality?: string | null;
  readonly shippingRegion?: string | null;
  readonly shippingCountry?: string | null;
  readonly subscriptions?: string | null;
  readonly competitiveLevel?: CompetitiveLevel | keyof typeof CompetitiveLevel | null;
  readonly teamName?: string | null;
  readonly organizationSchool?: string | null;
  readonly baseballPlayerPosition?: string | null;
  readonly coachedTeams: AsyncCollection<LmTeamCoaches>;
  readonly playerTeams: AsyncCollection<LmTeamPlayers>;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmUser = LazyLoading extends LazyLoadingDisabled ? EagerLmUser : LazyLmUser

export declare const LmUser: (new (init: ModelInit<LmUser, LmUserMetaData>) => LmUser) & {
  copyOf(source: LmUser, mutator: (draft: MutableModel<LmUser, LmUserMetaData>) => MutableModel<LmUser, LmUserMetaData> | void): LmUser;
}

type EagerLmProfile = {
  readonly id: string;
  readonly userId: string;
  readonly setupComplete: boolean;
  readonly showDevicePlacement?: boolean | null;
  readonly videoCapture?: boolean | null;
  readonly normalized?: boolean | null;
  readonly normalizedElevation?: number | null;
  readonly normalizedTemperature?: number | null;
  readonly normalizedBallType?: LmBallType | keyof typeof LmBallType | null;
  readonly normalizedOutdoorBallType?: LmBallType | keyof typeof LmBallType | null;
  readonly distanceUnits?: LmDistanceUnits | keyof typeof LmDistanceUnits | null;
  readonly speedUnits?: LmSpeedUnits | keyof typeof LmSpeedUnits | null;
  readonly apexUnits?: LmDistanceUnits | keyof typeof LmDistanceUnits | null;
  readonly elevationUnits?: LmDistanceUnits | keyof typeof LmDistanceUnits | null;
  readonly temperatureUnits?: LmTemperatureUnits | keyof typeof LmTemperatureUnits | null;
  readonly deviceDisplayMode?: LmDisplayMode | keyof typeof LmDisplayMode | null;
  readonly language?: string | null;
  readonly displayMode?: LmDisplayMode | keyof typeof LmDisplayMode | null;
  readonly theme?: LmTheme | keyof typeof LmTheme | null;
  readonly chosenPanel?: number | null;
  readonly recordClubs?: boolean | null;
  readonly useTileRangeView?: boolean | null;
  readonly targetOption?: LmTargetOption | keyof typeof LmTargetOption | null;
  readonly cameraView?: LmCameraView | keyof typeof LmCameraView | null;
  readonly ballPath?: LmBallPath | keyof typeof LmBallPath | null;
  readonly appTiles?: (ValueType | null)[] | Array<keyof typeof ValueType> | null;
  readonly watchTiles?: (ValueType | null)[] | Array<keyof typeof ValueType> | null;
  readonly deviceTiles?: (ValueType | null)[] | Array<keyof typeof ValueType> | null;
  readonly appTilesEnabled?: (boolean | null)[] | null;
  readonly watchTilesEnabled?: (boolean | null)[] | null;
  readonly baseballAppData?: (BaseBallValueType | null)[] | Array<keyof typeof BaseBallValueType> | null;
  readonly baseballAppTilesEnabled?: (boolean | null)[] | null;
  readonly baseballDeviceTiles?: (BaseBallValueType | null)[] | Array<keyof typeof BaseBallValueType> | null;
  readonly baseballWatchTiles?: (BaseBallValueType | null)[] | Array<keyof typeof BaseBallValueType> | null;
  readonly clubSpeedAudio?: boolean | null;
  readonly ballSpeedAudio?: boolean | null;
  readonly smashFactorAudio?: boolean | null;
  readonly attackAngleAudio?: boolean | null;
  readonly clubPathAudio?: boolean | null;
  readonly launchAngleAudio?: boolean | null;
  readonly horizontalLaunchAngleAudio?: boolean | null;
  readonly faceAngleAudio?: boolean | null;
  readonly spinRateAudio?: boolean | null;
  readonly spinAxisAudio?: boolean | null;
  readonly carryDistanceAudio?: boolean | null;
  readonly totalDistanceAudio?: boolean | null;
  readonly sideAudio?: boolean | null;
  readonly sideTotalAudio?: boolean | null;
  readonly apexAudio?: boolean | null;
  readonly features?: (LmFeature | null)[] | null;
  readonly devices?: (LmDevice | null)[] | null;
  readonly dataSessions?: (LmDataSession | null)[] | null;
  readonly user?: LmUser | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmProfile = {
  readonly id: string;
  readonly userId: string;
  readonly setupComplete: boolean;
  readonly showDevicePlacement?: boolean | null;
  readonly videoCapture?: boolean | null;
  readonly normalized?: boolean | null;
  readonly normalizedElevation?: number | null;
  readonly normalizedTemperature?: number | null;
  readonly normalizedBallType?: LmBallType | keyof typeof LmBallType | null;
  readonly normalizedOutdoorBallType?: LmBallType | keyof typeof LmBallType | null;
  readonly distanceUnits?: LmDistanceUnits | keyof typeof LmDistanceUnits | null;
  readonly speedUnits?: LmSpeedUnits | keyof typeof LmSpeedUnits | null;
  readonly apexUnits?: LmDistanceUnits | keyof typeof LmDistanceUnits | null;
  readonly elevationUnits?: LmDistanceUnits | keyof typeof LmDistanceUnits | null;
  readonly temperatureUnits?: LmTemperatureUnits | keyof typeof LmTemperatureUnits | null;
  readonly deviceDisplayMode?: LmDisplayMode | keyof typeof LmDisplayMode | null;
  readonly language?: string | null;
  readonly displayMode?: LmDisplayMode | keyof typeof LmDisplayMode | null;
  readonly theme?: LmTheme | keyof typeof LmTheme | null;
  readonly chosenPanel?: number | null;
  readonly recordClubs?: boolean | null;
  readonly useTileRangeView?: boolean | null;
  readonly targetOption?: LmTargetOption | keyof typeof LmTargetOption | null;
  readonly cameraView?: LmCameraView | keyof typeof LmCameraView | null;
  readonly ballPath?: LmBallPath | keyof typeof LmBallPath | null;
  readonly appTiles?: (ValueType | null)[] | Array<keyof typeof ValueType> | null;
  readonly watchTiles?: (ValueType | null)[] | Array<keyof typeof ValueType> | null;
  readonly deviceTiles?: (ValueType | null)[] | Array<keyof typeof ValueType> | null;
  readonly appTilesEnabled?: (boolean | null)[] | null;
  readonly watchTilesEnabled?: (boolean | null)[] | null;
  readonly baseballAppData?: (BaseBallValueType | null)[] | Array<keyof typeof BaseBallValueType> | null;
  readonly baseballAppTilesEnabled?: (boolean | null)[] | null;
  readonly baseballDeviceTiles?: (BaseBallValueType | null)[] | Array<keyof typeof BaseBallValueType> | null;
  readonly baseballWatchTiles?: (BaseBallValueType | null)[] | Array<keyof typeof BaseBallValueType> | null;
  readonly clubSpeedAudio?: boolean | null;
  readonly ballSpeedAudio?: boolean | null;
  readonly smashFactorAudio?: boolean | null;
  readonly attackAngleAudio?: boolean | null;
  readonly clubPathAudio?: boolean | null;
  readonly launchAngleAudio?: boolean | null;
  readonly horizontalLaunchAngleAudio?: boolean | null;
  readonly faceAngleAudio?: boolean | null;
  readonly spinRateAudio?: boolean | null;
  readonly spinAxisAudio?: boolean | null;
  readonly carryDistanceAudio?: boolean | null;
  readonly totalDistanceAudio?: boolean | null;
  readonly sideAudio?: boolean | null;
  readonly sideTotalAudio?: boolean | null;
  readonly apexAudio?: boolean | null;
  readonly features: AsyncCollection<LmFeature>;
  readonly devices: AsyncCollection<LmDevice>;
  readonly dataSessions: AsyncCollection<LmDataSession>;
  readonly user: AsyncItem<LmUser | undefined>;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmProfile = LazyLoading extends LazyLoadingDisabled ? EagerLmProfile : LazyLmProfile

export declare const LmProfile: (new (init: ModelInit<LmProfile, LmProfileMetaData>) => LmProfile) & {
  copyOf(source: LmProfile, mutator: (draft: MutableModel<LmProfile, LmProfileMetaData>) => MutableModel<LmProfile, LmProfileMetaData> | void): LmProfile;
}

type EagerLmFeature = {
  readonly id: string;
  readonly lmProfileId: string;
  readonly owner?: string | null;
  readonly featureName?: string | null;
  readonly enabled?: boolean | null;
  readonly expiresAt?: string | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmFeature = {
  readonly id: string;
  readonly lmProfileId: string;
  readonly owner?: string | null;
  readonly featureName?: string | null;
  readonly enabled?: boolean | null;
  readonly expiresAt?: string | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmFeature = LazyLoading extends LazyLoadingDisabled ? EagerLmFeature : LazyLmFeature

export declare const LmFeature: (new (init: ModelInit<LmFeature, LmFeatureMetaData>) => LmFeature) & {
  copyOf(source: LmFeature, mutator: (draft: MutableModel<LmFeature, LmFeatureMetaData>) => MutableModel<LmFeature, LmFeatureMetaData> | void): LmFeature;
}

type EagerLmDevice = {
  readonly id: string;
  readonly lmProfileId: string;
  readonly owner?: string | null;
  readonly deviceID?: string | null;
  readonly advertisementName?: string | null;
  readonly connectID?: string | null;
  readonly configuredWifiSSID?: string | null;
  readonly serialNumber?: string | null;
  readonly modelNumber?: string | null;
  readonly firmwareVersion?: string | null;
  readonly autoConnect?: boolean | null;
  readonly registered?: boolean | null;
  readonly registeredUser?: string | null;
  readonly registrationDate?: number | null;
  readonly registrationReminder?: boolean | null;
  readonly registrationReminderTime?: number | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmDevice = {
  readonly id: string;
  readonly lmProfileId: string;
  readonly owner?: string | null;
  readonly deviceID?: string | null;
  readonly advertisementName?: string | null;
  readonly connectID?: string | null;
  readonly configuredWifiSSID?: string | null;
  readonly serialNumber?: string | null;
  readonly modelNumber?: string | null;
  readonly firmwareVersion?: string | null;
  readonly autoConnect?: boolean | null;
  readonly registered?: boolean | null;
  readonly registeredUser?: string | null;
  readonly registrationDate?: number | null;
  readonly registrationReminder?: boolean | null;
  readonly registrationReminderTime?: number | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmDevice = LazyLoading extends LazyLoadingDisabled ? EagerLmDevice : LazyLmDevice

export declare const LmDevice: (new (init: ModelInit<LmDevice, LmDeviceMetaData>) => LmDevice) & {
  copyOf(source: LmDevice, mutator: (draft: MutableModel<LmDevice, LmDeviceMetaData>) => MutableModel<LmDevice, LmDeviceMetaData> | void): LmDevice;
}

type EagerLmDataResults = {
  readonly id: string;
  readonly resultId?: string | null;
  readonly sequenceNumber?: number | null;
  readonly lmSessionId: string;
  readonly owner?: string | null;
  readonly sport?: LmSportType | keyof typeof LmSportType | null;
  readonly readers?: (string | null)[] | null;
  readonly timestamp: number;
  readonly isFavorite?: boolean | null;
  readonly baseballResults?: LmBaseballResults | null;
  readonly videoKey?: string | null;
  readonly pointCloudKey?: string | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmDataResults = {
  readonly id: string;
  readonly resultId?: string | null;
  readonly sequenceNumber?: number | null;
  readonly lmSessionId: string;
  readonly owner?: string | null;
  readonly sport?: LmSportType | keyof typeof LmSportType | null;
  readonly readers?: (string | null)[] | null;
  readonly timestamp: number;
  readonly isFavorite?: boolean | null;
  readonly baseballResults?: LmBaseballResults | null;
  readonly videoKey?: string | null;
  readonly pointCloudKey?: string | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmDataResults = LazyLoading extends LazyLoadingDisabled ? EagerLmDataResults : LazyLmDataResults

export declare const LmDataResults: (new (init: ModelInit<LmDataResults, LmDataResultsMetaData>) => LmDataResults) & {
  copyOf(source: LmDataResults, mutator: (draft: MutableModel<LmDataResults, LmDataResultsMetaData>) => MutableModel<LmDataResults, LmDataResultsMetaData> | void): LmDataResults;
}

type EagerLmDataSession = {
  readonly id: string;
  readonly deviceID?: string | null;
  readonly owner?: string | null;
  readonly sport?: LmSportType | keyof typeof LmSportType | null;
  readonly startTimestamp: number;
  readonly endTimestamp: number;
  readonly duration?: number | null;
  readonly readers?: (string | null)[] | null;
  readonly name?: string | null;
  readonly details?: string | null;
  readonly city?: string | null;
  readonly state?: string | null;
  readonly country?: string | null;
  readonly address?: string | null;
  readonly locationName?: string | null;
  readonly elevation?: number | null;
  readonly latitude?: number | null;
  readonly longitude?: number | null;
  readonly temperature?: number | null;
  readonly humidity?: number | null;
  readonly location?: LmLocation | keyof typeof LmLocation | null;
  readonly playMode?: LmDataSessionPlayMode | keyof typeof LmDataSessionPlayMode | null;
  readonly results?: (LmDataResults | null)[] | null;
  readonly profile?: LmProfile | null;
  readonly team?: LmTeam | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmDataSession = {
  readonly id: string;
  readonly deviceID?: string | null;
  readonly owner?: string | null;
  readonly sport?: LmSportType | keyof typeof LmSportType | null;
  readonly startTimestamp: number;
  readonly endTimestamp: number;
  readonly duration?: number | null;
  readonly readers?: (string | null)[] | null;
  readonly name?: string | null;
  readonly details?: string | null;
  readonly city?: string | null;
  readonly state?: string | null;
  readonly country?: string | null;
  readonly address?: string | null;
  readonly locationName?: string | null;
  readonly elevation?: number | null;
  readonly latitude?: number | null;
  readonly longitude?: number | null;
  readonly temperature?: number | null;
  readonly humidity?: number | null;
  readonly location?: LmLocation | keyof typeof LmLocation | null;
  readonly playMode?: LmDataSessionPlayMode | keyof typeof LmDataSessionPlayMode | null;
  readonly results: AsyncCollection<LmDataResults>;
  readonly profile: AsyncItem<LmProfile | undefined>;
  readonly team: AsyncItem<LmTeam | undefined>;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmDataSession = LazyLoading extends LazyLoadingDisabled ? EagerLmDataSession : LazyLmDataSession

export declare const LmDataSession: (new (init: ModelInit<LmDataSession, LmDataSessionMetaData>) => LmDataSession) & {
  copyOf(source: LmDataSession, mutator: (draft: MutableModel<LmDataSession, LmDataSessionMetaData>) => MutableModel<LmDataSession, LmDataSessionMetaData> | void): LmDataSession;
}

type EagerLmTeamPlayers = {
  readonly id: string;
  readonly owner?: string | null;
  readonly readers?: (string | null)[] | null;
  readonly type?: LmTeamPlayerType | keyof typeof LmTeamPlayerType | null;
  readonly status?: LmTeamPlayerStatus | keyof typeof LmTeamPlayerStatus | null;
  readonly email?: string | null;
  readonly joined_at?: string | null;
  readonly lmPlayerId?: string | null;
  readonly lmPlayerName?: string | null;
  readonly lineupOrderBatting?: number | null;
  readonly lineupOrderPitching?: number | null;
  readonly team?: LmTeam | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmTeamPlayers = {
  readonly id: string;
  readonly owner?: string | null;
  readonly readers?: (string | null)[] | null;
  readonly type?: LmTeamPlayerType | keyof typeof LmTeamPlayerType | null;
  readonly status?: LmTeamPlayerStatus | keyof typeof LmTeamPlayerStatus | null;
  readonly email?: string | null;
  readonly joined_at?: string | null;
  readonly lmPlayerId?: string | null;
  readonly lmPlayerName?: string | null;
  readonly lineupOrderBatting?: number | null;
  readonly lineupOrderPitching?: number | null;
  readonly team: AsyncItem<LmTeam | undefined>;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmTeamPlayers = LazyLoading extends LazyLoadingDisabled ? EagerLmTeamPlayers : LazyLmTeamPlayers

export declare const LmTeamPlayers: (new (init: ModelInit<LmTeamPlayers, LmTeamPlayersMetaData>) => LmTeamPlayers) & {
  copyOf(source: LmTeamPlayers, mutator: (draft: MutableModel<LmTeamPlayers, LmTeamPlayersMetaData>) => MutableModel<LmTeamPlayers, LmTeamPlayersMetaData> | void): LmTeamPlayers;
}

type EagerLmTeamCoaches = {
  readonly id: string;
  readonly owner?: string | null;
  readonly lmTeamId?: string | null;
  readonly lmCoachId?: string | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmTeamCoaches = {
  readonly id: string;
  readonly owner?: string | null;
  readonly lmTeamId?: string | null;
  readonly lmCoachId?: string | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmTeamCoaches = LazyLoading extends LazyLoadingDisabled ? EagerLmTeamCoaches : LazyLmTeamCoaches

export declare const LmTeamCoaches: (new (init: ModelInit<LmTeamCoaches, LmTeamCoachesMetaData>) => LmTeamCoaches) & {
  copyOf(source: LmTeamCoaches, mutator: (draft: MutableModel<LmTeamCoaches, LmTeamCoachesMetaData>) => MutableModel<LmTeamCoaches, LmTeamCoachesMetaData> | void): LmTeamCoaches;
}

type EagerLmTeam = {
  readonly id: string;
  readonly owner?: string | null;
  readonly name?: string | null;
  readonly profileImage?: string | null;
  readonly pitcherId?: string | null;
  readonly readers?: (string | null)[] | null;
  readonly players?: (LmTeamPlayers | null)[] | null;
  readonly coaches?: (LmTeamCoaches | null)[] | null;
  readonly dataSessions?: (LmDataSession | null)[] | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmTeam = {
  readonly id: string;
  readonly owner?: string | null;
  readonly name?: string | null;
  readonly profileImage?: string | null;
  readonly pitcherId?: string | null;
  readonly readers?: (string | null)[] | null;
  readonly players: AsyncCollection<LmTeamPlayers>;
  readonly coaches: AsyncCollection<LmTeamCoaches>;
  readonly dataSessions: AsyncCollection<LmDataSession>;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmTeam = LazyLoading extends LazyLoadingDisabled ? EagerLmTeam : LazyLmTeam

export declare const LmTeam: (new (init: ModelInit<LmTeam, LmTeamMetaData>) => LmTeam) & {
  copyOf(source: LmTeam, mutator: (draft: MutableModel<LmTeam, LmTeamMetaData>) => MutableModel<LmTeam, LmTeamMetaData> | void): LmTeam;
}

type EagerLmCoachPlayers = {
  readonly id: string;
  readonly lmCoachId: string;
  readonly owner?: string | null;
  readonly lmPlayerId?: string | null;
  readonly lmPlayerName?: string | null;
  readonly status?: LmTeamPlayerStatus | keyof typeof LmTeamPlayerStatus | null;
  readonly lmPlayerEmail?: string | null;
  readonly teamAssigned?: LmCoachPlayersTeamAssigned | keyof typeof LmCoachPlayersTeamAssigned | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

type LazyLmCoachPlayers = {
  readonly id: string;
  readonly lmCoachId: string;
  readonly owner?: string | null;
  readonly lmPlayerId?: string | null;
  readonly lmPlayerName?: string | null;
  readonly status?: LmTeamPlayerStatus | keyof typeof LmTeamPlayerStatus | null;
  readonly lmPlayerEmail?: string | null;
  readonly teamAssigned?: LmCoachPlayersTeamAssigned | keyof typeof LmCoachPlayersTeamAssigned | null;
  readonly createdAt?: string | null;
  readonly updatedAt?: string | null;
}

export declare type LmCoachPlayers = LazyLoading extends LazyLoadingDisabled ? EagerLmCoachPlayers : LazyLmCoachPlayers

export declare const LmCoachPlayers: (new (init: ModelInit<LmCoachPlayers, LmCoachPlayersMetaData>) => LmCoachPlayers) & {
  copyOf(source: LmCoachPlayers, mutator: (draft: MutableModel<LmCoachPlayers, LmCoachPlayersMetaData>) => MutableModel<LmCoachPlayers, LmCoachPlayersMetaData> | void): LmCoachPlayers;
}