{"AWSTemplateFormatVersion": "2010-09-09", "Description": "Lambda Function resource stack creation using Amplify CLI", "Parameters": {"CloudWatchRule": {"Type": "String", "Default": "NONE", "Description": " Schedule Expression"}, "deploymentBucketName": {"Type": "String"}, "env": {"Type": "String"}, "s3Key": {"Type": "String"}, "apifullswingflightGraphQLAPIIdOutput": {"Type": "String", "Default": "apifullswingflightGraphQLAPIIdOutput"}, "apifullswingflightGraphQLAPIEndpointOutput": {"Type": "String", "Default": "apifullswingflightGraphQLAPIEndpointOutput"}, "functionfullswingflightstatsSharedArn": {"Type": "String", "Default": "functionfullswingflightstatsSharedArn"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "userstats", {"Fn::Join": ["", ["userstats", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "REGION": {"Ref": "AWS::Region"}, "API_FULLSWINGFLIGHT_LMUSERSTATSTABLE_NAME": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmUserStatsTable:Name"}}, "API_FULLSWINGFLIGHT_LMUSERSTATSTABLE_ARN": {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmUserStatsTable:Name"}}]]}, "API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT": {"Ref": "apifullswingflightGraphQLAPIIdOutput"}, "API_FULLSWINGFLIGHT_LMSESSIONTABLE_NAME": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmSessionTable:Name"}}, "API_FULLSWINGFLIGHT_LMSESSIONTABLE_ARN": {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmSessionTable:Name"}}]]}, "API_FULLSWINGFLIGHT_LMPROFILETABLE_NAME": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmProfileTable:Name"}}, "API_FULLSWINGFLIGHT_LMPROFILETABLE_ARN": {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmProfileTable:Name"}}]]}, "API_FULLSWINGFLIGHT_LMDEVICETABLE_NAME": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmDeviceTable:Name"}}, "API_FULLSWINGFLIGHT_LMDEVICETABLE_ARN": {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmDeviceTable:Name"}}]]}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs18.x", "Layers": [{"Ref": "functionfullswingflightstatsSharedArn"}], "Timeout": "25"}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "fullswingflightLambdaRole8ec85564", {"Fn::Join": ["", ["fullswingflightLambdaRole8ec85564", "-", {"Ref": "env"}]]}]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}]}}}, "AmplifyResourcesPolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "amplify-lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["dynamodb:Get*", "dynamodb:BatchGetItem", "dynamodb:List*", "dynamodb:Describe*", "dynamodb:<PERSON><PERSON>", "dynamodb:Query"], "Resource": [{"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmProfileTable:Name"}}]]}, {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmProfileTable:Name"}}, "/index/*"]]}]}, {"Effect": "Allow", "Action": ["dynamodb:Get*", "dynamodb:BatchGetItem", "dynamodb:List*", "dynamodb:Describe*", "dynamodb:<PERSON><PERSON>", "dynamodb:Query"], "Resource": [{"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmDeviceTable:Name"}}]]}, {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmDeviceTable:Name"}}, "/index/*"]]}]}, {"Effect": "Allow", "Action": ["dynamodb:Get*", "dynamodb:BatchGetItem", "dynamodb:List*", "dynamodb:Describe*", "dynamodb:<PERSON><PERSON>", "dynamodb:Query"], "Resource": [{"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmSessionTable:Name"}}]]}, {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmSessionTable:Name"}}, "/index/*"]]}]}, {"Effect": "Allow", "Action": ["dynamodb:Put*", "dynamodb:Create*", "dynamodb:BatchWriteItem", "dynamodb:Get*", "dynamodb:BatchGetItem", "dynamodb:List*", "dynamodb:Describe*", "dynamodb:<PERSON><PERSON>", "dynamodb:Query", "dynamodb:Update*", "dynamodb:RestoreTable*"], "Resource": [{"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmUserStatsTable:Name"}}]]}, {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmUserStatsTable:Name"}}, "/index/*"]]}]}]}}}, "LambdaTriggerPolicyLmSession": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "amplify-lambda-execution-policy-LmSession", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["dynamodb:DescribeStream", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:ListStreams"], "Resource": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmSessionTable:StreamArn"}}}]}}}, "LambdaEventSourceMappingLmSession": {"Type": "AWS::Lambda::EventSourceMapping", "DependsOn": ["LambdaTriggerPolicyLmSession", "LambdaExecutionRole"], "Properties": {"BatchSize": 1, "Enabled": true, "EventSourceArn": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmSessionTable:StreamArn"}}, "FunctionName": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}, "StartingPosition": "LATEST"}}, "LambdaTriggerPolicyLmShot": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "amplify-lambda-execution-policy-LmShot", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["dynamodb:DescribeStream", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:ListStreams"], "Resource": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmShotTable:StreamArn"}}}]}}}, "LambdaEventSourceMappingLmShot": {"Type": "AWS::Lambda::EventSourceMapping", "DependsOn": ["LambdaTriggerPolicyLmShot", "LambdaExecutionRole"], "Properties": {"BatchSize": 1, "Enabled": true, "EventSourceArn": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmShotTable:StreamArn"}}, "FunctionName": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}, "StartingPosition": "LATEST"}}, "LambdaTriggerPolicyLmClub": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "amplify-lambda-execution-policy-LmClub", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["dynamodb:DescribeStream", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:ListStreams"], "Resource": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmClubTable:StreamArn"}}}]}}}, "LambdaEventSourceMappingLmClub": {"Type": "AWS::Lambda::EventSourceMapping", "DependsOn": ["LambdaTriggerPolicyLmClub", "LambdaExecutionRole"], "Properties": {"BatchSize": 1, "Enabled": true, "EventSourceArn": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmClubTable:StreamArn"}}, "FunctionName": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}, "StartingPosition": "LATEST"}}, "LambdaTriggerPolicyLmDevice": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "amplify-lambda-execution-policy-LmDevice", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["dynamodb:DescribeStream", "dynamodb:GetRecords", "dynamodb:GetShardIterator", "dynamodb:ListStreams"], "Resource": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmDeviceTable:StreamArn"}}}]}}}, "LambdaEventSourceMappingLmDevice": {"Type": "AWS::Lambda::EventSourceMapping", "DependsOn": ["LambdaTriggerPolicyLmDevice", "LambdaExecutionRole"], "Properties": {"BatchSize": 1, "Enabled": true, "EventSourceArn": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmDeviceTable:StreamArn"}}, "FunctionName": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}, "StartingPosition": "LATEST"}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}}}