/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	API_FULLSWINGFLIGHT_LMPROFILETABLE_ARN
	API_FULLSWINGFLIGHT_LMPROFILETABLE_NAME
	API_FULLSWINGFLIGHT_LMSESSIONTABLE_ARN
	API_FULLSWINGFLIGHT_LMSESSIONTABLE_NAME
	API_FULLSWINGFLIGHT_LMUSERSTATSTABLE_ARN
	API_FULLSWINGFLIGHT_LMUSERSTATSTABLE_NAME
	ENV
	REGION
Amplify Params - DO NOT EDIT */


const AWS = require('aws-sdk');
const crypto = require('crypto');
//const agent = new http.Agent({ keepAlive: true })
//const dynamodb = new AWS.DynamoDB({ httpOptions: { agent } })
//const docClient = new AWS.DynamoDB.DocumentClient({ httpOptions: { agent } })
const docClient = new AWS.DynamoDB.DocumentClient()

/* Helper Functions */

/* AWS DynamoDB Functions */

async function getUserStatsId(userId) {
    var statqparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMUSERSTATSTABLE_NAME,
        Limit: 1,
        IndexName: 'byUser',
        KeyConditionExpression: '#owner = :userId',
        ProjectionExpression: 'id',
        ExpressionAttributeValues: { 
            ':userId': userId
        },
        ExpressionAttributeNames: { 
            '#owner': 'owner'
        },
    };
    const statsresponse = await docClient.query(statqparams).promise();
    if (statsresponse.Items.length > 1) 
        console.log('User Stats Found multiple for user: ', userId);
    if (statsresponse.Items.length > 0) 
        return statsresponse.Items[0].id;

    //console.log('User Stats Not Found: ', userId);
    return crypto.randomUUID();
}

async function getProfileIdByUser(userId) {
    var qparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMPROFILETABLE_NAME,
        Limit: 1,
        IndexName: 'byUser',
        KeyConditionExpression: '#userId = :userId',
        ProjectionExpression: 'id',
        ExpressionAttributeValues: { 
            ':userId': userId
        },
        ExpressionAttributeNames: { 
            '#userId': 'userId'
        },
    };
    const response = await docClient.query(qparams).promise();
    if (response.Items.length > 0) 
        return response.Items[0].id;

    console.log('Profile Not Found for owner: ', userId);
    return null;
}

function getUserStatsId(userId, lmProfileId) {
    var userIdParts = userId.split('-');
    var lmProfileIdParts = lmProfileId.split('-');
    return `${userIdParts[0]}-${userIdParts[1]}-${userIdParts[2]}-${lmProfileIdParts[3]}-${lmProfileIdParts[4]}`;
}

async function updateUserStats(owner, lmProfileId, attribute, incCount = 1) { 
    var timestamp = new Date();
    var timeStr = `${timestamp.getFullYear()}-${(timestamp.getMonth()+1).toString().padStart(2,'0')}-${timestamp.getDate().toString().padStart(2,'0')}T${timestamp.getHours().toString().padStart(2,'0')}:${timestamp.getMinutes().toString().padStart(2,'0')}:${timestamp.getSeconds().toString().padStart(2,'0')}.${timestamp.getMilliseconds()}Z`;
    const statsId = getUserStatsId(owner, lmProfileId);
    var attrname = `#${attribute}`
    var statuparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMUSERSTATSTABLE_NAME,
        Key: {
            id: statsId,
        },
        UpdateExpression: `SET ${attrname} = if_not_exists(${attrname}, :start) + :countInc, 
                    #owner = :owner,
                    #lmProfileId = :lmProfileId, 
                    #updatedAt = :updatedAt, 
                    #createdAt = if_not_exists(#createdAt, :createdAt),
                    #typename = if_not_exists(#typename, :typename), 
                    #lastchangedat = :lastchangedat, 
                    #version = if_not_exists(#version, :version)`,
        ExpressionAttributeNames: {
            [attrname]: attribute,
            '#owner': 'owner',
            '#lmProfileId': 'lmProfileId',
            '#updatedAt': 'updatedAt',
            '#createdAt': 'createdAt',
            '#typename': '__typename',
            '#lastchangedat': '_lastChangedAt',
            '#version': '_version'
        },
        ExpressionAttributeValues: {
            ':start': 0,
            ':countInc': incCount,
            ':lmProfileId': lmProfileId,
            ':owner': owner,
            ':updatedAt': timeStr,
            ':createdAt': timeStr,
            ':typename': 'LmUserStats',
            ':lastchangedat': timestamp.getTime(),
            ':version': 1,
        },
    };
    //console.log('Updating Stats: ', JSON.stringify(statuparams, null, 2));
    await docClient.update(statuparams).promise();
}

async function syncInsertRecords(unmarshalledRecords) {
    for await (let record of unmarshalledRecords) {
        console.log('DynamoDB Record: ', JSON.stringify(record, null, 2));

        if (record.__typename == 'LmSession') {
            console.log('Updating Stats with Session: ', record.owner, record.id);
            await updateUserStats(record.owner, record.lmProfileId, 'sessionCount');
        } else
        if (record.__typename == 'LmClub') {
            console.log('Updating Stats with Club: ', record.owner, record.id);
            await updateUserStats(record.owner, record.lmProfileId, 'clubCount');
        } else
        if (record.__typename == 'LmShot') {
            console.log('Updating Stats with Shot: ', record.owner, record.id);
            const profileId = await getProfileIdByUser(record.owner);
            if (profileId === null) return;
            console.log('updateUserStatsWithShot Fetched profile Id: ', profileId);
            await updateUserStats(record.owner, profileId, 'shotCount');
        } else
        if (record.__typename == 'LmDevice') {
            console.log('Updating Stats with Device: ', record.owner, record.id);
            console.log('LmDevice: ', record.owner, record.registeredUser);
            if (record.registered && record.owner == record.registeredUser) 
                await updateUserStats(record.owner, record.lmProfileId, 'registeredDeviceCount');
            await updateUserStats(record.owner, record.lmProfileId, 'deviceCount');
        } else {
            console.log('Unknown INSERT Record: ', JSON.stringify(record, null, 2));
        }
    }
}

async function syncDeleteRecords(unmarshalledRecords) {
    for await (let record of unmarshalledRecords) {
        console.log('DynamoDB Delete Record: ', JSON.stringify(record, null, 2));

        if (record.__typename == 'LmSession') {
            console.log('Updating Stats with Session: ', record.owner, record.id);
            await updateUserStats(record.owner, record.lmProfileId, 'sessionCount', -1);
        } else
        if (record.__typename == 'LmClub') {
            console.log('Updating Stats with Club: ', record.owner, record.id);
            await updateUserStats(record.owner, record.lmProfileId, 'clubCount', -1);
        } else
        if (record.__typename == 'LmShot') {
            console.log('Updating Stats with Shot: ', record.owner, record.id);
            const profileId = await getProfileIdByUser(record.owner);
            if (profileId === null) return;
            console.log('updateUserStatsWithShot Fetched profile Id: ', profileId);
            await updateUserStats(record.owner, profileId, 'shotCount', -1);
        } else
        if (record.__typename == 'LmDevice') {
            console.log('Updating Stats with Device: ', record.owner, record.id);
            console.log('LmDevice: ', record.owner, record.registeredUser);
            if (record.registered && record.owner == record.registeredUser) 
                await updateUserStats(record.owner, record.lmProfileId, 'registeredDeviceCount', -1);
            await updateUserStats(record.owner, record.lmProfileId, 'deviceCount', -1);
        } else {
            console.log('Unknown DELETE Record: ', JSON.stringify(record, null, 2));
        }
    }
}

exports.handler = async (event, context, callback) => {
    //eslint-disable-line
    console.log(JSON.stringify(event, null, 2));
    
    // New INSERT 
    const insertRecords = event.Records.filter(record => record.eventName == 'INSERT' );
    const unmarshalledInsRecords = insertRecords.map(record => 
        AWS.DynamoDB.Converter.unmarshall(record.dynamodb.NewImage)
    );
    
    // MODIFY
    const modRecords = event.Records.filter(record => record.eventName == 'MODIFY' );
    const unmarshalledModRecords = modRecords.map(record => 
        AWS.DynamoDB.Converter.unmarshall(record.dynamodb.NewImage)
    );

    // MODIFY New Image
    const unmarshalledModAddRecords = unmarshalledModRecords.filter(record => !record.hasOwnProperty('_deleted') || record._deleted == false );
    const modAddRecordIds = unmarshalledModAddRecords.map(record =>
        record.id
    );

    // MODIFY Old Image
    const unmarshalledModDelRecords = modRecords.map(record => 
        AWS.DynamoDB.Converter.unmarshall(record.dynamodb.OldImage)
    ).filter(record => modAddRecordIds.includes(record.id));

    // REMOVE 
    const remRecords = event.Records.filter(record => record.eventName == 'REMOVE' );
    const unmarshalledRemRecords = remRecords.map(record => 
        AWS.DynamoDB.Converter.unmarshall(record.dynamodb.OldImage)
    );
    
    // MODIFY New Deleted
    const unmarshalledDelRecords = unmarshalledModRecords.filter(record => record._deleted == true );
    
    console.log('DynamoDB Records Insert: ', unmarshalledInsRecords.length,
        'MODIFY: ', unmarshalledModRecords.length,
    //  'MODIFY add: ', unmarshalledModAddRecords.length,
    //  'MODIFY del: ', unmarshalledModDelRecords.length,
        'REMOVE: ', unmarshalledRemRecords.length,
        'Delete: ', unmarshalledDelRecords.length);

    try {
        await syncInsertRecords(unmarshalledInsRecords.concat(unmarshalledModAddRecords));
        await syncDeleteRecords(unmarshalledDelRecords.concat(unmarshalledModDelRecords).concat(unmarshalledRemRecords));
        console.log('===SUCCESS===');
    } catch (err) {
        console.log('===ERROR===');
        console.log(err);
        //callback(null, `Captured error ${JSON.stringify(err)}`);
        callback(err, `Captured error ${JSON.stringify(err)}`);
        return { error: err }
    }
    callback(null, 'Successfully processed DynamoDB record');
    return Promise.resolve('Successfully processed DynamoDB record');
};
