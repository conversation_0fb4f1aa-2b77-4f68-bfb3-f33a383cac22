/* Amplify Params - DO NOT EDIT
	ENV
	REGION
Amplify Params - DO NOT EDIT */
const AWS = require('aws-sdk');
const docClient = new AWS.DynamoDB.DocumentClient();

const BATCH_SIZE = 25;

const countItems = async (tableName) => {
    let totalCount = 0;
    let lastEvaluatedKey = undefined;

    try {
        do {
            const params = {
                TableName: tableName,
                Select: 'COUNT',
                ExclusiveStartKey: lastEvaluatedKey,
            };

            const data = await docClient.scan(params).promise();
            totalCount += data.Count;
            lastEvaluatedKey = data.LastEvaluatedKey;
        } while (lastEvaluatedKey);

        console.log(`Tổng số items: ${totalCount}`);
        return totalCount;
    } catch (err) {
        console.error('Lỗi khi đếm items:', err);
        throw err;
    }
};

const backupLmShareSession = async (sourceTable, backupTable) => {
    try {
        console.log(`Backing up data from ${sourceTable} to ${backupTable}`);

        let lastEvaluatedKey = undefined;
        let allItems = [];

        do {
            const scanParams = {
                TableName: sourceTable,
                ExclusiveStartKey: lastEvaluatedKey
            };

            const data = await docClient.scan(scanParams).promise();
            allItems = allItems.concat(data.Items);
            lastEvaluatedKey = data.LastEvaluatedKey;
        } while (lastEvaluatedKey);

        let totalItems = 0;
        for (let i = 0; i < allItems.length; i += BATCH_SIZE) {
            const batch = allItems.slice(i, i + BATCH_SIZE);
            const writeParams = {
                RequestItems: {
                    [backupTable]: batch.map(item => ({
                        PutRequest: {
                            Item: {...item, id: item['shareUrl']}
                        }
                    }))
                }
            };

            const result = await docClient.batchWrite(writeParams).promise();

            if (result.UnprocessedItems && Object.keys(result.UnprocessedItems).length > 0) {
                console.warn('Retrying unprocessed items...');
                await retryUnprocessedItems(result.UnprocessedItems);
            }

            totalItems += batch.length;
        }

        console.log(`Total items backed up: ${totalItems}`);
    } catch (error) {
        console.error("Error backing up data:", error);
    }
};

const retryUnprocessedItems = async (unprocessedItems) => {
    const MAX_RETRIES = 5;
    let retries = 0;
    console.warn("Unprocessed items:", Object.keys(unprocessedItems).length);
    while (Object.keys(unprocessedItems).length > 0 && retries < MAX_RETRIES) {
        await new Promise(res => setTimeout(res, 1000 * (retries + 1))); // exponential backoff
        const result = await docClient.batchWrite({ RequestItems: unprocessedItems }).promise();
        unprocessedItems = result.UnprocessedItems;
        retries++;
    }

    if (Object.keys(unprocessedItems).length > 0) {
        console.error("Failed to write some items after retries:", JSON.stringify(unprocessedItems, null, 2));
    }
};
/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log(`EVENT: ${JSON.stringify(event)}`);
    const backUpType = event.arguments['backUpType'];
    if (backUpType === 'backup') {
        await backupLmShareSession(process.env.API_FULLSWINGFLIGHT_LMSHARESESSIONTABLE_NAME, process.env.API_FULLSWINGFLIGHT_LMSHARESESSIONBACKUPTABLE_NAME);
    } else if (backUpType === 'restore') {
        await backupLmShareSession(process.env.API_FULLSWINGFLIGHT_LMSHARESESSIONBACKUPTABLE_NAME, process.env.API_FULLSWINGFLIGHT_LMSHARESESSIONTABLE_NAME);
    } else {
        await countItems(backUpType)
    }
    return {
        statusCode: 200,
    //  Uncomment below to enable CORS requests
    //  headers: {
    //      "Access-Control-Allow-Origin": "*",
    //      "Access-Control-Allow-Headers": "*"
    //  },
        body: JSON.stringify('Hello from Lambda!'),
    };
};
