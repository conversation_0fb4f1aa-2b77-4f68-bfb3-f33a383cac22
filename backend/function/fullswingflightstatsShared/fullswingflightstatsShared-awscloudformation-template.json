{"AWSTemplateFormatVersion": "2010-09-09", "Description": "Lambda layer resource stack creation using Amplify CLI", "Parameters": {"env": {"Type": "String"}, "deploymentBucketName": {"Type": "String"}, "s3Key": {"Type": "String"}, "description": {"Type": "String", "Default": ""}, "runtimes": {"Type": "List<String>"}}, "Resources": {"LambdaLayerVersion3ee45c6b": {"Type": "AWS::Lambda::LayerVersion", "Properties": {"CompatibleRuntimes": {"Ref": "runtimes"}, "Content": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Description": {"Ref": "description"}, "LayerName": {"Fn::Sub": ["fullswingflightstatsShared-${env}", {"env": {"Ref": "env"}}]}}, "DeletionPolicy": "Delete", "UpdateReplacePolicy": "<PERSON><PERSON>"}, "LambdaLayerPermissionAwsOrgo810d8s90nv3ee45c6b": {"Type": "AWS::Lambda::LayerVersionPermission", "Properties": {"Action": "lambda:GetLayerVersion", "LayerVersionArn": {"Ref": "LambdaLayerVersion3ee45c6b"}, "OrganizationId": "o-810d8s90nv", "Principal": "*"}}, "LambdaLayerPermissionPrivate3ee45c6b": {"Type": "AWS::Lambda::LayerVersionPermission", "Properties": {"Action": "lambda:GetLayerVersion", "LayerVersionArn": {"Ref": "LambdaLayerVersion3ee45c6b"}, "Principal": {"Ref": "AWS::AccountId"}}}}, "Outputs": {"Arn": {"Value": {"Ref": "LambdaLayerVersion3ee45c6b"}}}}