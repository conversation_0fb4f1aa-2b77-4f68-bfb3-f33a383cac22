const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

exports.returnErrorFn = (error, params) => {
    const err = new Error(error.message + (params ? ` ${params}` : ''));
    err.name = "CUSTOM_ERROR";
    err.code = error.errorType
    throw err;
}

exports.isEmailFn = (email) => {
    return EMAIL_REGEX.test(email)
};

exports.splitArrayIntoChunks = (array, chunkSize) => {
    return Array.from(
        {length: Math.ceil(array.length / chunkSize)},
        (_, i) => array.slice(i * chunkSize, (i + 1) * chunkSize)
    );
};

exports.getValidArgument = (arg) => {
    if (
        arg === null ||
        arg === undefined ||
        arg === 'null' ||
        arg === 'undefined' ||
        arg === ''
    ) {
        return undefined;
    }
    return arg;
};
