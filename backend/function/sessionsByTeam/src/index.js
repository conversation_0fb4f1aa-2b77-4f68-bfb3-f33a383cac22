/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	ENV
	REGION
Amplify Params - DO NOT EDIT */

/*
* Resource clients
*/
const AWS = require('aws-sdk');
const gql = require('graphql-tag');
const graphql = require('graphql');
const AppsyncClient = require('appsync-client').default;

const client = new AppsyncClient({
    // Required
    apiUrl: process.env.API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT,
    // Optional - these will default to process.env values (e.g. the IAM
    // role of the Lambda)
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    sessionToken: process.env.AWS_SESSION_TOKEN
});

const queryLmDataSessionsByTeam = gql`
query dataSessionsByTeam($lmTeamId: ID!,
                         $filter: ModelLmDataSessionFilterInput,
                         $nextToken: String) {
    dataSessionsByTeam(lmTeamId: $lmTeamId,
                       filter: $filter,
                       nextToken: $nextToken) {
        nextToken
        items {
            address
            city
            country
            createdAt
            deviceID
            duration
            elevation
            endTimestamp
            humidity
            id
            latitude
            lmProfileId
            lmTeamId
            location
            locationName
            longitude
            name
            owner
            sport
            startTimestamp
            state
            temperature
            updatedAt
        }
    }
}
`

/* AWS GraphQL Functions */
// lmTeamId: "", nextToken: "", filter: {_deleted: {ne: true}}
async function querySessions(teamId, nextToken) {
    const sresult = await client.request({
        query: queryLmDataSessionsByTeam,
        variables: {
            lmTeamId: teamId,
            nextToken: nextToken,
            filter: {
                _deleted: {
                    ne: true
                }
            }
        }
    });
    console.log(JSON.stringify(sresult));
    return sresult.dataSessionsByTeam
}

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log('===env===');
    //console.log(process.env);
    console.log('===event===');
    console.log(JSON.stringify(event, null, 2));

    const teamId = event.arguments.teamId;
    const nextToken = event.arguments.nextToken;

    try {
        console.log('Querying Sessions by Team: ', teamId);
        var results = await querySessions(teamId, nextToken);

        return results;
    } catch (err) {
        console.log('===ERROR===');
        console.log(err);
        return { error: err }
    }
};
