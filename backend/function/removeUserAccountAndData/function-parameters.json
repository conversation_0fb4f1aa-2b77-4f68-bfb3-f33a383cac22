{"permissions": {"auth": {"fullswingflight03ff7ff503ff7ff5": ["read", "update", "delete"]}, "storage": {"fullswingFlightStorage": ["read", "update", "delete"], "LmUser:@model(appsync)": ["read", "update", "delete"], "LmProfile:@model(appsync)": ["read", "update", "delete"], "LmDevice:@model(appsync)": ["read", "update", "delete"], "LmSession:@model(appsync)": ["read", "update", "delete"], "LmShot:@model(appsync)": ["read", "update", "delete"], "LmClub:@model(appsync)": ["read", "update", "delete"], "LmSessionStats:@model(appsync)": ["read", "update", "delete"], "LmUserStats:@model(appsync)": ["read", "update", "delete"]}}, "lambdaLayers": [{"type": "ProjectLayer", "resourceName": "fullswingflightstatsShared", "version": "Always choose latest version", "isLatestVersionSelected": true, "env": "dev"}]}