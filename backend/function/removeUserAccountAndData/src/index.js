/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	API_FULLSWINGFLIGHT_LMCLUBTABLE_ARN
	API_FULLSWINGFLIGHT_LMCLUBTABLE_NAME
	API_FULLSWINGFLIGHT_LMDEVICETABLE_ARN
	API_FULLSWINGFLIGHT_LMDEVICETABLE_NAME
	API_FULLSWINGFLIGHT_LMPROFILETABLE_ARN
	API_FULLSWINGFLIGHT_LMPROFILETABLE_NAME
	API_FULLSWINGFLIGHT_LMSESSIONSTATSTABLE_ARN
	API_FULLSWINGFLIGHT_LMSESSIONSTATSTABLE_NAME
	API_FULLSWINGFLIGHT_LMSESSIONTABLE_ARN
	API_FULLSWINGFLIGHT_LMSESSIONTABLE_NAME
	API_FULLSWINGFLIGHT_LMSHOTTABLE_ARN
	API_FULLSWINGFLIGHT_LMSHOTTABLE_NAME
	API_FULLSWINGFLIGHT_LMUSERSTATSTABLE_ARN
	API_FULLSWINGFLIGHT_LMUSERSTATSTABLE_NAME
	API_FULLSWINGFLIGHT_LMUSERTABLE_ARN
	API_FULLSWINGFLIGHT_LMUSERTABLE_NAME
	AUTH_FULLSWINGFLIGHT03FF7FF503FF7FF5_USERPOOLID
	ENV
	REGION
	STORAGE_FULLSWINGFLIGHTSTORAGE_BUCKETNAME
Amplify Params - DO NOT EDIT */


const AWS = require('aws-sdk');
const crypto = require('crypto');
const docClient = new AWS.DynamoDB.DocumentClient()
const cogClient = new AWS.CognitoIdentityServiceProvider()

/* Helper Functions */

/* AWS Cognito Functions */

async function deleteCognitoUser(userId) {
    var dparams = {
        UserPoolId: process.env.AUTH_FULLSWINGFLIGHT03FF7FF503FF7FF5_USERPOOLID,
        Username: userId
    }
    const response = await cogClient.adminDeleteUser(dparams).promise();
    return true;
}

/* AWS DynamoDB Delete Functions */

async function deleteLmUser(userId) {
    var dparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMUSERTABLE_NAME,
        Key: { 
            "id": userId
        },
    };
    const response = await docClient.delete(dparams).promise();
    return true;
}

async function deleteLmProfile(profileId) {
    var dparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMPROFILETABLE_NAME,
        Key: { 
            id: profileId
        },
    };
    const response = await docClient.delete(dparams).promise();
    return true;
}

async function deleteLmDevice(deviceId) {
    var dparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMDEVICETABLE_NAME,
        Key: { 
            id: deviceId
        },
    };
    const response = await docClient.delete(dparams).promise();
    return true;
}

async function deleteLmClub(clubId) {
    var dparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMCLUBTABLE_NAME,
        Key: { 
            id: clubId
        },
    };
    const response = await docClient.delete(dparams).promise();
    return true;
}

async function deleteLmSession(sessionId) {
    var dparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMSESSIONTABLE_NAME,
        Key: { 
            id: sessionId
        },
    };
    const response = await docClient.delete(dparams).promise();
     return true;
}

async function deleteLmShot(shotId) {
    var dparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMSHOTTABLE_NAME,
        Key: { 
            id: shotId
        },
    };
    const response = await docClient.delete(dparams).promise();
    return true;
}

async function deleteLmUserStats(statsId) {
    var dparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMUSERSTATSTABLE_NAME,
        Key: { 
            id: statsId
        },
    };
    const response = await docClient.delete(dparams).promise();
    return true;
}

async function deleteLmSessionStats(statsId) {
    var dparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMSESSIONSTATSTABLE_NAME,
        Key: { 
            id: statsId
        },
    };
    const response = await docClient.delete(dparams).promise();
    return true;
}

/* AWS DynamoDB Get Functions */

async function getLmProfile(userId) {
    var qparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMPROFILETABLE_NAME,
        IndexName: 'byUser',
        KeyConditionExpression: '#userId = :userId',
        ProjectionExpression: 'id',
        ExpressionAttributeValues: { 
            ':userId': userId
        },
        ExpressionAttributeNames: { 
            '#userId': 'userId'
        },
    };
    const response = await docClient.query(qparams).promise();
    if (response.Items.length > 0) 
        return response.Items;

    console.log('Profile Not Found for owner: ', userId);
    return [];
}

async function getLmClubs(userId) {
    var qparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMCLUBTABLE_NAME,
        IndexName: 'byUser',
        KeyConditionExpression: '#owner = :userId',
        ProjectionExpression: 'id',
        ExpressionAttributeValues: { 
            ':userId': userId
        },
        ExpressionAttributeNames: { 
            '#owner': 'owner'
        },
    };
    const response = await docClient.query(qparams).promise();
    if (response.Items.length > 0) 
        return response.Items;

    console.log('Clubs Not Found for owner: ', userId);
    return [];
}

async function getLmDevices(userId) {
    var qparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMDEVICETABLE_NAME,
        IndexName: 'byUser',
        KeyConditionExpression: '#owner = :userId',
        ProjectionExpression: 'id',
        ExpressionAttributeValues: { 
            ':userId': userId
        },
        ExpressionAttributeNames: { 
            '#owner': 'owner'
        },
    };
    const response = await docClient.query(qparams).promise();
    if (response.Items.length > 0) 
        return response.Items;

    console.log('Devices Not Found for owner: ', userId);
    return [];
}

async function getLmSessions(userId) {
    var response = {items:[]}
    var lastEvalKey = null;
    do {
        var qparams = {
            TableName: process.env.API_FULLSWINGFLIGHT_LMSESSIONTABLE_NAME,
            ExclusiveStartKey: lastEvalKey,
            IndexName: 'byUser',
            KeyConditionExpression: '#owner = :userId',
            ProjectionExpression: 'id',
            ExpressionAttributeValues: { 
                ':userId': userId
            },
            ExpressionAttributeNames: { 
                '#owner': 'owner'
            },
        };
        const sessionResponse = await docClient.query(qparams).promise();
        response.items.push(...sessionResponse.Items);
        lastEvalKey = sessionResponse.LastEvaluatedKey;
    } while (lastEvalKey);
    if (response.items.length > 0) 
        return response.items;

    console.log('Sessions Not Found for owner: ', userId);
    return [];
}

async function getLmShots(userId) {
    var response = {items:[]}
    var lastEvalKey = null;
    do {
        var qparams = {
            TableName: process.env.API_FULLSWINGFLIGHT_LMSHOTTABLE_NAME,
            ExclusiveStartKey: lastEvalKey,
            IndexName: 'byUser',
            KeyConditionExpression: '#owner = :userId',
            ProjectionExpression: 'id',
            ExpressionAttributeValues: { 
                ':userId': userId
            },
            ExpressionAttributeNames: { 
                '#owner': 'owner'
            },
        };
        const shotresponse = await docClient.query(qparams).promise();
        response.items.push(...shotresponse.Items);
        lastEvalKey = shotresponse.LastEvaluatedKey;
    } while (lastEvalKey);
    if (response.items.length > 0) 
        return response.items;

    console.log('Shots Not Found for owner: ', userId);
    return [];
}

async function getLmUserStats(userId) {
    var qparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMUSERSTATSTABLE_NAME,
        IndexName: 'byUser',
        KeyConditionExpression: '#owner = :userId',
        ProjectionExpression: 'id',
        ExpressionAttributeValues: { 
            ':userId': userId
        },
        ExpressionAttributeNames: { 
            '#owner': 'owner'
        },
    };
    const response = await docClient.query(qparams).promise();
    if (response.Items.length > 0) 
        return response.Items;

    console.log('User Stats Not Found for owner: ', userId);
    return [];
}

async function getLmSessionStats(userId) {
    var response = {items:[]}
    var lastEvalKey = null;
    do {
        var qparams = {
            TableName: process.env.API_FULLSWINGFLIGHT_LMSESSIONSTATSTABLE_NAME,
            ExclusiveStartKey: lastEvalKey,
            IndexName: 'byUser',
            KeyConditionExpression: '#owner = :userId',
            ProjectionExpression: 'id',
            ExpressionAttributeValues: { 
                ':userId': userId
            },
            ExpressionAttributeNames: { 
                '#owner': 'owner'
            },
        };
        const statResponse = await docClient.query(qparams).promise();
        response.items.push(...statResponse.Items);
        lastEvalKey = statResponse.LastEvaluatedKey;
    } while (lastEvalKey);
    if (response.items.length > 0) 
        return response.items;

    console.log('Session Stats Not Found for owner: ', userId);
    return [];
}

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log('===env===');
    //console.log(process.env);
    console.log('===event===');
    console.log(JSON.stringify(event, null, 2));
    
    try {
        console.log(`===Clearing Data for user ${event.arguments.userId}===`);
        await deleteLmUser(event.arguments.userId);

        const profiles = await getLmProfile(event.arguments.userId);
        for (const profile of profiles) {
            await deleteLmProfile(profile.id);
        }

        const devices = await getLmDevices(event.arguments.userId);
        for (const device of devices) {
            await deleteLmDevice(device.id);
        }

        const clubs = await getLmClubs(event.arguments.userId);
        for (const club of clubs) {
            await deleteLmClub(club.id);
        }

        const sessions = await getLmSessions(event.arguments.userId);
        for (const session of sessions) {
            await deleteLmSession(session.id);
        }

        const shots = await getLmShots(event.arguments.userId);
        for (const shot of shots) {
            await deleteLmShot(shot.id);
        }

        const userStats = await getLmUserStats(event.arguments.userId);
        for (const stat of userStats) {
            await deleteLmUserStats(stat.id);
        }

        const sessionStats = await getLmSessionStats(event.arguments.userId);
        for (const stat of sessionStats) {
            await deleteLmSessionStats(stat.id);
        }

        await deleteCognitoUser(event.arguments.userId);

        console.log('===SUCCESS===');
    } catch (err) {
        console.log('===ERROR===');
        console.log(err);
        //callback(null, `Captured error ${JSON.stringify(err)}`);
        callback(err, `Captured error ${JSON.stringify(err)}`);
        return { error: err }
    }
    callback(null, 'Successfully cleared User Data');
    return Promise.resolve('Successfully cleared User Data');
};
