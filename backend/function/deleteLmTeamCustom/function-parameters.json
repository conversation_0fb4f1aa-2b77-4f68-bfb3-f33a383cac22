{"permissions": {"api": {"fullswingflight": ["Mutation"]}, "storage": {"LmTeamPlayers:@model(appsync)": ["read", "update"], "LmTeamCoaches:@model(appsync)": ["read", "update"], "LmTeam:@model(appsync)": ["read", "update"], "LmCoachPlayers:@model(appsync)": ["read", "update"]}}, "lambdaLayers": [{"type": "ProjectLayer", "resourceName": "fullswingflightstatsShared", "version": "Always choose latest version", "isLatestVersionSelected": true, "env": "dev"}]}