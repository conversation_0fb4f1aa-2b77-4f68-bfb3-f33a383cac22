/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_ARN
	API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_NAME
	API_FULLSWINGFLIGHT_LMTEAMCOACHESTABLE_ARN
	API_FULLSWINGFLIGHT_LMTEAMCOACHESTABLE_NAME
	API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_ARN
	API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME
	API_FULLSWINGFLIGHT_LMTEAMTABLE_ARN
	API_FULLSWINGFLIGHT_LMTEAMTABLE_NAME
	ENV
	REGION
Amplify Params - DO NOT EDIT */
const AWS = require('aws-sdk');
const docClient = new AWS.DynamoDB.DocumentClient();
const {success, error} = require('/opt/nodejs/response');
const {returnErrorFn, getValidArgument, splitArrayIntoChunks} = require('/opt/nodejs/util');
const gql = require('graphql-tag');
const AppsyncClient = require('appsync-client').default;

const client = new AppsyncClient({
    // Required
    apiUrl: process.env.API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT,
    // Optional - these will default to process.env values (e.g. the IAM
    // role of the Lambda)
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    sessionToken: process.env.AWS_SESSION_TOKEN
});

const deleteLmTeamQuery = gql`
    mutation deleteLmTeam($input: DeleteLmTeamInput!) {
        deleteLmTeam(input: $input) {
            _deleted
            _lastChangedAt
            _version
            createdAt
            id
            name
            owner
            readers
            pitcherId
            profileImage
            updatedAt
        }}`
const deleteLmTeamPlayersQuery = gql`
    mutation deleteLmTeamPlayers($input: DeleteLmTeamPlayersInput!) {
        deleteLmTeamPlayers(input: $input) {
            _deleted
            _lastChangedAt
            _version
            createdAt
            email
            id
            readers
            joined_at
            lineupOrderBatting
            lineupOrderPitching
            lmPlayerId
            lmPlayerName
            lmTeamId
            owner
            type
            updatedAt
            team {
                id
                owner
                name
                profileImage
                pitcherId
                createdAt
                updatedAt
                _version
                _deleted
                _lastChangedAt
                __typename
            }
        }
    }
`
const updateLmCoachPlayersQuery = gql`
    mutation updateLmCoachPlayers($input: UpdateLmCoachPlayersInput!) {
        updateLmCoachPlayers(input: $input) {
            id
            lmCoachId
            owner
            lmPlayerId
            lmPlayerName
            status
            lmPlayerEmail
            teamAssigned
            createdAt
            updatedAt
            _version
            _deleted
            _lastChangedAt
            __typename
        }}`

const ERROR = {
    TEAM_NOT_FOUND: {
        errorType: "errors.team.not_found",
        message: "Team does not exist or has been deleted."
    },
    NOT_TEAM_OWNER: {
        errorType: "errors.team.not_owner",
        message: "Only the team owner can perform this action."
    },
}
const getTeamByIdFn = async (lmTeamId) => {
    const result = await docClient.get({
        TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMTABLE_NAME,
        Key: {
            id: lmTeamId
        }
    }).promise();
    const team = result.Item;
    if (!team) return returnErrorFn(ERROR.TEAM_NOT_FOUND);
    return team;
}
const deleteTeamFn = async (lmTeamId, version) => {
    console.log('Deleting lm team: ', lmTeamId);
    const result = await client.request({
        query: deleteLmTeamQuery,
        variables: {
            input: {id: lmTeamId, _version: version}
        }
    })
    if (!result || !result['deleteLmTeam'] || !result['deleteLmTeam']['id'] || !result['deleteLmTeam']['_deleted']) {
        console.error('Delete team fn fail', result);
        throw new Error('Delete team fn fail');
    }
    console.log('Deleted lm team: ', lmTeamId);
    return result['deleteLmTeam'];
}

const listPlayerNotAssignTeam = async (lmPlayerIds, lmPlayerEmails, owner) => {
    console.log('Getting list player not assign team: ', lmPlayerIds, lmPlayerEmails, owner);
    try {
        let allItems = [];
        let ExclusiveStartKey = undefined;
        do {
            const queryParams = {
                TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME,
                IndexName: 'byOwner',
                KeyConditionExpression: '#owner = :owner',
                ExpressionAttributeNames: {
                    '#owner': 'owner'
                },
                ExpressionAttributeValues: {
                    ':owner': owner
                }
            };
            const result = await docClient.query(queryParams).promise();
            allItems.push(...result['Items']);
            ExclusiveStartKey = result['LastEvaluatedKey'];
        } while (ExclusiveStartKey);
        allItems = allItems.filter(item => item['_deleted'] !== true);
        console.log('---', allItems);
        const notHasTeamPlayersById = lmPlayerIds.filter(lmPlayerId => {
            const match = allItems.find(item => item['lmPlayerId'] === lmPlayerId);
            return !match;
        });
        console.log('notHasTeamPlayersById', notHasTeamPlayersById)
        const notHasTeamPlayersByEmail = lmPlayerEmails.filter(lmPlayerEmail => {
            const match = allItems.find(item => item['email'] === lmPlayerEmail);
            return !match;
        });
        console.log('notHasTeamPlayersByEmail', notHasTeamPlayersByEmail)
        return {notHasTeamPlayersById, notHasTeamPlayersByEmail}
    } catch (e) {
        console.error('Error get list player not assign team', e);
    }
}

const updateCoachPlayersAssignedFn = async (lmPlayerIds, lmPlayerEmails, owner) => {
    console.log('Updating coach players assigned: ', lmPlayerIds, lmPlayerEmails, owner);
    const coachPlayersNotAssigned = [];
    const {
        notHasTeamPlayersById,
        notHasTeamPlayersByEmail
    } = await listPlayerNotAssignTeam(lmPlayerIds, lmPlayerEmails, owner);
    try {
        const allItems = [];
        let ExclusiveStartKey = undefined;
        do {
            const queryParams = {
                TableName: process.env.API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_NAME,
                IndexName: 'byCoach',
                KeyConditionExpression: 'lmCoachId = :lmCoachId',
                ExpressionAttributeValues: {
                    ':lmCoachId': owner
                }
            };
            const result = await docClient.query(queryParams).promise();
            allItems.push(...result['Items']);
            ExclusiveStartKey = result['LastEvaluatedKey'];
        } while (ExclusiveStartKey);
        const coachPlayersAssigned = [];
        notHasTeamPlayersById.forEach(lmPlayerId => {
            const match = allItems.find(item => item['lmPlayerId'] === lmPlayerId);
            if (match) coachPlayersAssigned.push(match);
        })
        notHasTeamPlayersByEmail.forEach(lmPlayerEmail => {
            const match = allItems.find(item => item['lmPlayerEmail'] === lmPlayerEmail);
            if (match) coachPlayersAssigned.push(match);
        });
        const batches = splitArrayIntoChunks(coachPlayersAssigned, 10);
        for (const batch of batches) {
            await Promise.all(batch.map(async (coachPlayer) => {
                try {
                    console.log('Updating teamAssigned coach player: ', coachPlayer.id);
                    const input = {
                        id: coachPlayer['id'],
                        teamAssigned: 'N',
                        _version: coachPlayer['_version'],
                    };
                    const result = await client.request({
                        query: updateLmCoachPlayersQuery,
                        variables: {
                            input
                        }
                    });
                    if (!result || !result['updateLmCoachPlayers'] || !result['updateLmCoachPlayers']['id']) {
                        console.error('Update coach player fn fail', result);
                    } else {
                        console.log(`Updated teamAssigned coach player`, coachPlayer.id);
                        coachPlayersNotAssigned.push(result['updateLmCoachPlayers'])
                    }
                } catch (e) {

                }
            }))
        }
    } catch (e) {
        console.error('Error updating coach players assigned', e);
    }
    return coachPlayersNotAssigned;
}

const deleteLmTeamPlayersFn = async (lmTeamId) => {
    console.log('Deleting team players: ', lmTeamId);
    const teamPlayersDeleted = [];
    try {
        const queryParams = {
            TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME,
            IndexName: 'byTeam',
            KeyConditionExpression: 'lmTeamId = :lmTeamId',
            ExpressionAttributeValues: {
                ':lmTeamId': lmTeamId
            }
        };
        const result = await docClient.query(queryParams).promise();
        if (result && result['Items'] && result['Items'].length > 0) {
            const teamPlayers = result['Items'];
            const batches = splitArrayIntoChunks(teamPlayers, 10);

            for (const batch of batches) {
                await Promise.all(batch.map(async (teamPlayer) => {
                    try {
                        console.log('Deleting team player: ', teamPlayer.id);
                        const result = await client.request({
                            query: deleteLmTeamPlayersQuery,
                            variables: {
                                input: {id: teamPlayer.id, _version: teamPlayer._version}
                            }
                        })
                        if (!result || !result['deleteLmTeamPlayers'] || !result['deleteLmTeamPlayers']['id'] || !result['deleteLmTeamPlayers']['_deleted']) {
                            console.error('Delete team player fn fail', teamPlayer.id, result);
                        } else {
                            console.log('Deleted team player: ', teamPlayer.id);
                            teamPlayersDeleted.push(result['deleteLmTeamPlayers'])
                        }
                    } catch (e) {
                        console.error('Error deleting team player', teamPlayer.id, e);
                    }
                }))
            }
        }
    } catch (e) {
        console.error('Error deleting team players', e);
    }
    return teamPlayersDeleted
}
/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log('===env===', event);
    try {
        const lmTeamId = getValidArgument(event.arguments['lmTeamId']);
        const owner = event.identity.username;
        console.log('Deleting team: ', lmTeamId);
        const team = await getTeamByIdFn(lmTeamId);
        if (team['owner'] !== owner) return returnErrorFn(ERROR.NOT_TEAM_OWNER);
        const teamDeleted = await deleteTeamFn(lmTeamId, team._version);
        const teamPlayersDeleted = await deleteLmTeamPlayersFn(lmTeamId);
        const teamPlayerIdsDeleted = teamPlayersDeleted.map(teamPlayer => teamPlayer.lmPlayerId).filter(Boolean);
        const teamPlayerEmailsDeleted = teamPlayersDeleted.filter(teamPlayer => !!teamPlayer['email'] && !teamPlayer['lmPlayerId']).map(teamPlayer => teamPlayer['email']);
        const coachPlayersNotAssigned = await updateCoachPlayersAssignedFn(teamPlayerIdsDeleted, teamPlayerEmailsDeleted, owner);
        const response = {
            teamDeleted: teamDeleted,
            teamPlayersDeleted: teamPlayersDeleted,
            coachPlayersNotAssigned: coachPlayersNotAssigned
        };
        console.log('Deleted team: ', lmTeamId);
        return success({...response});
    } catch (e) {
        console.error(e)
        return error(e.name === "CUSTOM_ERROR" ? {errorType: e.code, message: e.message} : undefined);
    }
};
