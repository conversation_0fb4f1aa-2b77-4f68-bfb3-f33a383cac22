/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAP<PERSON>NDPOINTOUTPUT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	ENV
	REGION
Amplify Params - DO NOT EDIT */

/*
* Resource clients
*/
const AWS = require('aws-sdk');
const gql = require('graphql-tag');
const graphql = require('graphql');
const AppsyncClient = require('appsync-client').default;

const client = new AppsyncClient({
    // Required
    apiUrl: process.env.API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT,
    // Optional - these will default to process.env values (e.g. the IAM
    // role of the Lambda)
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    sessionToken: process.env.AWS_SESSION_TOKEN
});

const queryLmDataResultsBySession = gql`
query dataResultsBySession($lmSessionId: ID!, 
                           $filter: ModelLmDataResultsFilterInput, 
                           $nextToken: String) {
    dataResultsBySession(lmSessionId: $lmSessionId,
                         filter: $filter,
                         nextToken: $nextToken) {
        nextToken
        items {
            createdAt
            id
            isFavorite
            lmSessionId
            owner
            pointCloudKey
            resultId
            sport
            timestamp
            updatedAt
            videoKey
            baseballResults {
                pitcherName
                pitcher
                hitterName
                hitter
                batData {
                    batAttackAngleDeg
                    batConnectionAtImpactDeg
                    batEarlyConnectionDeg
                    batPeakHandSpeedMps
                    batRotationalAccelerationGs
                    batSpeedMps
                    hitSpinTotalRpm
                    hitSpinTopRpm
                    hitSpinSideRpm
                    hitSpinDirectionEfficiencyPct
                    hitSpinDirectionClockBearing {
                        minutes
                        hours
                    }
                    hitSpinBackRpm
                    hitSpinActiveRpm
                    hitLaunchAngleDeg
                    hitExitVelocityMps
                    hitDistanceMeters
                    hitDirectionDeg
                    hitBallContactWorld3dPositionMeters {
                        z_pos
                        y_pos
                        x_pos
                    }
                    hitBallContactTimestamp {
                        seconds
                        nanos
                    }
                    batVerticalAngleDeg
                    batSwingStartTimestamp {
                        seconds
                        nanos
                    }
                    trajectory {
                        golfType
                        baseballType
                        polynomial {
                            z_coefficients
                            y_coefficients
                            x_coefficients
                        }
                    }
                    teedBallLocationWorld3dPositionMeters {
                        z_pos
                        y_pos
                        x_pos
                    }
                }
                pitchData {
                    trajectory {
                        polynomial {
                            z_coefficients
                            y_coefficients
                            x_coefficients
                        }
                        golfType
                        baseballType
                    }
                    pitchSpinTotalRpm
                    pitchSpinTopRpm
                    pitchSpinSideRpm
                    pitchSpinDirectionEfficiencyPct
                    pitchSpinDirectionClockBearing {
                        minutes
                        hours
                    }
                    pitchSpinBackRpm
                    pitchSpinActiveRpm
                    pitchReleaseWorld3dPositionMeters {
                        z_pos
                        y_pos
                        x_pos
                    }
                    pitchReleaseVerticalAngleDeg
                    pitchReleaseVelocityMps
                    pitchReleaseTimestamp {
                        seconds
                        nanos
                    }
                    pitchReleaseHorizontalAngleDeg
                    pitchReleaseBackwardExtensionMeters
                    pitchReleaseArmSlotDeg
                    pitchCrossPlateWorld3dPositionMeters {
                        z_pos
                        y_pos
                        x_pos
                    }
                    pitchCrossPlateTimestamp {
                        seconds
                        nanos
                    }
                    pitchBreakVerticalMeters
                    pitchBreakInducedVerticalMeters
                    pitchBreakHorizontalMeters
                    pitchApproachVelocityMps
                    pitchApproachPlateWorld3dPositionMeters {
                        z_pos
                        y_pos
                        x_pos
                    }
                    pitchApproachPlateTimestamp {
                        nanos
                        seconds
                    }
                }
            }
        }
    }
}
`

/* AWS GraphQL Functions */
// lmTeamId: "", nextToken: "", filter: {_deleted: {ne: true}}
async function queryResults(sessionId, nextToken) {
    const sresult = await client.request({
        query: queryLmDataResultsBySession,
        variables: {
            lmSessionId: sessionId,
            nextToken: nextToken,
            filter: {
                _deleted: {
                    ne: true
                }
            }
        }
    });
    console.log(JSON.stringify(sresult));
    return sresult.dataResultsBySession
}

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log('===env===');
    //console.log(process.env);
    console.log('===event===');
    console.log(JSON.stringify(event, null, 2));

    const sessionId = event.arguments.sessionId;
    const nextToken = event.arguments.nextToken;

    try {
        console.log('Querying Results by Session: ', sessionId);
        var results = await queryResults(sessionId, nextToken);

        return results;
    } catch (err) {
        console.log('===ERROR===');
        console.log(err);
        return { error: err }
    }
};
