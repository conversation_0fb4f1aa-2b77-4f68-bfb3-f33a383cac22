/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	API_FULLSWINGFLIGHT_LMCLUBTABLE_ARN
	API_FULLSWINGFLIGHT_LMCLUBTABLE_NAME
	API_FULLSWINGFLIGHT_LMSESSIONTABLE_ARN
	API_FULLSWINGFLIGHT_LMSESSIONTABLE_NAME
	API_FULLSWINGFLIGHT_LMSHARESESSIONTABLE_ARN
	API_FULLSWINGFLIGHT_LMSHARESESSIONTABLE_NAME
	API_FULLSWINGFLIGHT_LMSHOTTABLE_ARN
	API_FULLSWINGFLIGHT_LMSHOTTABLE_NAME
	ENV
	REGION
Amplify Params - DO NOT EDIT */

const AWS = require('aws-sdk');
//const crypto = require('crypto');
//const agent = new http.Agent({ keepAlive: true })
//const dynamodb = new AWS.DynamoDB({ httpOptions: { agent } })
//const docClient = new AWS.DynamoDB.DocumentClient({ httpOptions: { agent } })
const docClient = new AWS.DynamoDB.DocumentClient()

const gql = require('graphql-tag');
const graphql = require('graphql');
const AppsyncClient = require('appsync-client').default;

const client = new AppsyncClient({
    // Required
    apiUrl: process.env.API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT,
    // Optional - these will default to process.env values (e.g. the IAM
    // role of the Lambda)
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    sessionToken: process.env.AWS_SESSION_TOKEN
});

const updateLmShareSession = gql`
mutation updateLmShareSession($input: UpdateLmShareSessionInput!) {
    updateLmShareSession(input: $input) {
        shareUrl
        lmSessionId
        owner
        createdAt
        updatedAt
        _version
        _lastChangedAt
        _deleted
        session {
            name
            address
            duration
            drillTargets {
                id
                clubCategory
                distance {
                    fairwayWidth
                    maxDistance
                    distance
                    targetWidth
                }
                type
                shotCount
                shotType
            }
            elevation
            endTimestamp
            humidity
            location
            normalizedBallType
            normalizedElevation
            normalizedTemperature
            startTimestamp
            temperature
            shots {
                apex
                apexValid
                attackAngle
                attackAngleValid
                ballSpeed
                ballSpeedValid
                carryDistance
                carryDistanceValid
                clubId
                clubColor
                clubCategory
                clubName
                clubType
                clubPath
                clubPathValid
                clubSpeed
                clubSpeedValid
                faceAngle
                faceAngleValid
                horizontalLaunchAngle
                horizontalLaunchAngleValid
                launchAngle
                launchAngleValid
                descentAngle
                descentAngleValid
                normalizedValues {
                    apex
                    carryDistance
                    side
                    sideTotal
                    totalDistance
                }
                side
                sideTotal
                sideTotalValid
                sideValid
                smashFactor
                smashFactorValid
                spinAxis
                spinAxisValid
                spinRate
                spinRateValid
                timestamp
                totalDistance
                totalDistanceValid
                isFavorite
                impactUrl
                videoUrl
                yFit
                xFit
                zFit
                lmDrillTargetId
                shotQuality
                distanceToPin
                targetDistance
            }
        }
    }
}
`
const deleteLmShareSession = gql`
mutation deleteLmShareSession($input: DeleteLmShareSessionInput!) {
    deleteLmShareSession(input: $input) {
        shareUrl
        _version
    }
}
`

/* Helper Functions */

function mapShotData(lmShot) {
    var shotData = {
        clubId: lmShot.club.id,
        clubColor: lmShot.club.color,
        clubCategory: lmShot.clubCategory,
        clubType: lmShot.club.type,
        clubName: lmShot.club.name,
        timestamp: lmShot.timestamp,
        isFavorite: lmShot.isFavorite,

        // impactUrl: lmShot.impactKey,
        // videoUrl: lmShot.videoKey,

        clubSpeed: lmShot.clubSpeed,
        ballSpeed: lmShot.ballSpeed,
        smashFactor: lmShot.smashFactor,
        attackAngle: lmShot.attackAngle,
        clubPath: lmShot.clubPath,
        launchAngle: lmShot.launchAngle,
        horizontalLaunchAngle: lmShot.horizontalLaunchAngle,
        faceAngle: lmShot.faceAngle,
        spinRate: lmShot.spinRate,
        spinAxis: lmShot.spinAxis,
        carryDistance: lmShot.carryDistance,
        totalDistance: lmShot.totalDistance,
        side: lmShot.side,
        sideTotal: lmShot.sideTotal,
        apex: lmShot.apex,
        descentAngle: lmShot.descentAngle,
      
        clubSpeedValid: lmShot.clubSpeedValid,
        ballSpeedValid: lmShot.ballSpeedValid,
        smashFactorValid: lmShot.smashFactorValid,
        attackAngleValid: lmShot.attackAngleValid,
        clubPathValid: lmShot.clubPathValid,
        launchAngleValid: lmShot.launchAngleValid,
        horizontalLaunchAngleValid: lmShot.horizontalLaunchAngleValid,
        faceAngleValid: lmShot.faceAngleValid,
        spinRateValid: lmShot.spinRateValid,
        spinAxisValid: lmShot.spinAxisValid,
        carryDistanceValid: lmShot.carryDistanceValid,
        totalDistanceValid: lmShot.totalDistanceValid,
        sideValid: lmShot.sideValid,
        sideTotalValid: lmShot.sideTotalValid,
        apexValid: lmShot.apexValid,
        descentAngleValid: lmShot.descentAngleValid,
      
        xFit: lmShot.xFit,
        yFit: lmShot.yFit,
        zFit: lmShot.zFit,
        normalizedValues: lmShot.normalizedValues,

        lmDrillTargetId: lmShot.lmDrillTargetId,
        shotQuality: lmShot.shotQuality,
        targetDistance: lmShot.targetDistance,
        distanceToPin: lmShot.distanceToPin
    };
    return shotData;
}

function mapSessionData(lmSession) {
    var sessionData = {
        startTimestamp: lmSession.startTimestamp,
        endTimestamp: lmSession.endTimestamp,
        duration: lmSession.duration,

        name: lmSession.name,
        address: lmSession.address,

        elevation: lmSession.elevation,
        temperature: lmSession.temperature,
        humidity: lmSession.humidity,
        location: lmSession.location,
        normalizedElevation: lmSession.normalizedElevation,
        normalizedTemperature: lmSession.normalizedTemperature,
        normalizedBallType: lmSession.normalizedBallType,

        shots: lmSession.shots ? lmSession.shots.map(shot => mapShotData(shot)) : [],

        sessionQuality: lmSession.sessionQuality,
        drillTargets: lmSession.drillTargets ? lmSession.drillTargets : []
    };
    return sessionData;
}

/* AWS GraphQL Functions */

async function pushSharedSessionMutation(sharedSession) {
    const sresult = await client.request({
        query: updateLmShareSession,
        variables: {
            input: {
                shareUrl: sharedSession.shareUrl
            }
        }
    });
    console.log(JSON.stringify(sresult));
}
async function pushSharedSessionMutationDelete(sharedSession) {
    const sresult = await client.request({
        query: deleteLmShareSession,
        variables: {
            input: {
                shareUrl: sharedSession.shareUrl,
                _version: sharedSession._version
            }
        }
    });
    console.log(JSON.stringify(sresult));
}

/* AWS DynamoDB Functions */

async function getShareBySession(sessionId) {
    var sessionqparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMSHARESESSIONTABLE_NAME,
        Limit: 1,
        IndexName: 'bySession',
        KeyConditionExpression: '#lmSessionId = :lmSessionId',
        ProjectionExpression: 'shareUrl, lmSessionId, #owner, #session, #version',
        ExpressionAttributeValues: { 
            ':lmSessionId': sessionId
        },
        ExpressionAttributeNames: { 
            '#owner': 'owner',
            '#session': 'session',
            '#lmSessionId': 'lmSessionId',
            '#version': '_version'
        }
    };
    const sessionresponse = await docClient.query(sessionqparams).promise();
    if (sessionresponse.Items.length > 1) 
        console.log('Found multiple Shared Sessions for Session: ', sessionId);
    if (sessionresponse.Items.length > 0) 
        return sessionresponse.Items[0];

    //console.log('Session Share Not Found: ', sessionId);
    return null;
}

async function getSession(sessionId) {
    var sessionqparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMSESSIONTABLE_NAME,
        ExclusiveStartKey: null,
        KeyConditions: {
            id: {
                AttributeValueList: [sessionId],
                ComparisonOperator: 'EQ'
            }
        },
        ExpressionAttributeNames: { 
            '#deleted': '_deleted'
        },
        FilterExpression: 'NOT attribute_exists(#deleted)',
    };
    const sessionresponse = await docClient.query(sessionqparams).promise();
    if (sessionresponse.Items.length > 1) 
        console.log('Found multiple Sessions for Session Id: ', sessionId);
    if (sessionresponse.Items.length > 0) 
        return sessionresponse.Items[0];

    console.log('Session Not Found: ', sessionId);
    return null;
}

async function getShots(sessionId) {
    var shotqparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMSHOTTABLE_NAME,
        IndexName: 'bySession',
        KeyConditionExpression: '#lmSessionId = :lmSessionId',
        ExpressionAttributeValues: { 
            ':lmSessionId': sessionId
        },
        ExpressionAttributeNames: { 
            '#lmSessionId': 'lmSessionId',
            '#deleted': '_deleted'
        },
        FilterExpression: 'NOT attribute_exists(#deleted)',
    };
    const shotresponse = await docClient.query(shotqparams).promise();
    if (shotresponse.Items.length > 0) {
        let shots = shotresponse.Items;
        for await (let shot of shots) {
            const club = await getClub(shot.clubId);
            if (club !== null) {
                shot.club = club;
            }
        }
        return shots;
    }

    console.log('Shots Not Found: ', sessionId);
    return null;
}

async function getShotsByClub(clubId) {
    var shotqparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMSHOTTABLE_NAME,
        Limit: 50000,
        IndexName: 'byClub',
        KeyConditionExpression: '#clubId = :clubId',
        ExpressionAttributeValues: { 
            ':clubId': clubId
        },
        ExpressionAttributeNames: { 
            '#id': 'id',
            '#clubId': 'clubId',
            '#lmSessionId': 'lmSessionId',
            '#deleted': '_deleted'
        },
        ProjectionExpression: '#id, #lmSessionId',
        FilterExpression: 'NOT attribute_exists(#deleted)',
    };
    const shotresponse = await docClient.query(shotqparams).promise();
    if (shotresponse.Items.length > 0) 
        return shotresponse.Items;

    console.log('Shots Not Found: ', sessionId);
    return null;
}

async function getClub(clubId) {
    var clubqparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMCLUBTABLE_NAME,
        ExclusiveStartKey: null,
        KeyConditions: {
            id: {
                AttributeValueList: [clubId],
                ComparisonOperator: 'EQ'
            }
        },
        ExpressionAttributeNames: { 
            '#deleted': '_deleted'
        },
        FilterExpression: 'NOT attribute_exists(#deleted)',
    };
    const clubresponse = await docClient.query(clubqparams).promise();
    if (clubresponse.Items.length > 0) 
        return clubresponse.Items[0];

    console.log('Club Not Found: ', clubId);
    return null;
}

async function updateShareSession(share) { 
    var timestamp = new Date();
    var timeStr = `${timestamp.getFullYear()}-${(timestamp.getMonth()+1).toString().padStart(2,'0')}-${timestamp.getDate().toString().padStart(2,'0')}T${timestamp.getHours().toString().padStart(2,'0')}:${timestamp.getMinutes().toString().padStart(2,'0')}:${timestamp.getSeconds().toString().padStart(2,'0')}.${timestamp.getMilliseconds()}Z`;

    var shareuparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMSHARESESSIONTABLE_NAME,
        Key: {
            shareUrl: share.shareUrl,
        },
        UpdateExpression: `SET #session = :session, 
                    #owner = :owner,
                    #lmSessionId = :lmSessionId, 
                    #updatedAt = :updatedAt, 
                    #createdAt = if_not_exists(#createdAt, :createdAt),
                    #typename = if_not_exists(#typename, :typename), 
                    #lastchangedat = :lastchangedat, 
                    #version = if_not_exists(#version, :version)`,
        ExpressionAttributeNames: {
            '#session': 'session',
            '#owner': 'owner',
            '#lmSessionId': 'lmSessionId',
            '#updatedAt': 'updatedAt',
            '#createdAt': 'createdAt',
            '#typename': '__typename',
            '#lastchangedat': '_lastChangedAt',
            '#version': '_version'
        },
        ExpressionAttributeValues: {
            ':session': share.session,
            ':lmSessionId': share.lmSessionId,
            ':owner': share.owner,
            ':updatedAt': timeStr,
            ':createdAt': timeStr,
            ':typename': 'LmShareSession',
            ':lastchangedat': timestamp.getTime(),
            ':version': 1,
        },
    };
    console.log('Updating Share: ', JSON.stringify(shareuparams, null, 2));
    await docClient.update(shareuparams).promise();
}

async function deleteShareSession(share) { 
    var sharedparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMSHARESESSIONTABLE_NAME,
        Key: {
            shareUrl: share.shareUrl,
        }
    };
    console.log('Updating Share: ', JSON.stringify(sharedparams, null, 2));
    await docClient.delete(sharedparams).promise();
}

/* 
TODO: 
sync insert session/shot
sync delete session/shot
getShareBySession
getShareByShot
updateShareSession
*/


async function syncInsertRecords(unmarshalledRecords) {
    for await (let record of unmarshalledRecords) {
        console.log('DynamoDB Record: ', JSON.stringify(record, null, 2));

        if (record.__typename == 'LmSession') {
            console.log('Updating Share with Session: ', record.owner, record.id);
            const share = await getShareBySession(record.id);
            if (share === null) return;
            console.log('Fetched share Id: ', share.shareUrl);

            var session = await getSession(record.id);
            if (session === null) return;
            const shots = await getShots(record.id);
            session.shots = shots;
            const sessionData = mapSessionData(session);
            share.session = sessionData;

            await updateShareSession(share);
            await pushSharedSessionMutation(share);
        } else
        if (record.__typename == 'LmShot') {
            console.log('Updating Share with Shot: ', record.owner, record.id);
            const share = await getShareBySession(record.lmSessionId);

            if (share === null) return;
            console.log('Fetched share Id: ', share.shareUrl);

            var session = await getSession(record.lmSessionId);
            if (session === null) return;
            const shots = await getShots(record.lmSessionId);
            session.shots = shots;
            const sessionData = mapSessionData(session);
            share.session = sessionData;

            await updateShareSession(share);
            await pushSharedSessionMutation(share);
        } else
        if (record.__typename == 'LmClub') {
            console.log('Updating Shares with Club: ', record.owner, record.id);
            const shots = await getShotsByClub(record.id);

            if (shots === null) return;

            let shareSessions = []
            let sessionIds = new Set(shots.map((s) => s.lmSessionId));
            for await (let sessionId of sessionIds) {
                const share = await getShareBySession(sessionId);
                if (share !== null) {
                    shareSessions.push(share);
                }
            }

            for await (let share of shareSessions) {
                console.log('Updating Share with Club: ', share.owner, share.id);
                var session = await getSession(share.lmSessionId);
                if (session === null) return;
                const shots = await getShots(share.lmSessionId);
                session.shots = shots;
                const sessionData = mapSessionData(session);
                share.session = sessionData;
    
                await updateShareSession(share);
                await pushSharedSessionMutation(share);
            }
        } else {
            console.log('Unknown INSERT Record: ', JSON.stringify(record, null, 2));
        }
    }
}

async function syncDeleteRecords(unmarshalledRecords) {
    for await (let record of unmarshalledRecords) {
        console.log('DynamoDB Delete Record: ', JSON.stringify(record, null, 2));

        // Probably will ignore LmSession delete.  App should handle removing share session in this case.
        if (record.__typename == 'LmSession') {
            console.log('Deleting Share with Session: ', record.owner, record.id);
            const share = await getShareBySession(record.id);
            if (share === null) return;
            console.log('Fetched share Id: ', share.shareUrl);
            
            share.session = null;
            await updateShareSession(share);
            await pushSharedSessionMutationDelete(share);
        } else
        if (record.__typename == 'LmShot') {
            console.log('Updating Share with Deleted Shot: ', record.owner, record.id);
            const share = await getShareBySession(record.lmSessionId);
            if (share === null) return;
            console.log('Fetched share Id: ', share.shareUrl);

            var session = await getSession(record.lmSessionId);
            if (session === null) return;
            const shots = await getShots(record.lmSessionId);
            session.shots = shots;
            const sessionData = mapSessionData(session);
            share.session = sessionData;

            await updateShareSession(share);
            await pushSharedSessionMutation(share);
        } else {
            console.log('Unknown DELETE Record: ', JSON.stringify(record, null, 2));
        }
    }
}

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event, context, callback) => {
    console.log('===env===');
    //console.log(process.env);
    console.log('===event===');
    console.log(JSON.stringify(event, null, 2));

    // New INSERT 
    const insertRecords = event.Records.filter(record => record.eventName == 'INSERT' );
    const unmarshalledInsRecords = insertRecords.map(record => 
        AWS.DynamoDB.Converter.unmarshall(record.dynamodb.NewImage)
    );

    // MODIFY
    const modRecords = event.Records.filter(record => record.eventName == 'MODIFY' );
    const unmarshalledModRecords = modRecords.map(record => 
        AWS.DynamoDB.Converter.unmarshall(record.dynamodb.NewImage)
    );

    // MODIFY New Image
    const unmarshalledModAddRecords = unmarshalledModRecords.filter(record => !record.hasOwnProperty('_deleted') || record._deleted == false );
    const modAddRecordIds = unmarshalledModAddRecords.map(record =>
        record.id
    );

    // MODIFY Old Image
    const unmarshalledModDelRecords = modRecords.map(record => 
        AWS.DynamoDB.Converter.unmarshall(record.dynamodb.OldImage)
    ).filter(record => modAddRecordIds.includes(record.id));

    // REMOVE 
    const remRecords = event.Records.filter(record => record.eventName == 'REMOVE' );
    const unmarshalledRemRecords = remRecords.map(record => 
        AWS.DynamoDB.Converter.unmarshall(record.dynamodb.OldImage)
    );
    
    // MODIFY New Deleted
    const unmarshalledDelRecords = unmarshalledModRecords.filter(record => record._deleted == true );

    // Capture new modify removed tables

    try {
        await syncInsertRecords(unmarshalledInsRecords.concat(unmarshalledModAddRecords));
        await syncDeleteRecords(unmarshalledDelRecords.concat(unmarshalledRemRecords));
        console.log('===SUCCESS===');
    } catch (err) {
        console.log('===ERROR===');
        console.log(err);
        //callback(null, `Captured error ${JSON.stringify(err)}`);
        callback(err, `Captured error ${JSON.stringify(err)}`);
        return { error: err }
    }
    callback(null, 'Successfully processed DynamoDB record');
    return Promise.resolve('Successfully processed DynamoDB record');
};
