/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAP<PERSON>NDPOINTOUTPUT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	ENV
	REGION
Amplify Params - DO NOT EDIT */

const AWS = require('aws-sdk');
const gql = require('graphql-tag');
const crypto = require('crypto');
const graphql = require('graphql');
const AppsyncClient = require('appsync-client').default;

const client = new AppsyncClient({
    // Required
    apiUrl: process.env.API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT,
    // Optional - these will default to process.env values (e.g. the IAM
    // role of the Lambda)
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    sessionToken: process.env.AWS_SESSION_TOKEN
});

const getLmProfileByUser = gql`
query GetLmProfileByUser(
    $owner: ID
    $nextToken: String
) {
  listLmProfiles(
    filter: {userId: {eq: $owner}}
    nextToken: $nextToken
    limit: 1000
  ) {
    nextToken
    startedAt
    __typename
    items {
        id
    }
  }
}
`

const lmFeaturesByUser = gql`
query FeaturesByUser(
  $owner: String!
  $featureName: String
) {
  featuresByUser(
    owner: $owner
    filter: {featureName: {eq: $featureName}}
  ) {
    nextToken
    startedAt
    items {
      id
      lmProfileId
      owner
      featureName
      enabled
      expiresAt
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
}
`

const getLmFeature = gql`
query GetLmFeature($id: ID!) {
    getLmFeature(id: $id) {
      id
      lmProfileId
      owner
      featureName
      enabled
      expiresAt
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`

const createLmFeature = gql`
mutation CreateLmFeature(
  $id: ID!
  $lmProfileId: ID!
  $owner: String
  $featureName: String
  $enabled: Boolean
  $expiresAt: AWSDateTime
  $version: Int
) {
  createLmFeature(input: {
    id: $id, 
    lmProfileId: $lmProfileId,
    owner: $owner,
    featureName: $featureName,
    enabled: $enabled,
    expiresAt: $expiresAt,
    _version: $version}) {
    id
    lmProfileId
    owner
    featureName
    enabled
    expiresAt
    createdAt
    updatedAt
    _version
    _deleted
    _lastChangedAt
    __typename
  }
}
`

const updateLmFeature = gql`
mutation UpdateLmFeature(
  $id: ID!
  $enabled: Boolean
  $expiresAt: AWSDateTime
  $version: Int
) {
  updateLmFeature(input: {
    id: $id, 
    enabled: $enabled,
    expiresAt: $expiresAt,
    _version: $version}) {
    id
    lmProfileId
    owner
    featureName
    enabled
    expiresAt
    createdAt
    updatedAt
    _version
    _deleted
    _lastChangedAt
    __typename
  }
}
`

/* Helper Functions */

/* AWS GraphQL Functions */

async function queryProfileByUser(owner) {
    var response = {items:[]}
    var lastToken = null;
    do {
        const result = await client.request({
            query: getLmProfileByUser,
            variables: {
                owner: owner,
                nextToken: lastToken
            }
        });
        console.log(JSON.stringify(result));
        response.items.push(...result.listLmProfiles.items);
        lastToken = result.listLmProfiles.nextToken;
    } while (lastToken);
    return response;
}

async function queryFeatureByUser(featureName, owner) {
    const result = await client.request({
        query: lmFeaturesByUser,
        variables: {
            owner: owner,
            featureName: featureName
        }
    });
    console.log(JSON.stringify(result));
    return result.featuresByUser;
}

async function pushFeatureMutation(feature) {
    const query = feature.__typename ? updateLmFeature : createLmFeature;
    const sresult = await client.request({
        query: query,
        variables: {
            id: feature.id,
            lmProfileId: feature.lmProfileId,
            owner: feature.owner,
            featureName: feature.featureName,
            enabled: feature.enabled,
            expiresAt: feature.expiresAt,
            version: feature._version
        }
    });
    console.log(JSON.stringify(sresult));
}

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log('===env===');
    //console.log(process.env);
    console.log('===event===');
    console.log(JSON.stringify(event, null, 2));

    const ownerId = event.arguments.userId;
    const featureName = event.arguments.featureName;
    const enabled = event.arguments.enabled;
    const expiresAt = event.arguments.expiresAt;

    try {
        console.log('Updating User Feature Access: ', featureName);
        var features = await queryFeatureByUser(featureName, ownerId);
        var profiles = await queryProfileByUser(ownerId);
        var feature = null;

        if (features !== null && features.items.length > 0) {
            var feature = features.items[0];
        }
        if (profiles === null || profiles.items.length == 0) {
            console.log('User Profile not found, aborting.');
            return null;
        }
        var profileId = profiles.items[0].id;
        var date = new Date();
        var timestamp = date.getTime();
        var timeStr = `${date.getFullYear()}-${(date.getMonth()+1).toString().padStart(2,'0')}-${date.getDate().toString().padStart(2,'0')}T${date.getHours().toString().padStart(2,'0')}:${date.getMinutes().toString().padStart(2,'0')}:${date.getSeconds().toString().padStart(2,'0')}.${date.getMilliseconds()}Z`;

        if (feature === null) {
            console.log('Feature not found, generating.');
            feature = {
                id: crypto.randomUUID(),
                lmProfileId: profileId,
                owner: ownerId,
                _version: 0,
                createdAt: timeStr,
                updatedAt: timeStr,
                expiresAt: expiresAt,
                featureName: featureName
            }
        }
        console.log('Fetched Feature: ', feature.featureName);
        feature.enabled = enabled;
        feature.expiresAt = expiresAt;
        console.log(JSON.stringify(feature));

        await pushFeatureMutation(feature);

        console.log('===SUCCESS===');
        return feature;
    } catch (err) {
        console.log('===ERROR===');
        console.log(err);
        return { error: err }
    }
};
