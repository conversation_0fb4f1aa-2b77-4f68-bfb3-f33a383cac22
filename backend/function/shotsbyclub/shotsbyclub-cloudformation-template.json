{"AWSTemplateFormatVersion": "2010-09-09", "Description": "Lambda Function resource stack creation using Amplify CLI", "Parameters": {"CloudWatchRule": {"Type": "String", "Default": "NONE", "Description": " Schedule Expression"}, "deploymentBucketName": {"Type": "String"}, "env": {"Type": "String"}, "s3Key": {"Type": "String"}, "apifullswingflightGraphQLAPIIdOutput": {"Type": "String", "Default": "apifullswingflightGraphQLAPIIdOutput"}, "apifullswingflightGraphQLAPIEndpointOutput": {"Type": "String", "Default": "apifullswingflightGraphQLAPIEndpointOutput"}, "functionfullswingflightstatsSharedArn": {"Type": "String", "Default": "functionfullswingflightstatsSharedArn"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "shotsbyclub", {"Fn::Join": ["", ["shotsbyclub", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "REGION": {"Ref": "AWS::Region"}, "API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT": {"Ref": "apifullswingflightGraphQLAPIIdOutput"}, "API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT": {"Ref": "apifullswingflightGraphQLAPIEndpointOutput"}, "API_FULLSWINGFLIGHT_LMSHOTTABLE_NAME": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmShotTable:Name"}}, "API_FULLSWINGFLIGHT_LMSHOTTABLE_ARN": {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmShotTable:Name"}}]]}, "API_FULLSWINGFLIGHT_LMCLUBTABLE_NAME": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmClubTable:Name"}}, "API_FULLSWINGFLIGHT_LMCLUBTABLE_ARN": {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmClubTable:Name"}}]]}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs18.x", "Layers": [{"Ref": "functionfullswingflightstatsSharedArn"}], "Timeout": "25"}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "fullswingflightLambdaRole48aac822", {"Fn::Join": ["", ["fullswingflightLambdaRole48aac822", "-", {"Ref": "env"}]]}]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}]}}}, "AmplifyResourcesPolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "amplify-lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["appsync:Create*", "appsync:StartSchemaCreation", "appsync:GraphQL", "appsync:Get*", "appsync:List*"], "Resource": [{"Fn::Join": ["", ["arn:aws:appsync:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":apis/", {"Ref": "apifullswingflightGraphQLAPIIdOutput"}, "/*"]]}, {"Fn::Join": ["", ["arn:aws:appsync:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":apis/", {"Ref": "apifullswingflightGraphQLAPIIdOutput"}]]}]}, {"Effect": "Allow", "Action": ["dynamodb:Get*", "dynamodb:BatchGetItem", "dynamodb:List*", "dynamodb:Describe*", "dynamodb:<PERSON><PERSON>", "dynamodb:Query"], "Resource": [{"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmShotTable:Name"}}]]}, {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmShotTable:Name"}}, "/index/*"]]}]}, {"Effect": "Allow", "Action": ["dynamodb:Get*", "dynamodb:BatchGetItem", "dynamodb:List*", "dynamodb:Describe*", "dynamodb:<PERSON><PERSON>", "dynamodb:Query"], "Resource": [{"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmClubTable:Name"}}]]}, {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmClubTable:Name"}}, "/index/*"]]}]}]}}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}}}