/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	API_FULLSWINGFLIGHT_LMCLUBTABLE_ARN
	API_FULLSWINGFLIGHT_LMCLUBTABLE_NAME
	API_FULLSWINGFLIGHT_LMSHOTTABLE_ARN
	API_FULLSWINGFLIGHT_LMSHOTTABLE_NAME
	ENV
	REGION
Amplify Params - DO NOT EDIT */

const AWS = require('aws-sdk');

/*
* DynamoDB
*/
const docClient = new AWS.DynamoDB.DocumentClient();

exports.handler = async (event) => {
    //console.log('===env===');
    //console.log(process.env);
    //console.log('===event===');
    //console.log(event);

    var shotparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMSHOTTABLE_NAME,
        Limit: 50000,
        IndexName: 'byUserInRange',
        KeyConditionExpression: '#owner = :owner AND #timestamp BETWEEN :start AND :end',
        ProjectionExpression: 'clubId, side',
        ExpressionAttributeValues: { 
            ':owner': event.arguments.owner,
            ':start': event.arguments.start,
            ':end': event.arguments.end
        },
        ExpressionAttributeNames: { 
            '#owner': 'owner',
            '#timestamp': 'timestamp'
        },
    }

    try {
        // Prepare results
        var clubHsh = {};
        var returnVal = [];

        // Fetch Shots
        const sdata = await docClient.query(shotparams).promise();
        // console.log('===shots===');
        // console.log(JSON.stringify(sdata));
        for (const shot of sdata['Items']) {
            var clubId = shot['clubId'];
            //console.log('Shot with clubId: ', clubId);

            clubHsh[clubId] = clubHsh[clubId] || 0;
            clubHsh[clubId] += 1;
        }
        for (const clubId in clubHsh) {
            returnVal.push({clubId: clubId, count: clubHsh[clubId]});
        }
        // console.log('===return===');
        // console.log(JSON.stringify(returnVal));

        return returnVal;
    } catch (err) {
        console.log('===ERROR===');
        console.log(err);
        return { error: err }
    }
}