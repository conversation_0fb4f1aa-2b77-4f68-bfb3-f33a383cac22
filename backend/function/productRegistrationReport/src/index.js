/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	API_FULLSWINGFLIGHT_LMDEVICETABLE_ARN
	API_FULLSWINGFLIGHT_LMDEVICETABLE_NAME
	API_FULLSWINGFLIGHT_LMUSERTABLE_ARN
	API_FULLSWINGFLIGHT_LMUSERTABLE_NAME
	ENV
	REGION
Amplify Params - DO NOT EDIT */

const AWS = require('aws-sdk');

/*
* DynamoDB
*/
const docClient = new AWS.DynamoDB.DocumentClient();

/*
* SES
*/
const ses = new AWS.SES();

const cleanStr = (input) => {
    var strIn = input || ""
    return '"' + strIn + '"'
};

const sendAttachmentEmail = async (content, filename) => {
    const SOURCE_EMAIL = "<EMAIL>";

    var dayStr = new Date().toISOString().slice(0, 10);
    var data = Buffer.from(content);
  
    let ses_mail = "From: 'Full Swing Product Registration Report' <" + SOURCE_EMAIL + ">\n";
    ses_mail += "To: " + process.env.SEND_TO + "\n";
    ses_mail += "Subject: Product Registration Report\n";
    ses_mail += "MIME-Version: 1.0\n";
    ses_mail += "Content-Type: multipart/mixed; boundary=\"NextPart\"\n\n";
    ses_mail += "--NextPart\n";
    ses_mail += "Content-Type: text/html\n\n";
    ses_mail += "Product Registrations through " + dayStr + "\n\n";
    ses_mail += "--NextPart\n";
    ses_mail += "Content-Type: application/octet-stream; name=\"" + filename + "\"\n";
    ses_mail += "Content-Transfer-Encoding: base64\n";
    ses_mail += "Content-Disposition: attachment; filename=\"" + filename + "\"; size=" + data.length + ";\n\n";
    ses_mail += data.toString("base64").replace(/([^\0]{76})/g, "$1\n") + "\n\n";
    ses_mail += "--NextPart--";

    // console.log('===email===');
    // console.log(ses_mail);
  
    const params = {
        RawMessage: {
            Data: ses_mail
        },
        Source: "'Full Swing Product Registration Report' <" + SOURCE_EMAIL + ">'"
    };
  
    return new Promise((resolve, reject) => {
        ses.sendRawEmail(params, (err) => {
            if (err) {
                return reject(err);
            }
            return resolve();
        });
    });
};

exports.handler = async (event) => {
    //console.log('===env===');
    //console.log(process.env);
    //console.log('===event===');
    //console.log(event);

    // Capture current day string
    var dayStr = new Date().toISOString().slice(0, 10);

    var deviceparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMDEVICETABLE_NAME,
        ProjectionExpression: '#id, #deleted, #owner, deviceID, createdAt, registered, registeredUser, firmwareVersion, serialNumber',
        ExpressionAttributeValues: { 
            ':registered': true
        },
        ExpressionAttributeNames: { 
            '#id': 'id',
            '#deleted': '_deleted',
            '#owner': 'owner',
            '#registered': 'registered'
        },
        FilterExpression: '#registered = :registered', //add creation date
    }

    try {
        // Prepare results
        var returnVal = [];
        returnVal.push([
            "Device ID",
            "Email",
            "Name",
            "Registration Date",
            "Serial Number",
            "Firmware Version",
            "Company Name",
            "Phone",
            "Shipping Address Line 1",
            "Shipping Address Line 2",
            "Shipping Postcode",
            "Shipping Locality",
            "Shipping Region",
            "Shipping Country"
        ]);

        // Fetch Devices
        var device_result = [];
        var last_eval_key = undefined;
        do {
            deviceparams.ExclusiveStartKey = last_eval_key;
            const sdata = await docClient.scan(deviceparams).promise();
            device_result = [].concat(device_result, sdata.Items);
            last_eval_key = sdata.LastEvaluatedKey;
        } while (last_eval_key !== undefined);

        // console.log('===devices===');
        // console.log(JSON.stringify(device_result));

        const devices_owner = device_result.filter(d => d['owner'] == d['registeredUser'])
        const devices_supplemental = [...new Map(device_result.map(d => [d['deviceID'], d])).values()];
        const devices = [].concat(devices_supplemental, devices_owner);
        const devices_uniq = [...new Map(devices.map(d => [d['deviceID'], d])).values()];
        
        // console.log('===devices===');
        // console.log(JSON.stringify(devices_uniq));

        for (const device of devices_uniq) {
            try {
                // Query LmUser for registered user.  Combine into array to build CSV file.
                var userparams = {
                    TableName: process.env.API_FULLSWINGFLIGHT_LMUSERTABLE_NAME,
                    KeyConditionExpression: '#id = :id',
                    ExpressionAttributeValues: { 
                        ':id': device.registeredUser
                    },
                    ExpressionAttributeNames: { 
                        '#id': 'id'
                    },
                }

                // Fetch User
                const udata = await docClient.query(userparams).promise();
                for (const user of udata['Items']) {
                    // console.log('===user===');
                    // console.log(JSON.stringify(udata));
                    var date = new Date(device['registrationDate'] * 1000);
                    var dateStr = isNaN(date) ? device['createdAt'] : date.toISOString();

                    returnVal.push([cleanStr(device['deviceID']),
                        cleanStr(user['email']),
                        cleanStr(user['fullName']),
                        cleanStr(dateStr),
                        cleanStr(device['serialNumber']),
                        cleanStr(device['firmwareVersion']),
                        cleanStr(user['companyName']),
                        cleanStr(user['phone']),
                        cleanStr(user['shippingAddressLine1']),
                        cleanStr(user['shippingAddressLine2']),
                        cleanStr(user['shippingPostcode']),
                        cleanStr(user['shippingLocality']),
                        cleanStr(user['shippingRegion']),
                        cleanStr(user['shippingCountry'])]);
                }
            } catch (err) {
                console.log('===ERROR===');
                console.log(err);
            }
        }

        // console.log('===return===');
        // console.log(JSON.stringify(returnVal));

        // Email results
        if (returnVal.length > 0) {
            var csv = returnVal.map(x => x.join(',')).join('\n');
            return sendAttachmentEmail(csv, "product_registration_" + dayStr + ".csv");
        }

        return returnVal;
    } catch (err) {
        console.log('===ERROR===');
        console.log(err);
        return { error: err }
    }
}
