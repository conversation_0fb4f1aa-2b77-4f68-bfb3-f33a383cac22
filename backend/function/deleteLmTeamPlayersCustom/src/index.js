/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_ARN
	API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_NAME
	API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_ARN
	API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME
	API_FULLSWINGFLIGHT_LMUSERTABLE_ARN
	API_FULLSWINGFLIGHT_LMUSERTABLE_NAME
	ENV
	REGION
Amplify Params - DO NOT EDIT */
const AWS = require('aws-sdk');
const {success, error} = require('/opt/nodejs/response')
const {returnErrorFn} = require('/opt/nodejs/util')
const docClient = new AWS.DynamoDB.DocumentClient();
const gql = require('graphql-tag');
const AppsyncClient = require('appsync-client').default;

const client = new AppsyncClient({
    // Required
    apiUrl: process.env.API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT,
    // Optional - these will default to process.env values (e.g. the IAM
    // role of the Lambda)
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    sessionToken: process.env.AWS_SESSION_TOKEN
});
const ERROR = {
    TEAM_PLAYER_NOT_FOUND: {
        errorType: "errors.team_player.not_found",
        message: "Team player does not exist or has been deleted."
    },
    TEAM_PLAYER_ID_INVALID: {
        errorType: "errors.team_player.team_layer_id_invalid",
        message: "Team player id invalid."
    },
    NOT_TEAM_OWNER: {
        errorType: "errors.team.not_owner",
        message: "Only the team owner can perform this action."
    },
}
const deleteLmTeamPlayersQuery = gql`
    mutation deleteLmTeamPlayers($input: DeleteLmTeamPlayersInput!) {
        deleteLmTeamPlayers(input: $input) {
            _deleted
            _lastChangedAt
            _version
            createdAt
            email
            id
            readers
            joined_at
            lineupOrderBatting
            lineupOrderPitching
            lmPlayerId
            lmPlayerName
            lmTeamId
            owner
            type
            updatedAt
            team {
                id
                owner
                name
                profileImage
                pitcherId
                createdAt
                updatedAt
                _version
                _deleted
                _lastChangedAt
                __typename
            }
        }}`
const updateLmCoachPlayersQuery = gql`
    mutation updateLmCoachPlayers($input: UpdateLmCoachPlayersInput!) {
        updateLmCoachPlayers(input: $input) {
            id
            lmCoachId
            owner
            lmPlayerId
            lmPlayerName
            status
            lmPlayerEmail
            teamAssigned
            createdAt
            updatedAt
            _version
            _deleted
            _lastChangedAt
            __typename
        }}`

const getLmTeamPlayerByIdFn = async (lmTeamPlayerId) => {
    console.log('Getting team player', lmTeamPlayerId);
    const result = await docClient.get({
        TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME,
        Key: {
            id: lmTeamPlayerId
        }
    }).promise();
    if (!result || !result.Item) return returnErrorFn(ERROR.TEAM_PLAYER_NOT_FOUND, lmTeamPlayerId);
    console.log('Got team player', lmTeamPlayerId);
    return result.Item;
}

const updateTeamPlayerOrderFn = async (lmTeamPlayerId) => {
    console.log('Update order of team player', lmTeamPlayerId);
    try {
        const params = {
            TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME,
            Key: {
                id: lmTeamPlayerId
            },
            UpdateExpression: "set #lineupOrderBatting = :lineupOrderBatting, #lineupOrderPitching = :lineupOrderPitching, #updatedAt = :updatedAt",
            ExpressionAttributeNames: {
                "#lineupOrderPitching": "lineupOrderPitching",
                "#lineupOrderBatting": "lineupOrderBatting",
                "#updatedAt": "updatedAt"
            },
            ExpressionAttributeValues: {
                ":lineupOrderBatting": -1,
                ":lineupOrderPitching": -1,
                ":updatedAt": new Date().toISOString(),
            },
            ReturnValues: "ALL_NEW",
        };
        await docClient.update(params).promise();
        console.log('Updated order of team player', lmTeamPlayerId);
    }catch (e) {
        console.error('Error updating order team player', e);
    }
}

const deleteTeamPlayerFn = async (lmTeamPlayerId, lmPlayerId, email, version) => {
    console.log('Deleting team player', lmTeamPlayerId, 'playerId', lmPlayerId || email);
    const result = await client.request({
        query: deleteLmTeamPlayersQuery,
        variables: {
            input: {id: lmTeamPlayerId, _version: version}
        }
    });
    if (!result || !result['deleteLmTeamPlayers'] || !result['deleteLmTeamPlayers']['id'] || !result['deleteLmTeamPlayers']['_deleted']) throw new Error('Delete team player fn fail');
    await updateTeamPlayerOrderFn(lmTeamPlayerId)
    console.log('Deleted team player', lmTeamPlayerId, 'playerId', lmPlayerId || email);
}
const matchLmCoachPlayerFn = async (lmCoachId, lmPlayerId, lmPlayerEmail) => {
    console.log(`Get list of coach player [${lmPlayerId || lmPlayerEmail}]`);
    const allItems = [];
    let ExclusiveStartKey = undefined;
    do {
        const params = {
            TableName: process.env.API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_NAME,
            IndexName: "byCoach",
            KeyConditionExpression: "lmCoachId = :lmCoachId",
            ExpressionAttributeValues: {
                ":lmCoachId": lmCoachId,
            },
            ExclusiveStartKey,
        };
        const result = await docClient.query(params).promise();
        allItems.push(...result['Items']);
        ExclusiveStartKey = result['LastEvaluatedKey'];
    } while (ExclusiveStartKey);
    if (lmPlayerId) return (allItems || []).find(item => item['lmPlayerId'] === lmPlayerId)
    return (allItems || []).find(item => item['lmPlayerEmail'] === lmPlayerEmail);
}
const updateLmCoachPlayerFn = async (lmCoachPlayerId, version, teamAssigned) => {
    console.log(`Updating coach player [${lmCoachPlayerId}]`, 'teamAssigned=', teamAssigned);
    const input = {
        id: lmCoachPlayerId,
        teamAssigned,
        _version: version,
    };
    const result = await client.request({
        query: updateLmCoachPlayersQuery,
        variables: {
            input
        }
    });
    console.log('----', result)
    if (!result || !result['updateLmCoachPlayers'] || !result['updateLmCoachPlayers']['id']) {
        console.error('Update coach player fn fail', result);
    } else {
        console.log(`Updated coach player`, lmCoachPlayerId)
    }
}

const checkCoachPlayerTeamAssignedFn = async (lmCoachId, lmPlayerId, email) => {
    console.log('Checking coach player team assigned', lmPlayerId);
    let teamAssigned = 'Y';
    const params = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME,
        IndexName: lmPlayerId ? "byPlayer" : "byEmail",
        KeyConditionExpression: lmPlayerId ? "lmPlayerId = :lmPlayerId" : "email = :email",
        FilterExpression: "#owner = :lmCoachId",
        ExpressionAttributeNames: {
            '#owner': 'owner'
        },
        ExpressionAttributeValues: {
            ":lmPlayerId": lmPlayerId,
            ":lmCoachId": lmCoachId,
            ":email": lmPlayerId ? undefined :email,
        }
    };
    const result = await docClient.query(params).promise();
    if (result && result['Items']) {
        const notDeletedTeamPlayers = (result['Items'] || []).filter(item => !item['_deleted']);
        if (notDeletedTeamPlayers.length === 0) {
            const matchCoachPlayer = await matchLmCoachPlayerFn(lmCoachId, lmPlayerId, email);
            if (matchCoachPlayer) {
                teamAssigned = 'N';
                await updateLmCoachPlayerFn(matchCoachPlayer['id'], matchCoachPlayer['_version'], teamAssigned);
            }
        }
    }
    return teamAssigned
}

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log('===env===', event);
    try {
        const lmTeamPlayerId = event.arguments['lmTeamPlayerId'] && event.arguments['lmTeamPlayerId'] !== 'null' ? event.arguments['lmTeamPlayerId'] : undefined;
        const owner = event.identity.username;
        console.log('Deleting team player: ', lmTeamPlayerId);
        if (!lmTeamPlayerId) return returnErrorFn(ERROR.TEAM_PLAYER_ID_INVALID);
        const teamPlayer = await getLmTeamPlayerByIdFn(lmTeamPlayerId);
        if (teamPlayer['owner'] !== owner) return returnErrorFn(ERROR.TEAM_PLAYER_NOT_FOUND, lmTeamPlayerId);
        await deleteTeamPlayerFn(lmTeamPlayerId, teamPlayer['lmPlayerId'], teamPlayer['email'], teamPlayer['_version']);
        const teamAssigned = await checkCoachPlayerTeamAssignedFn(owner, teamPlayer['lmPlayerId'], teamPlayer['email']);
        const response = {teamAssigned};
        return success({...response});
    } catch (e) {
        console.error(e)
        return error(e.name === "CUSTOM_ERROR" ? {errorType: e.code, message: e.message} : undefined);
    }
};
