/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_ARN
	API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_NAME
	API_FULLSWINGFLIGHT_LMTEAMCOACHESTABLE_ARN
	API_FULLSWINGFLIGHT_LMTEAMCOACHESTABLE_NAME
	API_FULLSWINGFLIGHT_LMTEAMINVITECODESTABLE_ARN
	API_FULLSWINGFLIGHT_LMTEAMINVITECODESTABLE_NAME
	API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_ARN
	API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME
	API_FULLSWINGFLIGHT_LMTEAMTABLE_ARN
	API_FULLSWINGFLIGHT_LMTEAMTABLE_NAME
	AUTH_FULLSWINGFLIGHT03FF7FF503FF7FF5_USERPOOLID
	ENV
	FUNCTION_CREATEBRANCHIOINVITELINK_NAME
	REGION
Amplify Params - DO NOT EDIT */

const AWS = require('aws-sdk');
const {
    CognitoIdentityProviderClient,
    ListUsersCommand,
    AdminGetUserCommand
} = require('@aws-sdk/client-cognito-identity-provider');
const {v4: uuidv4} = require('uuid');
const {success, error} = require('/opt/nodejs/response')
const {isEmailFn, returnErrorFn, getValidArgument} = require('/opt/nodejs/util')
const ses = new AWS.SES({region: process.env.REGION});
const docClient = new AWS.DynamoDB.DocumentClient();
const lambda = new AWS.Lambda();
const gql = require('graphql-tag');
const AppsyncClient = require('appsync-client').default;

const cognitoIdentityServiceProvider = new CognitoIdentityProviderClient({});
const client = new AppsyncClient({
    // Required
    apiUrl: process.env.API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT,
    // Optional - these will default to process.env values (e.g. the IAM
    // role of the Lambda)
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    sessionToken: process.env.AWS_SESSION_TOKEN
});
const createLmTeamPlayerQuery = gql`
    mutation createLmTeamPlayers($input: CreateLmTeamPlayersInput!) {
        createLmTeamPlayers(input: $input) {
            id
            owner
            readers
            type
            status
            email
            joined_at
            lmTeamId
            lmPlayerId
            lmPlayerName
            lineupOrderBatting
            lineupOrderPitching
            team {
                id
                owner
                name
                profileImage
                pitcherId
                createdAt
                updatedAt
                _version
                _deleted
                _lastChangedAt
                __typename
            }
            createdAt
            updatedAt
            _version
            _deleted
            _lastChangedAt
            __typename
        }}`
const getLmTeamPlayersQuery = gql`
    query getLmTeamPlayers($id: ID!) {
        getLmTeamPlayers(id: $id) {
            _deleted
            _lastChangedAt
            _version
            createdAt
            updatedAt
            email
            id
            status
            joined_at
            lineupOrderBatting
            lineupOrderPitching
            lmPlayerId
            lmPlayerName
            lmTeamId
            owner
            type
            team {
                id
                owner
                name
                profileImage
                pitcherId
                createdAt
                updatedAt
                _version
                _deleted
                _lastChangedAt
                __typename
            }
        }
    }
`
const createLmCoachPlayersQuery = gql`
    mutation createLmCoachPlayers($input: CreateLmCoachPlayersInput!) {
        createLmCoachPlayers(input: $input) {
            _deleted
            _lastChangedAt
            _version
            createdAt
            id
            lmCoachId
            lmPlayerEmail
            lmPlayerId
            lmPlayerName
            owner
            teamAssigned
            updatedAt
        }}`
const updateLmCoachPlayersQuery = gql`
    mutation updateLmCoachPlayers($input: UpdateLmCoachPlayersInput!) {
        updateLmCoachPlayers(input: $input) {
            id
            lmCoachId
            owner
            lmPlayerId
            lmPlayerName
            status
            lmPlayerEmail
            teamAssigned
            createdAt
            updatedAt
            _version
            _deleted
            _lastChangedAt
            __typename
        }}`;
const updateLmTeamQuery = gql`
    mutation updateLmTeam($input: UpdateLmTeamInput!) {
        updateLmTeam(input: $input) {
            _deleted
            _lastChangedAt
            _version
            createdAt
            id
            name
            owner
            pitcherId
            profileImage
            updatedAt
            readers
        }}`;
const updateLmTeamPlayerQuery = gql`
    mutation updateLmTeamPlayers($input: UpdateLmTeamPlayersInput!) {
        updateLmTeamPlayers(input: $input) {
            id
            owner
            readers
            type
            status
            email
            joined_at
            lmTeamId
            lmPlayerId
            lmPlayerName
            lineupOrderBatting
            lineupOrderPitching
            team {
                id
                owner
                name
                profileImage
                pitcherId
                createdAt
                updatedAt
                _version
                _deleted
                _lastChangedAt
                __typename
            }
            createdAt
            updatedAt
            _version
            _deleted
            _lastChangedAt
            __typename
        }}`;
const MAX_TEAM_PLAYERS = 30;
const ERROR = {
    EMAIL_INVALID_FORMAT: {
        errorType: "errors.common.email_invalid_format",
        message: "Email format is invalid."
    },
    USER_NOT_FOUND: {
        errorType: "errors.user.not_found",
        message: "User does not exist or has been deleted."
    },
    TEAM_NOT_FOUND: {
        errorType: "errors.team.not_found",
        message: "Team does not exist or has been deleted."
    },
    TEAM_PLAYER_LIMIT_REACHED: {
        errorType: "errors.team.player_limit_reached",
        message: `A team can have a maximum of ${MAX_TEAM_PLAYERS} players.`
    },
    NOT_TEAM_OWNER: {
        errorType: "errors.team.not_owner",
        message: "Only the team owner can perform this action."
    },
    INVITE_LINK_INVALID: {
        errorType: "errors.invite.invite_link_invalid",
        message: "Invite link is invalid."
    },
    MISSING_PLAYER_IDENTIFIER: {
        errorType: "errors.invite.missing_player_identifier",
        message: "You must provide either an email or a user ID to add a player to the team."
    },
    MISSING_SSM_KEY: {
        errorType: "errors.common.missing_ssm_key",
        message: "SSM key is required."
    },
    CANNOT_ADD_SELF_TO_TEAM: {
        errorType: "errors.team.cannot_add_self",
        message: "You cannot add yourself to the team as a player."
    }
}


const encodeBase64 = (str) => {
    return Buffer.from(str, 'utf-8').toString('base64');
}

const getTeamByIdFn = async (lmTeamId) => {
    const result = await docClient.get({
        TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMTABLE_NAME,
        Key: {
            id: lmTeamId
        }
    }).promise();
    const team = result.Item;
    if (!team) return returnErrorFn(ERROR.TEAM_NOT_FOUND);
    return team;
}

const sendEmailFn = async (email, userName, team, inviteLink, coachId) => {
    console.log(`Sending invitation email to [${email}]`);
    const teamName = team ? team['name'] : undefined;
    let senderName = process.env.AWS_SES_SENDER_NAME;
    if (process.env.ENV !== 'live') {
        senderName += ` (${process.env.ENV})`;
    }
    let coachName = '';
    if (!teamName) {
        const coachInfo = await getCognitoUserByIdFn(coachId);
        if (coachInfo) coachName = coachInfo['name'];
    }
    const from = `=?utf-8?B?${encodeBase64(senderName)}?= <${process.env.AWS_SES_SENDER}>`;
    const subject = teamName ? `Join ${teamName} Team on Full Swing app` : `Join ${coachName}’s roster`;
    const inviteTitle = teamName ? `You're invited to join the ${teamName} on Full Swing.` : `You're invited to join ${coachName}’s roster.`
    const joinLinkTitle = teamName ? `Join ${teamName} on Full Swing` : 'Join the roster';
    const body = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <style>
        html {
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            font-family: "Trebuchet MS", Roboto, RobotoDraft, Helvetica, Arial, sans-serif;
        }

        body {
            margin: 0 auto;
            font-family: "Trebuchet MS", Roboto, RobotoDraft, Helvetica, Arial, sans-serif
        }

        p, h3, h2, span, a {
            font-family: "Trebuchet MS", Roboto, RobotoDraft, Helvetica, Arial, sans-serif
        }

        .note-div p {
            font-size: 12px;
            color: #4d4d4d;
        }
    </style>
</head>
<body>
<div>
    <p>Hello,</p>
    <p>${inviteTitle}</p>
    <div>
        <p style="display: inline; margin: 0;">Click the link below to accept the invitation and get started: </p>
        <a href="${inviteLink}" style="display: inline;color: #007bff;">${joinLinkTitle}</a>
    </div>
    <p>Cheers,<br>Full Swing Sports</p>
    <div class="note-div">
        <p>
            If you didn’t expect this email or believe you received it in error, feel free to ignore it. No action is required on your part.
        </p>
        <p>
            Legal Notice This email contains a personal invitation to join a specific team on the Full Swing platform. By accepting this invitation, you agree to Full Swing’s Terms of Service and Privacy Policy, available at <a href="https://www.fullswingsports.com" style="color: #007bff;">www.fullswingsports.com</a>
        </p>
        <p>
            This invitation is non-transferable and intended solely for the recipient. Full Swing reserves the right to revoke invitations at any time without notice. Your use of Full Swing services is subject to applicable laws and regulations.
        </p>
        <p>
           Unsubscribe If you no longer wish to receive team invitations from Full Swing, you can <a href="#" style="color: #007bff;">unsubscribe here</a> or contact <NAME_EMAIL>. Please note that unsubscribing from team invitations may not affect other communications related to your existing Full Swing account, if applicable.
        </p>
        <p>
            This email was sent to ${email} on behalf of ${teamName}. Full Swing, LLC, 1905 Aston Avenue, Suite 100, Carlsbad, CA.
        </p>
    </div>
</div>
</body>
</html>`
    const params = {
        Source: from,
        Destination: {ToAddresses: [email]},
        Message: {
            Subject: {
                Data: subject,
                Charset: 'UTF-8'
            },
            Body: {
                Html: {
                    Data: body,
                    Charset: 'UTF-8'
                }
            }
        }
    };
    await ses.sendEmail(params).promise();
    console.log(`Sent invitation email to ${email}`);
};

const branchIOCreateLinkFn = async (inviteCode, teamName, teamImage) => {
    console.log('Creating BranchIO link for invite code: ', inviteCode);
    const result = await lambda
        .invoke({
            FunctionName: process.env.FUNCTION_CREATEBRANCHIOINVITELINK_NAME,
            InvocationType: 'RequestResponse',
            Payload: JSON.stringify({inviteCode, teamName, teamImage})
        })
        .promise();
    const payload = JSON.parse(result.Payload);
    if (!payload || !payload['link']) return returnErrorFn(ERROR.INVITE_LINK_INVALID);
    const link = payload['link'];
    console.log('Created BranchIO link for invite code: ', inviteCode, link);
    return link;
}

const getCognitoUserByEmailFn = async (email) => {
    console.log('Getting Cognito user by email: ', email);
    const input = {
        UserPoolId: process.env.AUTH_FULLSWINGFLIGHT03FF7FF503FF7FF5_USERPOOLID,
        Filter: `email = "${email}"`,
        Limit: 1
    };
    const command = new ListUsersCommand(input);
    const response = await cognitoIdentityServiceProvider.send(command);
    const user = response.Users?.[0];
    const userInfo = {};
    if (user) {
        userInfo['userName'] = user['Username'];
        userInfo['userStatus'] = user['UserStatus'];
        user.Attributes.forEach(attr => {
            userInfo[attr.Name] = attr.Value;
        });
    }
    console.log('Got Cognito user by email: ', email, userInfo);
    return userInfo;
}

const getCognitoUserByIdFn = async (userId) => {
    console.log('Getting Cognito user by ID: ', userId);
    const command = new AdminGetUserCommand({
        UserPoolId: process.env.AUTH_FULLSWINGFLIGHT03FF7FF503FF7FF5_USERPOOLID,
        Username: userId,
    });

    const user = await cognitoIdentityServiceProvider.send(command);
    if (!user) return returnErrorFn(ERROR.USER_NOT_FOUND, userId);
    const userInfo = {};
    userInfo['userName'] = user['Username'];
    userInfo['userStatus'] = user['UserStatus'];
    user.UserAttributes.forEach(attr => {
        userInfo[attr.Name] = attr.Value;
    });
    console.log('Got Cognito user by ID: ', userId, userInfo);
    return userInfo;
};

const createTeamInviteCodeFn = async (inviteId, lmTeamId, link, owner, email, fullName) => {
    console.log('Creating invite code for team: ', lmTeamId);
    const currentTime = new Date().toISOString();
    const inviteCode = {
        email,
        id: inviteId,
        link,
        owner: owner,
        sport: 'Baseball',
        type: 'AddPlayerInvitation',
        lmTeamId: lmTeamId,
        lmPlayerName: fullName,
        createdAt: currentTime,
        updatedAt: currentTime,
    };
    await docClient.put({
        TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMINVITECODESTABLE_NAME,
        Item: inviteCode
    }).promise();
    console.log('Created invite code for team: ', lmTeamId, inviteCode);
}

const addCoachPlayerFn = async (lmCoachId, lmPlayerId, lmPlayerEmail, teamAssigned, fullName, userInfo, isManual = false) => {
    console.log(`Adding coach player, coach [${lmCoachId}] player [${lmPlayerId || lmPlayerEmail}]`);

    try {
        const params = {
            TableName: process.env.API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_NAME,
            IndexName: "byCoach",
            KeyConditionExpression: "lmCoachId = :lmCoachId",
            FilterExpression: "lmPlayerId = :lmPlayerId",
            ExpressionAttributeValues: {
                ":lmCoachId": lmCoachId,
                ":lmPlayerEmail": lmPlayerEmail,
                ":lmPlayerId": lmPlayerId
            }
        };
        if (!lmPlayerId) {
            params['FilterExpression'] = "lmPlayerEmail = :lmPlayerEmail"
        }
        if (lmPlayerId && !userInfo) {
            userInfo = await getCognitoUserByIdFn(lmPlayerId);
        }
        const result = await docClient.query(params).promise();
        if (!result || !result['Items'] || result['Items'].length === 0) {
            const input = {
                lmCoachId,
                owner: lmCoachId,
                teamAssigned,
                status: isManual ? 'Pending' : 'Accepted',
            };
            if (lmPlayerId) {
                input['lmPlayerId'] = lmPlayerId;
            }
            if (lmPlayerEmail || userInfo) input['lmPlayerEmail'] = lmPlayerEmail || userInfo['email'];
            if (fullName) {
                input['lmPlayerName'] = fullName
            } else {
                if (userInfo) {
                    input['lmPlayerName'] = userInfo['name'];
                }
            }
            const result = await client.request({
                query: createLmCoachPlayersQuery,
                variables: {
                    input
                }
            });
            if (!result || !result['createLmCoachPlayers'] || !result['createLmCoachPlayers']['id']) throw new Error(`Add coach player, coach [${lmCoachId}] player [${lmPlayerId || lmPlayerEmail}] failed`);
            console.log(`Added coach player, coach [${lmCoachId}] player [${lmPlayerId || lmPlayerEmail}]`);
            if (teamAssigned === 'N') return result['createLmCoachPlayers'];
        } else if (result && result['Items'] && result['Items'].length > 0) {
            if (teamAssigned === 'Y' && result['Items'][0]['teamAssigned'] === 'N') {
                await updateCoachPlayerTeamAssignedFn(result['Items'][0], 'Y');
            } else console.log(`Coach player, coach [${lmCoachId}] player [${lmPlayerId || lmPlayerEmail}] already exists`);
        }
    } catch (e) {
        console.error(`Adding coach player, coach [${lmCoachId}] player [${lmPlayerId || lmPlayerEmail}] has error`, e)
    }
}

const updateCoachPlayerTeamAssignedFn = async (item, teamAssigned) => {
    console.log(`Updating coach player [${item['id']}] team assigned to ${teamAssigned}]`);
    try {
        const result = await client.request({
            query: updateLmCoachPlayersQuery,
            variables: {
                input: {
                    id: item['id'],
                    teamAssigned,
                    _version: item['_version'],
                }
            }
        });
        if (!result || !result['updateLmCoachPlayers'] || !result['updateLmCoachPlayers']['id']) {
            console.error('Update coach player fn fail', result);
        } else {
            console.log(`Updated coach player [${item['id']}] team assigned to ${teamAssigned}]`)
        }
    } catch (e) {
        console.log(`Error updating coach player [${item['id']}] team assigned to ${teamAssigned}]`, e);
    }
}

const restoreTeamPlayerFn = async (lmTeamPlayerId, version, playerType, lineupOrderBatting, lineupOrderPitching, team, email, fullName, isManual) => {
    console.log(`Restoring player [${lmTeamPlayerId}]`);
    const params = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME,
        Key: {
            id: lmTeamPlayerId
        },
        UpdateExpression: "set #_deleted = :deleted",
        ExpressionAttributeNames: {
            "#_deleted": "_deleted",
        },
        ExpressionAttributeValues: {
            ":deleted": null,
        },
        ReturnValues: "ALL_NEW",
    };
    await docClient.update(params).promise();
    const input = {
        id: lmTeamPlayerId,
        type: playerType || 'Batting',
        lineupOrderBatting,
        lineupOrderPitching,
        _version: version,
        status: isManual ? 'Pending' : 'Accepted',
        lmPlayerName: fullName,
    };
    const resultUpdateLmTeamPlayer = await client.request({
        query: updateLmTeamPlayerQuery,
        variables: {
            input
        }
    });
    if (!resultUpdateLmTeamPlayer || !resultUpdateLmTeamPlayer['updateLmTeamPlayers'] || !resultUpdateLmTeamPlayer['updateLmTeamPlayers']['id']) {
        console.error('Update team player fn fail', resultUpdateLmTeamPlayer);
    } else {
        console.log(`Updated team player [${lmTeamPlayerId}]`)
    }
    if (team && team['id'] && email) {
        const inviteId = uuidv4();
        const inviteLink = await branchIOCreateLinkFn(inviteId, team ? team['name'] : 'Substitute', team ? team['image'] : '');
        await createTeamInviteCodeFn(inviteId, team['id'], inviteLink, team['owner'], email);
        await sendEmailFn(email, undefined, team, inviteLink, team['owner']);
    }
    console.log(`Restored player [${lmTeamPlayerId}]`);
}

const getLmTeamPlayerFn = async (lmTeamPlayerId) => {
    console.log('Getting lmTeamPlayerId', lmTeamPlayerId);
    try {
        const result = await client.request({
            query: getLmTeamPlayersQuery,
            variables: {
                id: lmTeamPlayerId,
            }
        });
        if (result && result['getLmTeamPlayers']) {
            console.log('Got lmTeamPlayerId', lmTeamPlayerId);
            return result['getLmTeamPlayers'];
        }
    } catch (e) {
        console.error('Error getting lmTeamPlayerId', e);
    }
}

const addTeamPlayerFn = async (lmPlayerId, email, lmTeamId, owner, fullName, userInfo, playerType = 'Batting', isManual = false, team) => {
    console.log(`Adding player [${lmPlayerId || email}] to team [${lmTeamId}]`);
    let restorePlayerName = fullName;
    const addedPlayer = {
        lmPlayerName: fullName
    }
    if (lmPlayerId && !userInfo) {
        userInfo = await getCognitoUserByIdFn(lmPlayerId);
    }
    if (userInfo) {
        if (!fullName) {
            fullName = userInfo['name'];
            addedPlayer['lmPlayerName'] = fullName;
        }
    }
    if (!lmTeamId) {
        console.log('Skipping adding player to team, team ID is missing.')
        return addedPlayer;
    }
    addedPlayer['isExist'] = false;
    const params = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME,
        IndexName: "byTeam",
        KeyConditionExpression: "lmTeamId = :lmTeamId",
        ExpressionAttributeValues: {
            ":lmTeamId": lmTeamId,
        }
    };
    const result = await docClient.query(params).promise();
    let existPlayer = undefined;
    let lineupOrderBatting = playerType === 'Batting' || playerType === 'Both' ? 1 : 0;
    let lineupOrderPitching = playerType === 'Pitching' || playerType === 'Both' ? 1 : 0;
    if (result && result['Items'] && result['Items'].length > 0) {
        const players = result['Items'].filter(item => item['_deleted'] !== true);
        if (players.length >= MAX_TEAM_PLAYERS) return returnErrorFn(ERROR.TEAM_PLAYER_LIMIT_REACHED);
        if (playerType === 'Batting' || playerType === 'Both') {
            const battingPlayers = players.filter(item => item['type'] === 'Batting' || item['type'] === 'Both');
            const maxOrder = Math.max(0, ...battingPlayers.map(o => o.lineupOrderBatting));
            lineupOrderBatting = maxOrder > battingPlayers.length ? maxOrder + 1 : battingPlayers.length + 1;
        }
        if (playerType === 'Pitching' || playerType === 'Both') {
            const pitchingPlayers = players.filter(item => item['type'] === 'Pitching' || item['type'] === 'Both');
            const maxOrder = Math.max(0, ...pitchingPlayers.map(o => o.lineupOrderPitching));
            lineupOrderPitching = maxOrder > pitchingPlayers.length ? maxOrder + 1 : pitchingPlayers.length + 1;
        }
        if (lmPlayerId) {
            existPlayer = result['Items'].find(item => item['lmPlayerId'] === lmPlayerId);
        } else {
            existPlayer = result['Items'].find(item => item['email'] === email);
        }
    }
    if (!existPlayer) {
        const input = {
            lmTeamId: lmTeamId,
            owner,
            type: playerType,
            lineupOrderBatting,
            lineupOrderPitching,
            status: isManual ? 'Pending' : 'Accepted',
        };
        if (!isManual && lmPlayerId) {
            await updateReadersOfTeamFn(lmTeamId, lmPlayerId);
        }
        if (!isManual) input['joined_at'] = new Date().toISOString();
        if (lmPlayerId) {
            input['lmPlayerId'] = lmPlayerId;
            input['readers'] = [lmPlayerId]
        }
        if (email || userInfo) input['email'] = email || userInfo['email'];
        if (fullName) input['lmPlayerName'] = fullName;
        const result = await client.request({
            query: createLmTeamPlayerQuery,
            variables: {
                input
            }
        });
        if (!result || !result['createLmTeamPlayers'] || !result['createLmTeamPlayers']['id']) throw new Error(`Add player [${email}] to team [${lmTeamId}] failed`);
        addedPlayer['lmTeamPlayerId'] = result['createLmTeamPlayers']['id'];
        addedPlayer['teamPlayer'] = result['createLmTeamPlayers'];
        console.log(`Added player [${lmPlayerId || email}] to team [${lmTeamId}]`);
    } else {
        if (existPlayer['_deleted'] === true) {
            if (!restorePlayerName) restorePlayerName = existPlayer['lmPlayerName'];
            const [lmTeamPlayer] = await Promise.all([
                getLmTeamPlayerFn(existPlayer['id']),
                restoreTeamPlayerFn(existPlayer['id'], existPlayer['_version'], playerType, lineupOrderBatting, lineupOrderPitching, team, userInfo ? userInfo['email'] : undefined, restorePlayerName, isManual)
            ])
            if (lmTeamPlayer) {
                lmTeamPlayer['status'] = isManual ? 'Pending' : 'Accepted';
                if (isManual) lmTeamPlayer['joined_at'] = null;
                addedPlayer['teamPlayer'] = lmTeamPlayer;
            }
        } else {
            addedPlayer['isExist'] = true
            console.log(`Player [${lmPlayerId || email}] already exists in team [${lmTeamId}]`);
        }
    }
    return addedPlayer;
}

const updateReadersOfTeamFn = async (lmTeamId, reader) => {
    if (lmTeamId && reader) {
        try {
            console.log(`Updating readers of team [${lmTeamId}] add reader [${reader}]`);
            const result = await docClient.get({
                TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMTABLE_NAME,
                Key: {
                    id: lmTeamId
                }
            }).promise();
            if (!result || !result.Item) throw new Error(`Team [${lmTeamId}] not found`);
            const item = result.Item;
            const readers = item['readers'] || [];
            if (!readers.includes(reader)) readers.push(reader);
            const resultUpdate = await client.request({
                query: updateLmTeamQuery,
                variables: {
                    input: {
                        id: item['id'],
                        _version: item['_version'],
                        readers
                    }
                }
            });
            if (!resultUpdate || !resultUpdate['updateLmTeam'] || !resultUpdate['updateLmTeam']['id']) {
                console.error(`Fail updating readers of team [${lmTeamId}] add reader [${reader}]`, resultUpdate);
            } else {
                console.log(`Updated readers of team [${lmTeamId}] add reader [${reader}]`);
            }
        } catch (e) {
            console.error(`Error updating readers of team [${lmTeamId}] add reader [${reader}]`, e);
        }
    } else {
        console.log(`Skipping updating readers of team [${lmTeamId}] add reader [${reader}]`);
    }

}

const addPlayerToTeamByEmailFn = async (lmTeamId, email, team, owner, fullName, playerType) => {
    console.log(`Adding player [${email}] to team [${lmTeamId}]`);
    const cognitoUser = await getCognitoUserByEmailFn(email);
    let addedPlayer = {}
    const inviteId = uuidv4();
    if (cognitoUser && cognitoUser['userName'] && cognitoUser['userStatus'] === 'CONFIRMED') {
        if (owner === cognitoUser['userName']) return returnErrorFn(ERROR.CANNOT_ADD_SELF_TO_TEAM);
        const inviteLink = await branchIOCreateLinkFn(inviteId, team ? team['name'] : 'Substitute', team ? team['image'] : '');
        await createTeamInviteCodeFn(inviteId, lmTeamId, inviteLink, owner, email, fullName);
        const [addedPlayerPrmA, _, coachPlayer] = await Promise.all([
            addTeamPlayerFn(cognitoUser['userName'], undefined, lmTeamId, owner, fullName, cognitoUser, playerType, true, team),
            sendEmailFn(email, undefined, team, inviteLink, owner),
            addCoachPlayerFn(owner, cognitoUser['userName'], undefined, lmTeamId ? 'Y' : 'N', fullName, cognitoUser, true),
        ])
        addedPlayer = addedPlayerPrmA;
        addedPlayer['coachPlayer'] = coachPlayer;
    } else {
        const inviteLink = await branchIOCreateLinkFn(inviteId, team ? team['name'] : 'Substitute', team ? team['image'] : '');
        await createTeamInviteCodeFn(inviteId, lmTeamId, inviteLink, owner, email);
        const [addedPlayerPrmA, _, coachPlayer] = await Promise.all([
            addTeamPlayerFn(undefined, email, lmTeamId, owner, fullName, undefined, playerType, true, team),
            sendEmailFn(email, undefined, team, inviteLink, owner),
            addCoachPlayerFn(owner, undefined, email, lmTeamId ? 'Y' : 'N', fullName, undefined, true),
        ]);
        addedPlayer = addedPlayerPrmA;
        addedPlayer['coachPlayer'] = coachPlayer;
    }
    console.log(`Added player [${email}] to team [${lmTeamId}]`);
    return addedPlayer;
}
/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log('===env===', event);
    try {
        const lmTeamId = getValidArgument(event.arguments['lmTeamId']);
        const email = getValidArgument(event.arguments['email']);
        const userId = getValidArgument(event.arguments['userId']);
        const fullName = getValidArgument(event.arguments['fullName']);
        const playerType = getValidArgument(event.arguments['playerType']) || 'Batting';
        const owner = event.identity.username;
        console.log(`Inviting player to team [${lmTeamId}]`, email || userId);
        if (!email && !userId) return returnErrorFn(ERROR.MISSING_PLAYER_IDENTIFIER);
        if (email && !isEmailFn(email)) return returnErrorFn(ERROR.EMAIL_INVALID_FORMAT, email);
        let team;
        let addedPlayer = {}
        if (lmTeamId) {
            team = await getTeamByIdFn(lmTeamId);
            if (team['owner'] !== owner) return returnErrorFn(ERROR.NOT_TEAM_OWNER);
        }
        if (userId) {
            if (userId === owner) return returnErrorFn(ERROR.CANNOT_ADD_SELF_TO_TEAM);
            const [addedPlayerPrmA, coachPlayer] = await Promise.all([
                addTeamPlayerFn(userId, undefined, lmTeamId, owner, fullName, undefined, playerType, false, team),
                addCoachPlayerFn(owner, userId, undefined, lmTeamId ? 'Y' : 'N', fullName, undefined)
            ]);
            addedPlayer = addedPlayerPrmA;
            addedPlayer['coachPlayer'] = coachPlayer;
        } else if (email) {
            addedPlayer = await addPlayerToTeamByEmailFn(lmTeamId, email, team, owner, fullName, playerType);
        }
        console.log(`Invited player [${userId || email}] to team [${lmTeamId}]`);
        return success({...addedPlayer});
    } catch (e) {
        console.error(e)
        return error(e.name === "CUSTOM_ERROR" ? {errorType: e.code, message: e.message} : undefined);
    }
};
