{"permissions": {"api": {"fullswingflight": ["Query", "Mutation"]}, "auth": {"fullswingflight03ff7ff503ff7ff5": ["read"]}, "function": {"createBranchIoInviteLink": ["read"]}, "storage": {"LmTeamPlayers:@model(appsync)": ["create", "read", "update"], "LmTeamCoaches:@model(appsync)": ["read"], "LmTeam:@model(appsync)": ["read", "update"], "LmTeamInviteCodes:@model(appsync)": ["create", "read", "update"], "LmCoachPlayers:@model(appsync)": ["create", "read", "update"]}}, "lambdaLayers": [{"type": "ProjectLayer", "resourceName": "fullswingflightstatsShared", "version": "Always choose latest version", "isLatestVersionSelected": true, "env": "dev"}], "environmentVariableList": [{"cloudFormationParameterName": "awsSesSender", "environmentVariableName": "AWS_SES_SENDER"}, {"cloudFormationParameterName": "awsSesSenderName", "environmentVariableName": "AWS_SES_SENDER_NAME"}]}