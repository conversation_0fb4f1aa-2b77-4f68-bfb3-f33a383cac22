{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"13.0.1\",\"stackType\":\"function-Lambda\",\"metadata\":{\"whyContinueWithGen1\":\"\"}}", "Parameters": {"CloudWatchRule": {"Type": "String", "Default": "NONE", "Description": " Schedule Expression"}, "deploymentBucketName": {"Type": "String"}, "env": {"Type": "String"}, "s3Key": {"Type": "String"}, "apifullswingflightGraphQLAPIIdOutput": {"Type": "String", "Default": "apifullswingflightGraphQLAPIIdOutput"}, "functionfullswingflightstatsSharedArn": {"Type": "String", "Default": "functionfullswingflightstatsSharedArn"}, "functioncreateBranchIoInviteLinkName": {"Type": "String", "Default": "functioncreateBranchIoInviteLinkName"}, "apifullswingflightGraphQLAPIEndpointOutput": {"Type": "String", "Default": "apifullswingflightGraphQLAPIEndpointOutput"}, "authfullswingflight03ff7ff503ff7ff5UserPoolId": {"Type": "String", "Default": "authfullswingflight03ff7ff503ff7ff5UserPoolId"}, "awsSesSender": {"Type": "String"}, "awsSesSenderName": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "addPlayerToTeam", {"Fn::Join": ["", ["addPlayerToTeam", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "REGION": {"Ref": "AWS::Region"}, "API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamPlayersTable:Name"}}, "API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_ARN": {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamPlayersTable:Name"}}]]}, "API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT": {"Ref": "apifullswingflightGraphQLAPIIdOutput"}, "API_FULLSWINGFLIGHT_LMTEAMTABLE_NAME": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamTable:Name"}}, "API_FULLSWINGFLIGHT_LMTEAMTABLE_ARN": {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamTable:Name"}}]]}, "API_FULLSWINGFLIGHT_LMTEAMINVITECODESTABLE_NAME": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamInviteCodesTable:Name"}}, "API_FULLSWINGFLIGHT_LMTEAMINVITECODESTABLE_ARN": {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamInviteCodesTable:Name"}}]]}, "API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_NAME": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmCoachPlayersTable:Name"}}, "API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_ARN": {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmCoachPlayersTable:Name"}}]]}, "FUNCTION_CREATEBRANCHIOINVITELINK_NAME": {"Ref": "functioncreateBranchIoInviteLinkName"}, "API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT": {"Ref": "apifullswingflightGraphQLAPIEndpointOutput"}, "AUTH_FULLSWINGFLIGHT03FF7FF503FF7FF5_USERPOOLID": {"Ref": "authfullswingflight03ff7ff503ff7ff5UserPoolId"}, "API_FULLSWINGFLIGHT_LMTEAMCOACHESTABLE_NAME": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamCoachesTable:Name"}}, "API_FULLSWINGFLIGHT_LMTEAMCOACHESTABLE_ARN": {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamCoachesTable:Name"}}]]}, "AWS_SES_SENDER": {"Ref": "awsSesSender"}, "AWS_SES_SENDER_NAME": {"Ref": "awsSesSenderName"}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs22.x", "Layers": [{"Ref": "functionfullswingflightstatsSharedArn"}], "MemorySize": 3008, "EphemeralStorage": {"Size": 1024}, "Timeout": 29}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "fullswingflightLambdaRole5c2c949c", {"Fn::Join": ["", ["fullswingflightLambdaRole5c2c949c", "-", {"Ref": "env"}]]}]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}]}}}, "AmplifyResourcesPolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "amplify-lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["appsync:GraphQL"], "Resource": [{"Fn::Join": ["", ["arn:aws:appsync:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":apis/", {"Ref": "apifullswingflightGraphQLAPIIdOutput"}, "/types/Query/*"]]}, {"Fn::Join": ["", ["arn:aws:appsync:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":apis/", {"Ref": "apifullswingflightGraphQLAPIIdOutput"}, "/types/Mutation/*"]]}]}, {"Effect": "Allow", "Action": ["cognito-identity:Describe*", "cognito-identity:Get*", "cognito-identity:List*", "cognito-idp:Describe*", "cognito-idp:AdminGetDevice", "cognito-idp:AdminGetUser", "cognito-idp:AdminList*", "cognito-idp:List*", "cognito-sync:Describe*", "cognito-sync:Get*", "cognito-sync:List*", "iam:ListOpenIdConnectProviders", "iam:ListRoles", "sns:ListPlatformApplications"], "Resource": [{"Fn::Join": ["", ["arn:aws:cognito-idp:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":userpool/", {"Ref": "authfullswingflight03ff7ff503ff7ff5UserPoolId"}]]}]}, {"Effect": "Allow", "Action": ["lambda:Get*", "lambda:List*", "lambda:Invoke*"], "Resource": [{"Fn::Join": ["", ["arn:aws:lambda:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":function:", {"Ref": "functioncreateBranchIoInviteLinkName"}]]}]}, {"Effect": "Allow", "Action": ["dynamodb:Put*", "dynamodb:Create*", "dynamodb:BatchWriteItem", "dynamodb:PartiQLInsert", "dynamodb:Get*", "dynamodb:BatchGetItem", "dynamodb:List*", "dynamodb:Describe*", "dynamodb:<PERSON><PERSON>", "dynamodb:Query", "dynamodb:PartiQLSelect", "dynamodb:Update*", "dynamodb:RestoreTable*", "dynamodb:PartiQLUpdate"], "Resource": [{"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamPlayersTable:Name"}}]]}, {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamPlayersTable:Name"}}, "/index/*"]]}]}, {"Effect": "Allow", "Action": ["dynamodb:Get*", "dynamodb:BatchGetItem", "dynamodb:List*", "dynamodb:Describe*", "dynamodb:<PERSON><PERSON>", "dynamodb:Query", "dynamodb:PartiQLSelect"], "Resource": [{"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamCoachesTable:Name"}}]]}, {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamCoachesTable:Name"}}, "/index/*"]]}]}, {"Effect": "Allow", "Action": ["dynamodb:Get*", "dynamodb:BatchGetItem", "dynamodb:List*", "dynamodb:Describe*", "dynamodb:<PERSON><PERSON>", "dynamodb:Query", "dynamodb:PartiQLSelect", "dynamodb:Update*", "dynamodb:RestoreTable*", "dynamodb:PartiQLUpdate"], "Resource": [{"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamTable:Name"}}]]}, {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamTable:Name"}}, "/index/*"]]}]}, {"Effect": "Allow", "Action": ["dynamodb:Put*", "dynamodb:Create*", "dynamodb:BatchWriteItem", "dynamodb:PartiQLInsert", "dynamodb:Get*", "dynamodb:BatchGetItem", "dynamodb:List*", "dynamodb:Describe*", "dynamodb:<PERSON><PERSON>", "dynamodb:Query", "dynamodb:PartiQLSelect", "dynamodb:Update*", "dynamodb:RestoreTable*", "dynamodb:PartiQLUpdate"], "Resource": [{"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamInviteCodesTable:Name"}}]]}, {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamInviteCodesTable:Name"}}, "/index/*"]]}]}, {"Effect": "Allow", "Action": ["dynamodb:Put*", "dynamodb:Create*", "dynamodb:BatchWriteItem", "dynamodb:PartiQLInsert", "dynamodb:Get*", "dynamodb:BatchGetItem", "dynamodb:List*", "dynamodb:Describe*", "dynamodb:<PERSON><PERSON>", "dynamodb:Query", "dynamodb:PartiQLSelect", "dynamodb:Update*", "dynamodb:RestoreTable*", "dynamodb:PartiQLUpdate"], "Resource": [{"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmCoachPlayersTable:Name"}}]]}, {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmCoachPlayersTable:Name"}}, "/index/*"]]}]}]}}}, "CustomLambdaExecutionPolicy": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "custom-lambda-execution-policy", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["ses:SendEmail", "ses:SendRawEmail"], "Resource": [{"Fn::Sub": "arn:aws:ses:${AWS::Region}:${AWS::AccountId}:identity/<EMAIL>"}, {"Fn::Sub": "arn:aws:ses:${AWS::Region}:${AWS::AccountId}:configuration-set/fullswingapps_com-configuration-set"}]}]}, "Roles": [{"Ref": "LambdaExecutionRole"}]}, "DependsOn": "LambdaExecutionRole"}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}}}