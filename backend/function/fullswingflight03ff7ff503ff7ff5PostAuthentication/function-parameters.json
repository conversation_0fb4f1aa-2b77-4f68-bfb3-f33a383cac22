{"trigger": true, "modules": ["custom"], "parentResource": "fullswingflight03ff7ff503ff7ff5", "functionName": "fullswingflight03ff7ff503ff7ff5PostAuthentication", "resourceName": "fullswingflight03ff7ff503ff7ff5PostAuthentication", "parentStack": "auth", "triggerEnvs": "[]", "triggerDir": "/snapshot/amplify-cli/build/node_modules/@aws-amplify/amplify-category-auth/provider-utils/awscloudformation/triggers/PostAuthentication", "triggerTemplate": "PostAuthentication.json.ejs", "triggerEventPath": "PostAuthentication.event.json", "roleName": "fullswingflight03ff7ff503ff7ff5PostAuthentication", "skipEdit": true}