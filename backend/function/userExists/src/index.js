/* Amplify Params - DO NOT EDIT
	AUTH_FULLSWINGFLIGHT03FF7FF503FF7FF5_USERPOOLID
	ENV
	REGION
Amplify Params - DO NOT EDIT */

const AWS = require('aws-sdk');
const cogClient = new AWS.CognitoIdentityServiceProvider()

/* Helper Functions */

async function findCognitoUser(email) {
    var lparams = {
        UserPoolId: process.env.AUTH_FULLSWINGFLIGHT03FF7FF503FF7FF5_USERPOOLID,
        AttributesToGet: ['email'],
        Filter: `email="${email}"`
    }
    const response = await cogClient.listUsers(lparams).promise();
    if (response.Users != null && response.Users.length > 0) 
        return { status: email };

    return { status: "" };
}

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log('===env===');
    //console.log(process.env);
    console.log('===event===');
    console.log(JSON.stringify(event, null, 2));
    
    try {
        console.log(`===Finding user by email ${event.arguments.email}===`);
        var result = await findCognitoUser(event.arguments.email); 
        console.log('Find user result: ', result);
        return result;
    } catch (err) {
        console.log('===ERROR===');
        console.log(err);
        return { error: err }
    }
};
