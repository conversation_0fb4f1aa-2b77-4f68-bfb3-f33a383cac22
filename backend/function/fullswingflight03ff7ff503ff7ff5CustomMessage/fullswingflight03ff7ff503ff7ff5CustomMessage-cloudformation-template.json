{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"12.2.5\",\"stackType\":\"function-Lambda\",\"metadata\":{}}", "Parameters": {"modules": {"Type": "String", "Default": "", "Description": "Comma-delimited list of modules to be executed by a lambda trigger. Sent to resource as an env variable."}, "resourceName": {"Type": "String", "Default": ""}, "trigger": {"Type": "String", "Default": "true"}, "functionName": {"Type": "String", "Default": ""}, "roleName": {"Type": "String", "Default": ""}, "parentResource": {"Type": "String", "Default": ""}, "parentStack": {"Type": "String", "Default": ""}, "env": {"Type": "String"}, "deploymentBucketName": {"Type": "String"}, "s3Key": {"Type": "String"}, "authClientId": {"Type": "String"}, "authUrl": {"Type": "String"}, "bayManagerAdminClientId": {"Type": "String"}, "bayManagerClientId": {"Type": "String"}, "bayManagerResidentialClientId": {"Type": "String"}, "bayManagerResetUrl": {"Type": "String"}, "leaguesClientId": {"Type": "String"}, "leaguesAdminClientId": {"Type": "String"}, "leaguesUrl": {"Type": "String"}, "leaguesAdminUrl": {"Type": "String"}, "userPoolId": {"Type": "String"}, "version": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "fullswingflight03ff7ff503ff7ff5CustomMessage", {"Fn::Join": ["", ["fullswingflight03ff7ff503ff7ff5CustomMessage", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "MODULES": {"Ref": "modules"}, "REGION": {"Ref": "AWS::Region"}, "RESOURCENAME": {"Ref": "resourceName"}, "BAY_MANAGER_ADMIN_CLIENT_ID": {"Ref": "bayManagerAdminClientId"}, "BAY_MANAGER_CLIENT_ID": {"Ref": "bayManagerClientId"}, "BAY_MANAGER_RESET_URL": {"Ref": "bayManagerResetUrl"}, "BAY_MANAGER_RESIDENTIAL_CLIENT_ID": {"Ref": "bayManagerResidentialClientId"}, "LEAGUES_CLIENT_ID": {"Ref": "leaguesClientId"}, "LEAGUES_ADMIN_CLIENT_ID": {"Ref": "leaguesAdminClientId"}, "LEAGUES_ADMIN_URL": {"Ref": "leaguesAdminUrl"}, "LEAGUES_URL": {"Ref": "leaguesUrl"}, "USER_POOL_ID": {"Ref": "userPoolId"}, "VERSION": {"Ref": "version"}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs18.x", "Timeout": 10, "Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "fullswingflight03ff7ff503ff7ff5CustomMessage", {"Fn::Join": ["", ["fullswingflight03ff7ff503ff7ff5CustomMessage", "-", {"Ref": "env"}]]}]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}]}}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}}}