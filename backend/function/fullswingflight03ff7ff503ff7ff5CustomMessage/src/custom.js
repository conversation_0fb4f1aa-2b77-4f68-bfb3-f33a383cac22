const fs = require('fs');
var path = require('path');
const USER_POOL_ID = process.env.USER_POOL_ID;
const AUTH_CLIENT_ID = process.env.AUTH_CLIENT_ID;
const AUTH_URL = process.env.AUTH_URL;
const BAY_MANAGER_ADMIN_CLIENT_ID = process.env.BAY_MANAGER_ADMIN_CLIENT_ID;
const BAY_MANAGER_RESIDENTIAL_CLIENT_ID = process.env.BAY_MANAGER_RESIDENTIAL_CLIENT_ID;
const BAY_MANAGER_CLIENT_ID = process.env.BAY_MANAGER_CLIENT_ID;
const BAY_MANAGER_RESET_URL = process.env.BAY_MANAGER_RESET_URL;
const LEAGUES_CLIENT_ID = process.env.LEAGUES_CLIENT_ID;
const LEAGUES_ADMIN_CLIENT_ID = process.env.LEAGUES_ADMIN_CLIENT_ID;
const LEAGUES_ADMIN_URL = process.env.LEAGUES_ADMIN_URL;
const LEAGUES_URL = process.env.LEAGUES_URL;
const REGION = process.env.REGION;
// const ISS = `https://cognito-idp.${region}.amazonaws.com/${userPoolId}`


exports.handler = async (event)=> {
    console.log('Received event:', JSON.stringify(event, null, 2));

    // Retrieve request parameters from the Lambda function input:
    var headers = event.headers;
    var queryStringParameters = event.queryStringParameters;
    var pathParameters = event.pathParameters;
    var stageVariables = event.stageVariables;

    /*
    CustomMessage_SignUp	Custom message – To send the confirmation code post sign-up.
    CustomMessage_AdminCreateUser	Custom message – To send the temporary password to a new user.
    CustomMessage_ResendCode	Custom message – To resend the confirmation code to an existing user.
    CustomMessage_ForgotPassword	Custom message – To send the confirmation code for Forgot Password request.
    CustomMessage_UpdateUserAttribute	Custom message – When a user's email or phone number is changed, this trigger sends a verification code automatically to the user. Cannot be used for other attributes.
    CustomMessage_VerifyUserAttribute	Custom message – This trigger sends a verification code to the user when they manually request it for a new email or phone number.
    CustomMessage_Authentication	Custom message – To send MFA code during authentication.
 */
    try {

        // this is common to any user (LM/Bay Manager, etc)
        if(event.triggerSource === "CustomMessage_AdminCreateUser") {
            let templatePath = path.join(__dirname, 'html-templates', 'cognito-invitation-html-email-template.html');
            // Ensure that your message contains event.request.codeParameter event.request.usernameParameter. This is the placeholder for the code and username that will be sent to your user.
            event.response.smsMessage = "Welcome to Full Swing! Your user name is " + event.request.usernameParameter + " and temporary password is " + event.request.codeParameter;
            event.response.emailSubject = "Welcome to Full Swing!";
            let message = `Your username is ${event.request.usernameParameter} and temporary password is ${event.request.codeParameter}`;
            // check if the user was onboarded in admin utility.
            if ((event.request.clientMetadata && (event.request.clientMetadata.UserType === 'BayManagerAdmin' || event.request.clientMetadata.UserType === 'BaymanagerCompeteAdmin'))) {
                message = "Welcome to Full Swing and Bay Manager. You have been designated as an FS Bay Manager administrator." +
                    `Your username is ${event.request.usernameParameter} and temporary password is ${event.request.codeParameter}` +
                    `<br/><br/>You can continue to the admin <a href="${BAY_MANAGER_RESET_URL}">FS Bay Manager website</a>.`
            }
            else if (event.request.clientMetadata && event.request.clientMetadata.UserType === 'CompeteAdmin') {
                message = "Welcome to Full Swing and Compete. You have been designated as an FS Compete administrator." +
                    `Your username is ${event.request.usernameParameter} and temporary password is ${event.request.codeParameter}` +
                    `<br/><br/>You can continue to the admin <a href="${LEAGUES_ADMIN_URL}">FS Compete website</a>.`
            }
            else if (event.request.clientMetadata && event.request.clientMetadata.UserType === 'None') {
                message = "Welcome to Full Swing." +
                    `Your username is ${event.request.usernameParameter} and temporary password is ${event.request.codeParameter}` +
                    `<br/><br/>You can continue to the <a href="${AUTH_URL}">FS Auth website</a>.`
            }
            else if ("custom:company_id" in event.request.userAttributes){ // let's keep this as a backward compatibility
                message += `<br/><br/>Please continue signing in via the FS Launcher.`
                // add the link below for sign in to bay manager
                message += `<br/><br/>If you are a Bay Manager user, you can continue this process on the <a href="${BAY_MANAGER_RESET_URL}">manager website</a>.`
            }

            event.response.emailMessage = replaceHtmlToken(templatePath, message);

        }
        else if(event.userPoolId === USER_POOL_ID && "custom:company_id" in event.request.userAttributes) {// check to see if it is coming from bay manager admin portal
            // Identify why was this function invoked

            if (event.triggerSource === "CustomMessage_ResendCode"
                || event.triggerSource === "CustomMessage_UpdateUserAttribute"
                || event.triggerSource === "CustomMessage_VerifyUserAttribute") {
                let templatePath = path.join(__dirname,'html-templates', 'cognito-verification-html-email-template.html');
                // Ensure that your message contains event.request.codeParameter event.request.usernameParameter. This is the placeholder for the code and username that will be sent to your user.
                event.response.smsMessage = "Full Swing Confirmation. Your confirmation code is " +event.request.codeParameter;
                event.response.emailSubject = "Full Swing Confirmation";
                event.response.emailMessage = replaceHtmlToken(templatePath,"Your confirmation code is " + event.request.codeParameter);
            }

            else if (event.triggerSource === "CustomMessage_ForgotPassword"){
                let templatePath = path.join(__dirname,'html-templates', 'cognito-forgot-password-html-email-template.html');
                event.response.smsMessage = "Full Swing Password Reset. Your code is " + event.request.codeParameter;
                event.response.emailSubject = "Full Swing Password Reset";
                // let's check the client metadata which takes precedence
                if (
                    (event.request.clientMetadata &&
                        (event.request.clientMetadata.UserType === 'BayManagerAdmin' || event.request.clientMetadata.UserType === 'BaymanagerCompeteAdmin'))
                    ||
                    (!event.request.clientMetadata &&
                        (event.callerContext.clientId === BAY_MANAGER_RESIDENTIAL_CLIENT_ID || event.callerContext.clientId === BAY_MANAGER_CLIENT_ID))
                    ){
                    let resetUrl = BAY_MANAGER_RESET_URL + `?code=${event.request.codeParameter}&email=${event.request.userAttributes.email}`;
                    event.response.emailMessage = replaceHtmlToken(templatePath, "Click on the link below to reset your password.<br/><br/> "
                        + ` <a href="${resetUrl}">Password Reset</a>`
                        + `<br/><br/>ATTN: If you have authenticated any simulator with this account, either personal or a venue, you are required to re-login as this password reset will invalidate any existing session.`);
                }
                // let's check if either leagues or LM and change the template accordingly
                else if (event.callerContext.clientId === LEAGUES_CLIENT_ID) {
                    let resetUrl = LEAGUES_URL + `/login?type=1&code=${event.request.codeParameter}&username=${event.userName || event.request.userAttributes.email}`;
                    event.response.emailMessage = replaceHtmlToken(templatePath, "Click on the link below to reset your password.<br/><br/>" + ` <a href="${resetUrl}">Password Reset</a>`);
                }
                else if ((event.request.clientMetadata && event.request.clientMetadata.UserType === 'CompeteAdmin')
                    ||
                    (!event.request.clientMetadata  && event.callerContext.clientId === LEAGUES_ADMIN_CLIENT_ID)) {
                    let resetUrl = LEAGUES_ADMIN_URL + `/login?code=${event.request.codeParameter}&username=${event.userName || event.request.userAttributes.email}`;
                    event.response.emailMessage = replaceHtmlToken(templatePath, "Click on the link below to reset your password.<br/><br/>"
                        + ` <a href="${resetUrl}">Password Reset</a>`
                        + `<br/><br/>ATTN: If you have authenticated any simulator with this account, either personal or a venue, you are required to re-login as this password reset will invalidate any existing session.`);
                }
                else if ((event.request.clientMetadata && event.request.clientMetadata.UserType === 'None')
                    ||
                    (!event.request.clientMetadata && event.callerContext.clientId === AUTH_CLIENT_ID)) {
                    let resetUrl = AUTH_URL + `?type=1&code=${event.request.codeParameter}&username=${event.userName || event.request.userAttributes.email}`;
                    event.response.emailMessage = replaceHtmlToken(templatePath, "Click on the link below to reset your password.<br/><br/>" + ` <a href="${resetUrl}">Password Reset</a>`);
                }
                else {
                    event.response.emailMessage = replaceHtmlToken(templatePath, "Your reset code is " + event.request.codeParameter);
                }
            }
        }
        else {
            // lm userpools
            // user signed up, no verify with link
            if (event.triggerSource === "CustomMessage_SignUp"
                || (event.triggerSource === "CustomMessage_ResendCode" && event.request.userAttributes["cognito:user_status"] === "UNCONFIRMED")){
                let templatePath = path.join(__dirname, 'html-templates', 'cognito-invitation-html-email-template.html');
                // let's check if either leagues or LM and change the template accordingly
                if (event.callerContext.clientId === LEAGUES_CLIENT_ID) {
                    let confirmUrl = LEAGUES_URL + `/confirmUser?type=2&code=${event.request.codeParameter}&username=${event.userName || event.request.userAttributes.email}&redirect_url=${encodeURIComponent(LEAGUES_URL)}`;
                    event.response.emailMessage = replaceHtmlToken(templatePath, "Click on the link below to verify your email address<br/><br/>" + ` <a href="${confirmUrl}">Confirm User</a> <!-- This is needed to bypass the custom sent url required by cognito${event.request.linkParameter} -->`);
                }
                else if (event.callerContext.clientId === LEAGUES_ADMIN_CLIENT_ID) {
                    let confirmUrl = LEAGUES_ADMIN_URL + `/confirmUser?code=${event.request.codeParameter}&username=${event.userName || event.request.userAttributes.email}`;
                    event.response.emailMessage = replaceHtmlToken(templatePath, "Click on the link below to verify your email address<br/><br/>" + ` <a href="${confirmUrl}">Confirm User</a> <!-- This is needed to bypass the custom sent url required by cognito${event.request.linkParameter} -->`);
                }
                else if (event.callerContext.clientId === AUTH_CLIENT_ID) {
                    let resetUrl = AUTH_URL + `?type=2&code=${event.request.codeParameter}&username=${event.userName || event.request.userAttributes.email}`;
                    event.response.emailMessage = replaceHtmlToken(templatePath, "Click on the link below to verify your email address<br/><br/>" + ` <a href="${resetUrl}">Confirm User</a> <!-- This is needed to bypass the custom sent url required by cognito${event.request.linkParameter} -->`);
                }
                else {
                    event.response.emailMessage = replaceHtmlToken(templatePath, 'Please click the link below to verify your email address<br/>' + event.request.linkParameter);
                }

                event.response.smsMessage = "Welcome to Full Swing! Please click the link to verify your email address " + event.request.linkParameter;
                event.response.emailSubject = "Welcome to Full Swing!";

            }
            else if (event.triggerSource === "CustomMessage_ForgotPassword") {
                let templatePath = path.join(__dirname, 'html-templates', 'cognito-forgot-password-html-email-template.html');
                event.response.smsMessage = "Full Swing Password Reset. Your reset code is " + event.request.codeParameter;
                event.response.emailSubject = "Full Swing Password Reset";
                if (
                    (event.request.clientMetadata &&
                        (event.request.clientMetadata.UserType === 'BayManagerAdmin' || event.request.clientMetadata.UserType === 'BaymanagerCompeteAdmin'))
                    ||
                    (!event.request.clientMetadata &&
                        (event.callerContext.clientId === BAY_MANAGER_RESIDENTIAL_CLIENT_ID || event.callerContext.clientId === BAY_MANAGER_CLIENT_ID))
                ){
                    let resetUrl = BAY_MANAGER_RESET_URL + `?code=${event.request.codeParameter}&email=${event.request.userAttributes.email}`;
                    event.response.emailMessage = replaceHtmlToken(templatePath, "Click on the link below to reset your password.<br/><br/>"
                        + ` <a href="${resetUrl}">Password Reset</a>`
                        + `<br/><br/>ATTN: If you have authenticated any simulator with this account, either personal or a venue, you are required to re-login as this password reset will invalidate any existing session.`);
                }
                else if (event.callerContext.clientId === LEAGUES_CLIENT_ID) {
                    let resetUrl = LEAGUES_URL + `/login?type=1&code=${event.request.codeParameter}&username=${event.userName || event.request.userAttributes.email}`;
                    event.response.emailMessage = replaceHtmlToken(templatePath, "Click on the link below to reset your password.<br/><br/>" + ` <a href="${resetUrl}">Password Reset</a>`);
                }
                else if ((event.request.clientMetadata && event.request.clientMetadata.UserType === 'CompeteAdmin')
                    ||
                    (!event.request.clientMetadata  && event.callerContext.clientId === LEAGUES_ADMIN_CLIENT_ID)) {
                    let resetUrl = LEAGUES_ADMIN_URL + `/login?code=${event.request.codeParameter}&username=${event.userName || event.request.userAttributes.email}`;
                    event.response.emailMessage = replaceHtmlToken(templatePath, "Click on the link below to reset your password.<br/><br/>"
                        + ` <a href="${resetUrl}">Password Reset</a>`
                        + `<br/><br/>ATTN: If you have authenticated any simulator with this account, either personal or a venue, you are required to re-login as this password reset will invalidate any existing session.`);
                }
                else if ((event.request.clientMetadata && event.request.clientMetadata.UserType === 'None')
                    ||
                    (!event.request.clientMetadata && event.callerContext.clientId === AUTH_CLIENT_ID)) {
                    let resetUrl = AUTH_URL + `?type=1&code=${event.request.codeParameter}&username=${event.userName || event.request.userAttributes.email}`;
                    event.response.emailMessage = replaceHtmlToken(templatePath, "Click on the link below to reset your password.<br/><br/>" + ` <a href="${resetUrl}">Password Reset</a>`);
                }
                else {
                    event.response.emailMessage = replaceHtmlToken(templatePath, "Your reset code is " + event.request.codeParameter);
                }

            }
            else if (event.triggerSource === "CustomMessage_ResendCode"
                || event.triggerSource === "CustomMessage_UpdateUserAttribute"
                || event.triggerSource === "CustomMessage_VerifyUserAttribute") {
                let templatePath = path.join(__dirname,'html-templates', 'cognito-verification-html-email-template.html');
                // Ensure that your message contains event.request.codeParameter event.request.usernameParameter. This is the placeholder for the code and username that will be sent to your user.
                event.response.smsMessage = "Full Swing Confirmation. Your confirmation code is " +event.request.codeParameter;
                event.response.emailSubject = "Full Swing Confirmation";
                event.response.emailMessage = replaceHtmlToken(templatePath,"Your confirmation code is " + event.request.codeParameter);
            }
        }
    } catch (e) {
        console.log(e)
    }
    //console.log('sending response', JSON.stringify(event));
    return event;
}

function replaceHtmlToken(htmlPath, body, token = '{body_content}'){
    let buffer = fs.readFileSync(htmlPath);
    return buffer.toString().replace(token, body);
}



