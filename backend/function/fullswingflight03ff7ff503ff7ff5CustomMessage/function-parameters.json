{"trigger": true, "modules": ["custom"], "parentResource": "fullswingflight03ff7ff503ff7ff5", "functionName": "fullswingflight03ff7ff503ff7ff5CustomMessage", "resourceName": "fullswingflight03ff7ff503ff7ff5CustomMessage", "parentStack": "auth", "triggerEnvs": "[]", "triggerDir": "/snapshot/amplify-cli/build/node_modules/@aws-amplify/amplify-category-auth/provider-utils/awscloudformation/triggers/CustomMessage", "triggerTemplate": "CustomMessage.json.ejs", "triggerEventPath": "CustomMessage.event.json", "roleName": "fullswingflight03ff7ff503ff7ff5CustomMessage", "skipEdit": true, "environmentVariableList": [{"cloudFormationParameterName": "authClientId", "environmentVariableName": "AUTH_CLIENT_ID"}, {"cloudFormationParameterName": "authUrl", "environmentVariableName": "AUTH_URL"}, {"cloudFormationParameterName": "bayManagerAdminClientId", "environmentVariableName": "BAY_MANAGER_ADMIN_CLIENT_ID"}, {"cloudFormationParameterName": "bayManagerClientId", "environmentVariableName": "BAY_MANAGER_CLIENT_ID"}, {"cloudFormationParameterName": "bayManagerResidentialClientId", "environmentVariableName": "BAY_MANAGER_RESIDENTIAL_CLIENT_ID"}, {"cloudFormationParameterName": "bayManagerResetUrl", "environmentVariableName": "BAY_MANAGER_RESET_URL"}, {"cloudFormationParameterName": "leaguesClientId", "environmentVariableName": "LEAGUES_CLIENT_ID"}, {"cloudFormationParameterName": "leaguesAdminClientId", "environmentVariableName": "LEAGUES_ADMIN_CLIENT_ID"}, {"cloudFormationParameterName": "leaguesUrl", "environmentVariableName": "LEAGUES_URL"}, {"cloudFormationParameterName": "userPoolId", "environmentVariableName": "USER_POOL_ID"}, {"cloudFormationParameterName": "version", "environmentVariableName": "VERSION"}]}