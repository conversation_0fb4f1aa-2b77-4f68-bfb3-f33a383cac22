/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	API_FULLSWINGFLIGHT_LMSHOTTABLE_ARN
	API_FULLSWINGFLIGHT_LMSHOTTABLE_NAME
	ENV
	REGION
Amplify Params - DO NOT EDIT */

const AWS = require('aws-sdk');

/*
* DynamoDB
*/
const docClient = new AWS.DynamoDB.DocumentClient();

exports.handler = async (event) => {
    //console.log('===env===');
    //console.log(process.env);
    // console.log('===event===');
    // console.log(event);

    var shotparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMSHOTTABLE_NAME,
        Limit: 50000,
        IndexName: 'byClubCategoryInRange',
        KeyConditionExpression: '#clubCategory = :clubCategory AND #timestamp BETWEEN :start AND :end',
        FilterExpression: '#owner = :owner',
        ProjectionExpression: 'side',
        ExpressionAttributeValues: { 
            ':owner': event.arguments.owner,
            ':clubCategory': event.arguments.clubCategory,
            ':start': event.arguments.start,
            ':end': event.arguments.end
        },
        ExpressionAttributeNames: { 
            '#owner': 'owner',
            '#clubCategory': 'clubCategory',
            '#timestamp': 'timestamp'
        },
    }

    try {
        // Fetch Shots
        const data = await docClient.query(shotparams).promise()
        //console.log(JSON.stringify(data));

        // Prepare results
        var rangeHsh = {};
        var returnVal = [];
        data['Items'].forEach(shot => {
            var bin = 5 * Math.floor(shot['side'] / 5);
            rangeHsh[bin] = rangeHsh[bin] || 0
            rangeHsh[bin] += 1
        });
        for (let range = -75; range <= 75; range+=5) {
            var count = rangeHsh[range] || 0
            returnVal.push({range: range, count: count});
        }
        //console.log(JSON.stringify(returnVal));

        return returnVal;
    } catch (err) {
        return { error: err }
    }
}