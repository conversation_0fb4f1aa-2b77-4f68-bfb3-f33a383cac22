/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	API_FULLSWINGFLIGHT_LMSESSIONSTATSTABLE_ARN
	API_FULLSWINGFLIGHT_LMSESSIONSTATSTABLE_NAME
	ENV
	REGION
Amplify Params - DO NOT EDIT */


const AWS = require('aws-sdk');
const crypto = require('crypto');
//const agent = new http.Agent({ keepAlive: true })
//const dynamodb = new AWS.DynamoDB({ httpOptions: { agent } })
//const docClient = new AWS.DynamoDB.DocumentClient({ httpOptions: { agent } })
const docClient = new AWS.DynamoDB.DocumentClient()

const gql = require('graphql-tag');
const graphql = require('graphql');
const AppsyncClient = require('appsync-client').default;

const client = new AppsyncClient({
    // Required
    apiUrl: process.env.API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT,
    // Optional - these will default to process.env values (e.g. the IAM
    // role of the Lambda)
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    sessionToken: process.env.AWS_SESSION_TOKEN
});

const updateLmSessionStats = gql`
  mutation updateLmSessionStats(
      $input: UpdateLmSessionStatsInput!) {
    updateLmSessionStats(input: $input) {
      id
      shotCount
      owner
      updatedAt
      lmSessionId
      _version
      _lastChangedAt
      _deleted
      createdAt
      averages {
        apex
        attackAngle
        ballCurve
        ballDirection
        ballSpeed
        carryDistance
        clubId
        clubPath
        clubSpeed
        faceAngle
        horizontalLaunchAngle
        launchAngle
        descentAngle
        side
        sideTotal
        smashFactor
        spinAxis
        spinRate
        totalDistance

        normalizedCarryDistance
        normalizedTotalDistance
        normalizedSide
        normalizedSideTotal
        normalizedApex
      }
      values {
        totalDistance
        spinRate
        spinAxis
        smashFactor
        sideTotal
        side
        launchAngle
        horizontalLaunchAngle
        faceAngle
        descentAngle
        clubSpeed
        clubPath
        clubId
        carryDistance
        ballSpeed
        ballDirection
        ballCurve
        attackAngle
        apex
        
        normalizedCarryDistance
        normalizedTotalDistance
        normalizedSide
        normalizedSideTotal
        normalizedApex
      }
    }
  }
`

/* Helper Functions */

function getSessionStatsId(userId, lmSessionId) {
    var userIdParts = userId.split('-');
    var lmSessionIdParts = lmSessionId.split('-');
    return `${userIdParts[0]}-${userIdParts[1]}-${userIdParts[2]}-${lmSessionIdParts[3]}-${lmSessionIdParts[4]}`;
}

function removeItemFromArray(array, item) {
    if (typeof array === "undefined") return;
    const index = array.indexOf(item);
    if (index > -1)
        array.splice(index, 1);
}

function removeValue(value, record) {
    if (record.clubSpeedValid)
        removeItemFromArray(value.clubSpeed, record.clubSpeed);
    if (record.ballSpeedValid)
        removeItemFromArray(value.ballSpeed, record.ballSpeed);
    if (record.smashFactorValid)
        removeItemFromArray(value.smashFactor, record.smashFactor);
    if (record.attackAngleValid)
        removeItemFromArray(value.attackAngle, record.attackAngle);
    if (record.clubPathValid)
        removeItemFromArray(value.clubPath, record.clubPath);
    if (record.launchAngleValid)
        removeItemFromArray(value.launchAngle, record.launchAngle);
    if (record.horizontalLaunchAngleValid)
        removeItemFromArray(value.horizontalLaunchAngle, record.horizontalLaunchAngle);
    if (record.faceAngleValid)
        removeItemFromArray(value.faceAngle, record.faceAngle);
    if (record.spinRateValid)
        removeItemFromArray(value.spinRate, record.spinRate);
    if (record.spinAxisValid)
        removeItemFromArray(value.spinAxis, record.spinAxis);
    if (record.carryDistanceValid)
        removeItemFromArray(value.carryDistance, record.carryDistance);
    if (record.totalDistanceValid)
        removeItemFromArray(value.totalDistance, record.totalDistance);
    if (record.sideValid)
        removeItemFromArray(value.side, record.side);
    if (record.sideTotalValid)
        removeItemFromArray(value.sideTotal, record.sideTotal);
    if (record.ballDirection !== null)
        removeItemFromArray(value.ballDirection, record.ballDirection);
    if (record.ballCurve !== null)
        removeItemFromArray(value.ballCurve, record.ballCurve);
    if (record.apexValid)
        removeItemFromArray(value.apex, record.apex);
    if (record.descentAngleValid)
        removeItemFromArray(value.descentAngle, record.descentAngle);
    
    if (record.normalizedValues != null) {
        if (record.normalizedValues.carryDistance != null)
            removeItemFromArray(value.normalizedCarryDistance, record.normalizedValues.carryDistance);
        if (record.normalizedValues.totalDistance != null)
            removeItemFromArray(value.normalizedTotalDistance, record.normalizedValues.totalDistance);
        if (record.normalizedValues.side != null)
            removeItemFromArray(value.normalizedSide, record.normalizedValues.side);
        if (record.normalizedValues.normalizedValues != null)
            removeItemFromArray(value.sideTotal, record.normalizedValues.sideTotal);
        if (record.normalizedValues.apex != null)
            removeItemFromArray(value.normalizedApex, record.normalizedValues.apex);
    }
}

function updateValue(value, record) {
    if (record.clubSpeedValid)
        value.clubSpeed.push(record.clubSpeed);
    if (record.ballSpeedValid)
        value.ballSpeed.push(record.ballSpeed);
    if (record.smashFactorValid)
        value.smashFactor.push(record.smashFactor);
    if (record.attackAngleValid)
        value.attackAngle.push(record.attackAngle);
    if (record.clubPathValid)
        value.clubPath.push(record.clubPath);
    if (record.launchAngleValid)
        value.launchAngle.push(record.launchAngle);
    if (record.horizontalLaunchAngleValid)
        value.horizontalLaunchAngle.push(record.horizontalLaunchAngle);
    if (record.faceAngleValid)
        value.faceAngle.push(record.faceAngle);
    if (record.spinRateValid)
        value.spinRate.push(record.spinRate);
    if (record.spinAxisValid)
        value.spinAxis.push(record.spinAxis);
    if (record.carryDistanceValid)
        value.carryDistance.push(record.carryDistance);
    if (record.totalDistanceValid)
        value.totalDistance.push(record.totalDistance);
    if (record.sideValid)
        value.side.push(record.side);
    if (record.sideTotalValid)
        value.sideTotal.push(record.sideTotal);
    if (record.ballDirection !== null)
        value.ballDirection.push(record.ballDirection);
    if (record.ballCurve !== null)
        value.ballCurve.push(record.ballCurve);
    if (record.apexValid)
        value.apex.push(record.apex);
    if (record.descentAngleValid)
        value.descentAngle.push(record.descentAngle);
    
    if (record.normalizedValues != null) {
        if (record.normalizedValues.carryDistance != null)
            value.normalizedCarryDistance.push(record.normalizedValues.carryDistance);
        if (record.normalizedValues.totalDistance != null)
            value.normalizedTotalDistance.push(record.normalizedValues.totalDistance);
        if (record.normalizedValues.side != null)
            value.normalizedSide.push(record.normalizedValues.side);
        if (record.normalizedValues.sideTotal != null)
            value.normalizedSideTotal.push(record.normalizedValues.sideTotal);
        if (record.normalizedValues.apex != null)
            value.normalizedApex.push(record.normalizedValues.apex);
    }
}

function initValue(record) {
    return {
        clubId:  record.clubId,
        clubSpeed: record.clubSpeedValid ? [record.clubSpeed] : [],
        ballSpeed: record.ballSpeedValid ? [record.ballSpeed] : [],
        smashFactor: record.smashFactorValid ? [record.smashFactor] : [],
        attackAngle: record.attackAngleValid ? [record.attackAngle] : [],
        clubPath: record.clubPathValid ? [record.clubPath] : [],
        launchAngle: record.launchAngleValid ? [record.launchAngle] : [],
        horizontalLaunchAngle: record.horizontalLaunchAngleValid ? [record.horizontalLaunchAngle] : [],
        faceAngle: record.faceAngleValid ? [record.faceAngle] : [],
        spinRate: record.spinRateValid ? [record.spinRate] : [],
        spinAxis: record.spinAxisValid ? [record.spinAxis] : [],
        carryDistance: record.carryDistanceValid ? [record.carryDistance] : [],
        totalDistance: record.totalDistanceValid ? [record.totalDistance] : [],
        side: record.sideValid ? [record.side] : [],
        sideTotal: record.sideTotalValid ? [record.sideTotal] : [],
        ballDirection: record.ballDirection === null ? [] : [record.ballDirection],
        ballCurve: record.ballCurve === null ? [] : [record.ballCurve],
        apex: record.apexValid ? [record.apex] : [],
        descentAngle: record.descentValid ? [record.descentAngle] : [],

        normalizedCarryDistance: (record.normalizedValues ?? {}).carryDistance ? [record.normalizedValues.carryDistance] : [],
        normalizedTotalDistance: (record.normalizedValues ?? {}).totalDistance ? [record.normalizedValues.totalDistance] : [],
        normalizedSide: (record.normalizedValues ?? {}).side ? [record.normalizedValues.side] : [],
        normalizedSideTotal: (record.normalizedValues ?? {}).sideTotal ? [record.normalizedValues.sideTotal] : [],
        normalizedApex: (record.normalizedValues ?? {}).apex ? [record.normalizedValues.apex] : []
    }
}

function initAverages(record) {
    return {
        clubId: record.clubId,
        clubSpeed: record.clubSpeedValid ? record.clubSpeed : 0,
        ballSpeed: record.ballSpeedValid ? record.ballSpeed : 0,
        smashFactor: record.smashFactorValid ? record.smashFactor : 0,
        attackAngle: record.attackAngleValid ? record.attackAngle : 0,
        clubPath: record.clubPathValid ? record.clubPath : 0,
        launchAngle: record.launchAngleValid ? record.launchAngle : 0,
        horizontalLaunchAngle: record.horizontalLaunchAngleValid ? record.horizontalLaunchAngle : 0,
        faceAngle: record.faceAngleValid ? record.faceAngle : 0,
        spinRate: record.spinRateValid ? record.spinRate : 0,
        spinAxis: record.spinAxisValid ? record.spinAxis : 0,
        carryDistance: record.carryDistanceValid ? record.carryDistance : 0,
        totalDistance: record.totalDistanceValid ? record.totalDistance : 0,
        side: record.sideValid ? record.side : 0,
        sideTotal: record.sideTotalValid ? record.sideTotal : 0,
        ballDirection: record.ballDirection === null ? 0 : record.ballDirection,
        ballCurve: record.ballCurve === null ? 0 : record.ballCurve,
        apex: record.apexValid ? record.apex : 0,
        descentAngle: record.descentValid ? record.descentAngle : 0,

        normalizedCarryDistance: (record.normalizedValues ?? {}).carryDistanceValid ? record.normalizedValues.carryDistance : 0,
        normalizedTotalDistance: (record.normalizedValues ?? {}).totalDistanceValid ? record.normalizedValues.totalDistance : 0,
        normalizedSide: (record.normalizedValues ?? {}).sideValid ? record.normalizedValues.side : 0,
        normalizedSideTotal: (record.normalizedValues ?? {}).sideTotalValid ? record.normalizedValues.sideTotal : 0,
        normalizedApex: (record.normalizedValues ?? {}).apexValid ? record.normalizedValues.apex : 0
    }
}

function computeAverage(list) {
    if (typeof list === "undefined") return 0.0;
    return (list.length == 0) ? 0.0 : list.reduce((a, b) => a+b) / list.length;
}

/* AWS GraphQL Functions */

async function pushSessionStatsMutation(sessionStats) {
    const sresult = await client.request({
        query: updateLmSessionStats,
        variables: {
            input: {
                id: sessionStats.id
            }
        }
    });
    console.log(JSON.stringify(sresult));
}

/* AWS DynamoDB Functions */

async function getStatsBySession(sessionId) {
    var statqparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMSESSIONSTATSTABLE_NAME,
        Limit: 1,
        IndexName: 'bySession',
        KeyConditionExpression: '#lmSessionId = :lmSessionId',
        ProjectionExpression: 'id, lmSessionId, #owner, shotCount, #values, averages',
        ExpressionAttributeValues: { 
            ':lmSessionId': sessionId
        },
        ExpressionAttributeNames: { 
            '#owner': 'owner',
            '#values': 'values',
            '#lmSessionId': 'lmSessionId'
        },
    };
    const statsresponse = await docClient.query(statqparams).promise();
    if (statsresponse.Items.length > 1) 
        console.log('Session Stats Found multiple for Session: ', sessionId);
    if (statsresponse.Items.length > 0) 
        return statsresponse.Items[0];

    //console.log('Session Not Found: ', sessionId);
    return null;
}

async function updateStatsSessionAddClub(sessionStats, record) { 
    var timestamp = new Date();
    var timeStr = `${timestamp.getFullYear()}-${(timestamp.getMonth()+1).toString().padStart(2,'0')}-${timestamp.getDate().toString().padStart(2,'0')}T${timestamp.getHours().toString().padStart(2,'0')}:${timestamp.getMinutes().toString().padStart(2,'0')}:${timestamp.getSeconds().toString().padStart(2,'0')}.${timestamp.getMilliseconds()}Z`;
    var statuparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMSESSIONSTATSTABLE_NAME,
        Key: {
            id: sessionStats.id,
        },
        UpdateExpression: `SET #shotCount = #shotCount + :shotInc, 
            #values = list_append(#values, :values), 
            #averages = list_append(#averages, :averages), 
            #updatedAt = :updatedAt`,
        ExpressionAttributeNames: {
            '#shotCount': 'shotCount',
            '#values': 'values',
            '#averages': 'averages',
            '#updatedAt': 'updatedAt'
        },
        ExpressionAttributeValues: {
            ':shotInc': 1,
            ':values': [initValue(record)],
            ':averages': [initAverages(record)],
            ':updatedAt': timeStr,
        },
    };
    //console.log('Updating Stats: ', JSON.stringify(statuparams, null, 2));
    await docClient.update(statuparams).promise();
}

async function updateStatsSession(sessionStats, values, averages, incCount = 1) { 
    var timestamp = new Date();
    var timeStr = `${timestamp.getFullYear()}-${(timestamp.getMonth()+1).toString().padStart(2,'0')}-${timestamp.getDate().toString().padStart(2,'0')}T${timestamp.getHours().toString().padStart(2,'0')}:${timestamp.getMinutes().toString().padStart(2,'0')}:${timestamp.getSeconds().toString().padStart(2,'0')}.${timestamp.getMilliseconds()}Z`;
    var statuparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMSESSIONSTATSTABLE_NAME,
        Key: {
            id: sessionStats.id,
        },
        UpdateExpression: `SET #shotCount = #shotCount + :shotInc, 
            #values = :values, 
            #averages = :averages, 
            #updatedAt = :updatedAt`,
        ConditionExpression: '#shotCount = :curShotCount',
        ExpressionAttributeNames: {
            '#shotCount': 'shotCount',
            '#values': 'values',
            '#averages': 'averages',
            '#updatedAt': 'updatedAt'
        },
        ExpressionAttributeValues: {
            ':curShotCount': sessionStats.shotCount,
            ':shotInc': incCount,
            ':values': values,
            ':averages': averages,
            ':updatedAt': timeStr,
        },
    };
    //console.log('Updating Stats: ', JSON.stringify(statuparams, null, 2));
    await docClient.update(statuparams).promise();
}

async function pushNewStatsSession(record) {
    var timestamp = new Date();
    var timeStr = `${timestamp.getFullYear()}-${(timestamp.getMonth()+1).toString().padStart(2,'0')}-${timestamp.getDate().toString().padStart(2,'0')}T${timestamp.getHours().toString().padStart(2,'0')}:${timestamp.getMinutes().toString().padStart(2,'0')}:${timestamp.getSeconds().toString().padStart(2,'0')}.${timestamp.getMilliseconds()}Z`;
    const statsId = getSessionStatsId(record.owner, record.lmSessionId);
    var statuparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMSESSIONSTATSTABLE_NAME,
        ConditionExpression: 'attribute_not_exists(id)',
        Item: {
            id: statsId,
            lmSessionId: record.lmSessionId,
            owner: record.owner,
            __typename: 'LmSessionStats',
            _lastChangedAt: timestamp.getTime(),
            _version: 1,
            createdAt: timeStr,
            updatedAt: timeStr,
            shotCount: 1,
            values: [
                initValue(record)
            ],
            averages: [
                initAverages(record)
            ]
        }
    };
    await docClient.put(statuparams).promise();
}

async function syncInsertRecords(unmarshalledRecords) {
    for await (let record of unmarshalledRecords) {
        //console.log('DynamoDB Record: ', JSON.stringify(record, null, 2));
        //console.log('DynamoDB Record: ', record.lmSessionId, record.clubId);

        var tries = 5;
        while (true) {
            try {
                const sessionStats = await getStatsBySession(record.lmSessionId);//100ms
                if (sessionStats != null) {
                    //console.log('SessionStats: ', JSON.stringify(sessionStats, null, 2));
                    //console.log('SessionStats: ', sessionStats.id, sessionStats.shotCount);

                    var values = [];
                    var valuesHsh = {};
                    var averages = [];

                    var foundClub = false;
                    for (let value of sessionStats.values) {
                        if (value.clubId == record.clubId) {
                            foundClub = true;
                            updateValue(value, record);
                        }
                        values.push(value);
                        valuesHsh[value.clubId] = value;
                    }
                    if (!foundClub) {
                        //console.log('Updating Stats with new club: ', sessionStats.shotCount, record.lmSessionId, record.clubId);
                        await updateStatsSessionAddClub(sessionStats, record);//100ms
                        break;
                    }
                    //console.log('Values: ', JSON.stringify(values, null, 2));

                    for (let average of sessionStats.averages) {
                        if (average.clubId == record.clubId) {
                            average.clubSpeed = computeAverage(valuesHsh[average.clubId].clubSpeed);
                            average.ballSpeed = computeAverage(valuesHsh[average.clubId].ballSpeed);
                            average.smashFactor = computeAverage(valuesHsh[average.clubId].smashFactor);
                            average.attackAngle = computeAverage(valuesHsh[average.clubId].attackAngle);
                            average.clubPath = computeAverage(valuesHsh[average.clubId].clubPath);
                            average.launchAngle = computeAverage(valuesHsh[average.clubId].launchAngle);
                            average.horizontalLaunchAngle = computeAverage(valuesHsh[average.clubId].horizontalLaunchAngle);
                            average.faceAngle = computeAverage(valuesHsh[average.clubId].faceAngle);
                            average.spinRate = computeAverage(valuesHsh[average.clubId].spinRate);
                            average.spinAxis = computeAverage(valuesHsh[average.clubId].spinAxis);
                            average.carryDistance = computeAverage(valuesHsh[average.clubId].carryDistance);
                            average.totalDistance = computeAverage(valuesHsh[average.clubId].totalDistance);
                            average.side = computeAverage(valuesHsh[average.clubId].side);
                            average.sideTotal = computeAverage(valuesHsh[average.clubId].sideTotal);
                            average.ballDirection = computeAverage(valuesHsh[average.clubId].ballDirection);
                            average.ballCurve = computeAverage(valuesHsh[average.clubId].ballCurve);
                            average.apex = computeAverage(valuesHsh[average.clubId].apex);
                            average.descentAngle = computeAverage(valuesHsh[average.clubId].descentAngle);

                            average.normalizedCarryDistance = computeAverage(valuesHsh[average.clubId].normalizedCarryDistance);
                            average.normalizedTotalDistance = computeAverage(valuesHsh[average.clubId].normalizedTotalDistance);
                            average.normalizedSide = computeAverage(valuesHsh[average.clubId].normalizedSide);
                            average.normalizedSideTotal = computeAverage(valuesHsh[average.clubId].normalizedSideTotal);
                            average.normalizedApex = computeAverage(valuesHsh[average.clubId].normalizedApex);
                        }
                        averages.push(average);
                    }
                    //console.log('Averages: ', JSON.stringify(averages, null, 2));
                    
                    //console.log('Updating Stats: ', sessionStats.shotCount, record.lmSessionId, record.clubId);
                    await updateStatsSession(sessionStats, values, averages);//200ms success, 500ms failure
                    await pushSessionStatsMutation(sessionStats);
                } else {
                    //console.log('Session Stats not yet created.');

                    await pushNewStatsSession(record);
                    //TODO: 
                    const sessionStats = await getStatsBySession(record.lmSessionId);
                    if (sessionStats != null) {
                        await pushSessionStatsMutation(sessionStats);
                    } else {
                        console.log('Session Stats not created.');
                    }
                }
                break;
            } catch (err) {//ConditionalCheckFailedException
                //console.log('===CONDITION FAILED=== Retries Left: ', tries);
                //console.log(err);
                if (--tries <= 0) throw err;
            }
        }
    }
}

async function syncDeleteRecords(unmarshalledRecords) {
    for await (let record of unmarshalledRecords) {
        //console.log('DynamoDB Del Record: ', JSON.stringify(record, null, 2));
        //console.log('DynamoDB Record: ', record.lmSessionId, record.clubId);

        var tries = 5;
        while (true) {
            try {
                const sessionStats = await getStatsBySession(record.lmSessionId);//100ms
                if (sessionStats != null) {
                    //console.log('SessionStats: ', JSON.stringify(sessionStats, null, 2));
                    //console.log('SessionStats: ', sessionStats.id, sessionStats.shotCount);

                    var values = [];
                    var valuesHsh = {};
                    var averages = [];

                    var foundClub = false;
                    for (let value of sessionStats.values) {
                        if (value.clubId == record.clubId) {
                            foundClub = true;
                            removeValue(value, record);
                        }
                        values.push(value);
                        valuesHsh[value.clubId] = value;
                    }
                    if (!foundClub) {
                        console.log('Session Stats does not contain club to remove. ', record.lmSessionId, record.clubId);
                        break;
                    }
                    //console.log('Values: ', JSON.stringify(values, null, 2));

                    for (let average of sessionStats.averages) {
                        if (average.clubId == record.clubId) {
                            average.clubSpeed = computeAverage(valuesHsh[average.clubId].clubSpeed);
                            average.ballSpeed = computeAverage(valuesHsh[average.clubId].ballSpeed);
                            average.smashFactor = computeAverage(valuesHsh[average.clubId].smashFactor);
                            average.attackAngle = computeAverage(valuesHsh[average.clubId].attackAngle);
                            average.clubPath = computeAverage(valuesHsh[average.clubId].clubPath);
                            average.launchAngle = computeAverage(valuesHsh[average.clubId].launchAngle);
                            average.horizontalLaunchAngle = computeAverage(valuesHsh[average.clubId].horizontalLaunchAngle);
                            average.faceAngle = computeAverage(valuesHsh[average.clubId].faceAngle);
                            average.spinRate = computeAverage(valuesHsh[average.clubId].spinRate);
                            average.spinAxis = computeAverage(valuesHsh[average.clubId].spinAxis);
                            average.carryDistance = computeAverage(valuesHsh[average.clubId].carryDistance);
                            average.totalDistance = computeAverage(valuesHsh[average.clubId].totalDistance);
                            average.side = computeAverage(valuesHsh[average.clubId].side);
                            average.sideTotal = computeAverage(valuesHsh[average.clubId].sideTotal);
                            average.ballDirection = computeAverage(valuesHsh[average.clubId].ballDirection);
                            average.ballCurve = computeAverage(valuesHsh[average.clubId].ballCurve);
                            average.apex = computeAverage(valuesHsh[average.clubId].apex);
                            average.descentAngle = computeAverage(valuesHsh[average.clubId].descentAngle);

                            average.normalizedCarryDistance = computeAverage(valuesHsh[average.clubId].normalizedCarryDistance);
                            average.normalizedTotalDistance = computeAverage(valuesHsh[average.clubId].normalizedTotalDistance);
                            average.normalizedSide = computeAverage(valuesHsh[average.clubId].normalizedSide);
                            average.normalizedSideTotal = computeAverage(valuesHsh[average.clubId].normalizedSideTotal);
                            average.normalizedApex = computeAverage(valuesHsh[average.clubId].normalizedApex);
                        }
                        averages.push(average);
                    }
                    //console.log('Averages: ', JSON.stringify(averages, null, 2));
                    
                    var incShots = sessionStats.shotCount > 0 ? -1: -sessionStats.shotCount
                    //console.log('Updating Stats: ', sessionStats.shotCount, record.lmSessionId, record.clubId);
                    await updateStatsSession(sessionStats, values, averages, incShots);//200ms success, 500ms failure
                    await pushSessionStatsMutation(sessionStats);
                } else {
                    console.log('Session Stats not yet created.  No operation for delete');
                }
                break;
            } catch (err) {//ConditionalCheckFailedException
                //console.log('===CONDITION FAILED=== Retries Left: ', tries);
                //console.log(err);
                if (--tries <= 0) throw err;
            }
        }
    }
}

exports.handler = async (event, context, callback) => {
    //eslint-disable-line
    console.log(JSON.stringify(event, null, 2));

    // New INSERT 
    const insertRecords = event.Records.filter(record => record.eventName == 'INSERT' );
    const unmarshalledInsRecords = insertRecords.map(record => 
        AWS.DynamoDB.Converter.unmarshall(record.dynamodb.NewImage)
    );

    // MODIFY
    const modRecords = event.Records.filter(record => record.eventName == 'MODIFY' );
    const unmarshalledModRecords = modRecords.map(record => 
        AWS.DynamoDB.Converter.unmarshall(record.dynamodb.NewImage)
    );

    // MODIFY New Image
    const unmarshalledModAddRecords = unmarshalledModRecords.filter(record => !record.hasOwnProperty('_deleted') || record._deleted == false );
    const modAddRecordIds = unmarshalledModAddRecords.map(record =>
        record.id
    );

    // MODIFY Old Image
    const unmarshalledModDelRecords = modRecords.map(record => 
        AWS.DynamoDB.Converter.unmarshall(record.dynamodb.OldImage)
    ).filter(record => modAddRecordIds.includes(record.id));

    // REMOVE 
    const remRecords = event.Records.filter(record => record.eventName == 'REMOVE' );
    const unmarshalledRemRecords = remRecords.map(record => 
        AWS.DynamoDB.Converter.unmarshall(record.dynamodb.OldImage)
    );
    
    // MODIFY New Deleted
    const unmarshalledDelRecords = unmarshalledModRecords.filter(record => record._deleted == true );

    console.log('DynamoDB Records Insert: ', unmarshalledInsRecords.length,
        'MODIFY: ', unmarshalledModRecords.length,
    //  'MODIFY add: ', unmarshalledModAddRecords.length,
    //  'MODIFY del: ', unmarshalledModDelRecords.length,
        'REMOVE: ', unmarshalledRemRecords.length,
        'Delete: ', unmarshalledDelRecords.length);

    try {
        await syncInsertRecords(unmarshalledInsRecords.concat(unmarshalledModAddRecords));
        await syncDeleteRecords(unmarshalledDelRecords.concat(unmarshalledModDelRecords).concat(unmarshalledRemRecords));
        console.log('===SUCCESS===');
    } catch (err) {
        console.log('===ERROR===');
        console.log(err);
        //callback(null, `Captured error ${JSON.stringify(err)}`);
        callback(err, `Captured error ${JSON.stringify(err)}`);
        return { error: err }
    }
    callback(null, 'Successfully processed DynamoDB record');
    return Promise.resolve('Successfully processed DynamoDB record');
};
