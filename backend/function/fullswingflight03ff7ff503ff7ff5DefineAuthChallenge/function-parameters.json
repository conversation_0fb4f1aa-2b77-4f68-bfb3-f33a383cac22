{"trigger": true, "modules": ["fs-define-challenge"], "parentResource": "fullswingflight03ff7ff503ff7ff5", "functionName": "fullswingflight03ff7ff503ff7ff5DefineAuthChallenge", "resourceName": "fullswingflight03ff7ff503ff7ff5DefineAuthChallenge", "parentStack": "auth", "triggerEnvs": "[]", "triggerDir": "/snapshot/amplify-cli/build/node_modules/@aws-amplify/amplify-category-auth/provider-utils/awscloudformation/triggers/DefineAuthChallenge", "triggerTemplate": "DefineAuthChallenge.json.ejs", "triggerEventPath": "DefineAuthChallenge.event.json", "roleName": "fullswingflight03ff7ff503ff7ff5DefineAuthChallenge", "skipEdit": true}