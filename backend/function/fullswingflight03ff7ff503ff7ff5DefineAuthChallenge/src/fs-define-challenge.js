/**
 * @type {import('@types/aws-lambda').DefineAuthChallengeTriggerHandler}
 */
exports.handler = async (event) => {
  console.log('DefineAuthChallenge event:', JSON.stringify(event, null, 2));

  if (event.request.session.length === 0) {
    // First request - present custom challenge for ID token
    event.response.issueTokens = false;
    event.response.failAuthentication = false;
    event.response.challengeName = 'CUSTOM_CHALLENGE';
  } else if (
    event.request.session.length === 1 &&
    event.request.session[0].challengeName === 'CUSTOM_CHALLENGE' &&
    event.request.session[0].challengeResult === true
  ) {
    // ID token verified successfully - issue tokens
    event.response.issueTokens = true;
    event.response.failAuthentication = false;
  } else {
    // Failed authentication
    event.response.issueTokens = false;
    event.response.failAuthentication = true;
  }

  return event;
};
