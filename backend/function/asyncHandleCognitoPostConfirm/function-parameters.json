{"permissions": {"api": {"fullswingflight": ["Mutation"]}, "storage": {"LmTeamPlayers:@model(appsync)": ["create", "read", "update"], "LmTeam:@model(appsync)": ["read", "update"], "LmTeamInviteCodes:@model(appsync)": ["read"], "LmCoachPlayers:@model(appsync)": ["create", "read", "update"], "LmUser:@model(appsync)": ["create", "read", "update"]}}, "lambdaLayers": [{"type": "ProjectLayer", "resourceName": "fullswingflightstatsShared", "version": "Always choose latest version", "isLatestVersionSelected": true, "env": "dev"}]}