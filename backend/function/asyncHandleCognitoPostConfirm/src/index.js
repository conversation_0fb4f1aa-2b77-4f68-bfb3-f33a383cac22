/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_ARN
	API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_NAME
	API_FULLSWINGFLIGHT_LMTEAMINVITECODESTABLE_ARN
	API_FULLSWINGFLIGHT_LMTEAMINVITECODESTABLE_NAME
	API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_ARN
	API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME
	API_FULLSWINGFLIGHT_LMTEAMTABLE_ARN
	API_FULLSWINGFLIGHT_LMTEAMTABLE_NAME
	API_FULLSWINGFLIGHT_LMUSERTABLE_ARN
	API_FULLSWINGFLIGHT_LMUSERTABLE_NAME
	ENV
	REGION
Amplify Params - DO NOT EDIT */
const AWS = require('aws-sdk');
const {success, error} = require('/opt/nodejs/response')
const docClient = new AWS.DynamoDB.DocumentClient();
const gql = require('graphql-tag');
const AppsyncClient = require('appsync-client').default;
const client = new AppsyncClient({
    // Required
    apiUrl: process.env.API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT,
    // Optional - these will default to process.env values (e.g. the IAM
    // role of the Lambda)
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    sessionToken: process.env.AWS_SESSION_TOKEN
});
const MAX_TEAM_PLAYERS = 30;
// TODO: GraphQL queries
const createLmUserQuery = gql`
    mutation createLmUser($input: CreateLmUserInput!) {
        createLmUser(input: $input) {
            id
            owner
        }}`
const createLmTeamPlayerQuery = gql`
    mutation createLmTeamPlayers($input: CreateLmTeamPlayersInput!) {
        createLmTeamPlayers(input: $input) {
            id
            owner
            readers
            type
            status
            email
            joined_at
            lmTeamId
            lmPlayerId
            lmPlayerName
            lineupOrderBatting
            lineupOrderPitching
            team {
                id
                owner
                name
                profileImage
                pitcherId
                createdAt
                updatedAt
                _version
                _deleted
                _lastChangedAt
                __typename
            }
            createdAt
            updatedAt
            _version
            _deleted
            _lastChangedAt
            __typename
        }}`
const createLmCoachPlayersQuery = gql`
    mutation createLmCoachPlayers($input: CreateLmCoachPlayersInput!) {
        createLmCoachPlayers(input: $input) {
            _deleted
            _lastChangedAt
            _version
            createdAt
            id
            lmCoachId
            lmPlayerEmail
            lmPlayerId
            lmPlayerName
            owner
            teamAssigned
            updatedAt
        }}`
const updateLmTeamPlayerQuery = gql`
    mutation updateLmTeamPlayers($input: UpdateLmTeamPlayersInput!) {
        updateLmTeamPlayers(input: $input) {
            id
            owner
            readers
            type
            status
            email
            joined_at
            lmTeamId
            lmPlayerId
            lmPlayerName
            lineupOrderBatting
            lineupOrderPitching
            team {
                id
                owner
                name
                profileImage
                pitcherId
                createdAt
                updatedAt
                _version
                _deleted
                _lastChangedAt
                __typename
            }
            createdAt
            updatedAt
            _version
            _deleted
            _lastChangedAt
            __typename
        }}`
const updateLmCoachPlayersQuery = gql`
    mutation updateLmCoachPlayers($input: UpdateLmCoachPlayersInput!) {
        updateLmCoachPlayers(input: $input) {
            id
            lmCoachId
            owner
            lmPlayerId
            lmPlayerName
            status
            lmPlayerEmail
            teamAssigned
            createdAt
            updatedAt
            _version
            _deleted
            _lastChangedAt
            __typename
        }}`;
const updateLmTeamQuery = gql`
    mutation updateLmTeam($input: UpdateLmTeamInput!) {
        updateLmTeam(input: $input) {
            _deleted
            _lastChangedAt
            _version
            createdAt
            id
            name
            owner
            pitcherId
            profileImage
            updatedAt
            readers
        }}`;
// TODO: syncCognitoUserFn
const syncCognitoUserFn = async (userId, email, fullName) => {
    try {
        console.log('Syncing user: ', userId, email, fullName);
        const isExistLmUser = await isExistLmUserFn(userId);
        if (isExistLmUser) {
            console.log('User already exist in LM, skip syncing');
            return success();
        }
        await syncLmUserFn(userId, email, fullName);
        console.log('Synced user: ', userId, email, fullName);
    } catch (e) {
        console.error('Sync user fail', e);
    }
}

const isExistLmUserFn = async (lmUserId) => {
    console.log('Getting user', lmUserId);
    const result = await docClient.get({
        TableName: process.env.API_FULLSWINGFLIGHT_LMUSERTABLE_NAME,
        Key: {
            id: lmUserId
        }
    }).promise();
    console.log('Got user', lmUserId);
    return !!(result && result.Item)
}

const syncLmUserFn = async (userId, email, fullName) => {
    console.log('Syncing user', userId, email, fullName, {
        input: {setupComplete: false, id: userId, fullName, email}
    });
    const result = await client.request({
        query: createLmUserQuery,
        variables: {
            input: {setupComplete: false, id: userId, fullName, email}
        }
    });
    if (!result || !result['createLmUser'] || !result['createLmUser']['id']) {
        console.error('Sync user fn fail', result);
    }
    const params = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMUSERTABLE_NAME,
        Key: {
            id: userId
        },
        UpdateExpression: "set #owner = :owner",
        ExpressionAttributeNames: {
            "#owner": "owner",
        },
        ExpressionAttributeValues: {
            ":owner": userId,
        },
        ReturnValues: "ALL_NEW",
    };
    await docClient.update(params).promise();
    console.log('Synced user', userId);
}


// TODO: addUserToTeamFn
const addUserToTeamFn = async (inviteCode, userName, email, fullName) => {
    try {
        console.log(`Adding user [${userName}] to team with invite code: `, inviteCode);
        const [result, listLmTeamPlayers, listLmCoachPlayers] = await Promise.all([
            getInviteInfoFn(inviteCode),
            listLmTeamPlayerByEmailFn(email),
            listLmCoachPlayerByEmailFn(email)
        ]);
        const updateLmTeamPlayers = (listLmTeamPlayers || []).filter(item => !item['lmPlayerId']);
        const updateLmCoachPlayers = (listLmCoachPlayers || []).filter(item => !item['lmPlayerId']);
        const addLmTeamPlayers = [];
        const addLmCoachPlayers = [];
        let lmCoachId;
        let lmTeamId;
        let inviteCodeItem;
        if (result && result['Item']) {
            inviteCodeItem = result['Item'];
            lmCoachId = inviteCodeItem['owner'];
            lmTeamId = inviteCodeItem['lmTeamId'];
            if (inviteCodeItem['type'] === 'AddPlayerInvitation' && inviteCodeItem['email'] !== email) {
            } else {
                const hasTeamPlayerEmail = updateLmTeamPlayers.some(item => item['lmTeamId'] === lmTeamId);
                if (!hasTeamPlayerEmail) {
                    addLmTeamPlayers.push({
                        owner: inviteCodeItem['owner'],
                        lmTeamId: lmTeamId,
                        lmPlayerId: userName,
                        status: 'Accepted'
                    });
                }
            }

            const hasCoachPlayerEmail = updateLmCoachPlayers.some(item => item['lmCoachId'] === inviteCodeItem['owner']);
            if (!hasCoachPlayerEmail) {
                addLmCoachPlayers.push({
                    owner: inviteCodeItem['owner'],
                    lmCoachId: inviteCodeItem['owner'],
                    lmPlayerId: userName,
                    teamAssigned: inviteCodeItem['lmTeamId'] ? 'Y' : 'N',
                    status: 'Accepted'
                })
            }
        }
        await Promise.all([
            updateLmTeamPlayersFn(updateLmTeamPlayers, userName, email, inviteCodeItem, fullName),
            updateLmCoachPlayersFn(updateLmCoachPlayers, userName, lmCoachId, lmTeamId, email, inviteCodeItem, fullName),
            addLmTeamPlayersFn(addLmTeamPlayers, fullName, email),
            addLmCoachPlayersFn(addLmCoachPlayers, fullName, email),
        ])
    } catch (e) {
        console.error(`Add user [${userName}] to team with invite code [${inviteCode}] has error`)
        console.error(e)
    }
}

const listLmTeamPlayerByEmailFn = async (email) => {
    console.log(`Get list of team player by email [${email}]`);
    const allItems = [];
    let ExclusiveStartKey = undefined;
    do {
        const params = {
            TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME,
            IndexName: "byEmail",
            KeyConditionExpression: "email = :email",
            ExpressionAttributeValues: {
                ":email": email,
            },
            ExclusiveStartKey,
        };
        const result = await docClient.query(params).promise();
        allItems.push(...result['Items']);
        ExclusiveStartKey = result['LastEvaluatedKey'];
    } while (ExclusiveStartKey);
    console.log(`Get list of team player by email [${email}] done`);
    return allItems;
}

const getInviteInfoFn = async (inviteCode) => {
    console.log(`Get invite info of code [${inviteCode || 'None'}]`);
    if (inviteCode && inviteCode !== "") {
        return await docClient.get({
            TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMINVITECODESTABLE_NAME,
            Key: {
                id: inviteCode
            }
        }).promise();
    }
    console.log(`Get invite info of code [${inviteCode || 'None'}] done`);
    return null;
}

const updateLmTeamPlayersFn = async (lmTeamPlayers, playerId, email, inviteCodeInfo, fullName) => {
    console.log(`Update list of team player [${playerId}]`, lmTeamPlayers.length);
    await Promise.all(lmTeamPlayers.map(async (item) => {
        console.log(`Updating team player`, item['email'], item['lmTeamId'])
        try {
            const updateInput = {
                id: item['id'],
                lmPlayerId: playerId,
                readers: [playerId],
                _version: item['_version'],
            };
            if (!item['lmPlayerName']) updateInput['lmPlayerName'] = fullName;
            if (inviteCodeInfo) {
                if (inviteCodeInfo['type'] === 'AddPlayerInvitation') {
                    if (email === inviteCodeInfo['email'] && inviteCodeInfo['lmTeamId'] === item['lmTeamId']) {
                        updateInput['status'] = 'Accepted';
                        updateInput['joined_at']= new Date().toISOString()
                    }
                } else if (inviteCodeInfo['lmTeamId'] === item['lmTeamId'])  updateInput['status'] = 'Accepted';
            }
            await updateReadersOfTeamFn(item['lmTeamId'], playerId);
            const result = await client.request({
                query: updateLmTeamPlayerQuery,
                variables: {
                    input: updateInput
                }
            });
            if (!result || !result['updateLmTeamPlayers'] || !result['updateLmTeamPlayers']['id']) {
                console.error('Update team player fn fail', result);
            }
        } catch (e) {
            console.error(`Update team player [${item['email']}_${item['lmTeamId']}] has error`, e)
        }
        console.log(`Updated team player`, item['email'], item['lmTeamId'])
    }))
    console.log(`Updated list of team player [${playerId}]`, lmTeamPlayers.length);
}

const updateLmCoachPlayersFn = async (lmCoachPlayers, playerId, lmCoachId, lmTeamId, email, inviteCodeInfo, fullName) => {
    console.log(`Update list of coach player [${playerId}]`, lmCoachPlayers.length);
    await Promise.all(lmCoachPlayers.map(async (item) => {
        console.log(`Updating coach player`, item['email'], item['lmCoachId']);
        const input = {
            id: item['id'],
            lmPlayerId: playerId,
            _version: item['_version'],
        };
        if (!item['lmPlayerName']) input['lmPlayerName'] = fullName;
        if (inviteCodeInfo) {
            if (inviteCodeInfo['type'] === 'AddPlayerInvitation') {
                if (email === inviteCodeInfo['email'] && inviteCodeInfo['owner'] === item['owner']) {
                    input['status'] = 'Accepted';
                }
            } else if (inviteCodeInfo['owner'] === item['owner'])  input['status'] = 'Accepted';
        }
        if (item['lmCoachId'] === lmCoachId && lmTeamId && item['teamAssigned'] === 'N') input['teamAssigned'] = 'Y';
        try {
            const result = await client.request({
                query: updateLmCoachPlayersQuery,
                variables: {
                    input
                }
            });
            if (!result || !result['updateLmCoachPlayers'] || !result['updateLmCoachPlayers']['id']) {
                console.error('Update coach player fn fail', result);
            } else {
                console.log(`Updated coach player`, item['email'], item['lmCoachId'])
            }
        } catch (e) {
            console.error(`Update coach player [${item['email']}_${item['lmCoachId']}] has error`, e)
        }
    }))
    console.log(`Updated list of coach player [${playerId}]`, lmCoachPlayers.length);
}

const countPlayersOfTeamFn = async (lmTeamId) => {
    console.log(`Count players of team [${lmTeamId}]`);
    let count = 0;
    let playerTypeOrder = 1;
    try {
        const params = {
            TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME,
            IndexName: "byTeam",
            KeyConditionExpression: "lmTeamId = :lmTeamId",
            ExpressionAttributeValues: {
                ":lmTeamId": lmTeamId,
            }
        };
        const result = await docClient.query(params).promise();
        if (result && result['Items'] && result['Items'].length > 0) {
            count = result['Items'].length;
            const battingPlayers = result['Items'].filter(item => item['type'] === 'Batting' || item['type'] === 'Both');
            const maxOrder = Math.max(0, ...battingPlayers.map(o => o.lineupOrderBatting));
            playerTypeOrder = maxOrder > battingPlayers.length ? maxOrder + 1 : battingPlayers.length + 1;
        }
        console.log(`Count players of team [${lmTeamId}] done`, count);
    } catch (e) {
        playerTypeOrder = 0;
        console.error(`Count players of team [${lmTeamId}] has error`, e)
    }
    return {count, playerTypeOrder};
}

const addLmTeamPlayersFn = async (inputs, fullName, email) => {
    if (!inputs || inputs.length === 0) return;
    const input = inputs[0];
    const lmTeamId = input['lmTeamId'];
    const lmPlayerId = input['lmPlayerId'];
    const owner = input['owner'];
    const status = input['status'];
    try {
        console.log(`Adding new team [${lmTeamId}] player [${lmPlayerId}]`);
        const {count, playerTypeOrder} = await countPlayersOfTeamFn(lmTeamId);
        if (count >= MAX_TEAM_PLAYERS) {
            console.log(`Team [${lmTeamId}] has reached max players [${MAX_TEAM_PLAYERS}]`);
            return;
        }
        await updateReadersOfTeamFn(lmTeamId, lmPlayerId);
        const result = await client.request({
            query: createLmTeamPlayerQuery,
            variables: {
                input: {
                    lmTeamId: lmTeamId,
                    lmPlayerId: lmPlayerId,
                    email,
                    owner,
                    readers: [lmPlayerId],
                    status,
                    joined_at: new Date().toISOString(),
                    type: 'Batting',
                    lineupOrderBatting: playerTypeOrder,
                    lineupOrderPitching: 0,
                    lmPlayerName: fullName || null
                }
            }
        });
        if (!result || !result['createLmTeamPlayers'] || !result['createLmTeamPlayers']['id']) {
            console.error(`Added new team fn fail`, result);
        } else console.log(`Added new team [${lmTeamId}] player [${lmPlayerId}]`);
    } catch (e) {
        console.log(`Add new team [${lmTeamId}] player [${lmPlayerId}] has error`, e);
    }
}

const updateReadersOfTeamFn = async (lmTeamId, reader) => {
    if (lmTeamId && reader) {
        try {
            console.log(`Updating readers of team [${lmTeamId}] add reader [${reader}]`);
            const result = await docClient.get({
                TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMTABLE_NAME,
                Key: {
                    id: lmTeamId
                }
            }).promise();
            if (!result || !result.Item) throw new Error(`Team [${lmTeamId}] not found`);
            const item = result.Item;
            const readers = item['readers'] || [];
            if (!readers.includes(reader)) readers.push(reader);
            const resultUpdate = await client.request({
                query: updateLmTeamQuery,
                variables: {
                    input: {
                        id: item['id'],
                        _version: item['_version'],
                        readers
                    }
                }
            });
            if (!resultUpdate || !resultUpdate['updateLmTeam'] || !resultUpdate['updateLmTeam']['id']) {
                console.error(`Fail updating readers of team [${lmTeamId}] add reader [${reader}]`, resultUpdate);
            } else {
                console.log(`Updated readers of team [${lmTeamId}] add reader [${reader}]`);
            }
        } catch (e) {
            console.error(`Error updating readers of team [${lmTeamId}] add reader [${reader}]`, e);
        }
    } else {
        console.log(`Skipping updating readers of team [${lmTeamId}] add reader [${reader}]`);
    }

}

const addLmCoachPlayersFn = async (inputs, fullName, email) => {
    if (!inputs || inputs.length === 0) return;
    const input = inputs[0];
    const lmCoachId = input['lmCoachId'];
    const lmPlayerId = input['lmPlayerId'];
    const teamAssigned = input['teamAssigned'];
    const owner = input['owner'];
    try {
        console.log(`Adding new coach [${lmCoachId}] player [${lmPlayerId}]`);
        const result = await client.request({
            query: createLmCoachPlayersQuery,
            variables: {
                input: {
                    lmCoachId,
                    lmPlayerId,
                    lmPlayerEmail: email,
                    owner,
                    teamAssigned,
                    lmPlayerName: fullName || null
                }
            }
        });
        if (!result || !result['createLmCoachPlayers'] || !result['createLmCoachPlayers']['id']) {
            console.error('Add new coach fn fail', result);
        } else console.log(`Added new coach [${lmCoachId}] player [${lmPlayerId}]`);
    } catch (e) {
        console.log(`Add new coach [${lmCoachId}] player [${lmPlayerId}] has error`, e);
    }
}

const listLmCoachPlayerByEmailFn = async (email) => {
    console.log(`Get list of coach player by email [${email}]`);
    const allItems = [];
    let ExclusiveStartKey = undefined;
    do {
        const params = {
            TableName: process.env.API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_NAME,
            IndexName: "byLmPlayerEmail",
            KeyConditionExpression: "lmPlayerEmail = :lmPlayerEmail",
            ExpressionAttributeValues: {
                ":lmPlayerEmail": email,
            },
            ExclusiveStartKey,
        };
        const result = await docClient.query(params).promise();
        allItems.push(...result['Items']);
        ExclusiveStartKey = result['LastEvaluatedKey'];
    } while (ExclusiveStartKey);
    console.log(`Get list of coach player by email [${email}] done`);
    return allItems;
}

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log("===event=== ", event);
    const userId = event['userId'];
    const email = event['email'];
    const fullName = event['fullName'];
    const inviteCode = event['inviteCode'];
    try {
        await Promise.all([
            addUserToTeamFn(inviteCode, userId, email, fullName),
            syncCognitoUserFn(userId, email, fullName),
        ])
        return success();
    } catch (e) {
        console.error(e)
        return error(e.name === "CUSTOM_ERROR" ? {errorType: e.code, message: e.message} : undefined);
    }
};
