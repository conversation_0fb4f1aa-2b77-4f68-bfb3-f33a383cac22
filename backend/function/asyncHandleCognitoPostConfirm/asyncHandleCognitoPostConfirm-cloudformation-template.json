{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"13.0.1\",\"stackType\":\"function-Lambda\",\"metadata\":{\"whyContinueWithGen1\":\"\"}}", "Parameters": {"CloudWatchRule": {"Type": "String", "Default": "NONE", "Description": " Schedule Expression"}, "deploymentBucketName": {"Type": "String"}, "env": {"Type": "String"}, "s3Key": {"Type": "String"}, "apifullswingflightGraphQLAPIIdOutput": {"Type": "String", "Default": "apifullswingflightGraphQLAPIIdOutput"}, "apifullswingflightGraphQLAPIEndpointOutput": {"Type": "String", "Default": "apifullswingflightGraphQLAPIEndpointOutput"}, "functionfullswingflightstatsSharedArn": {"Type": "String", "Default": "functionfullswingflightstatsSharedArn"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "asyncHandleCognitoPostConfirm", {"Fn::Join": ["", ["asyncHandleCognitoPostConfirm", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "REGION": {"Ref": "AWS::Region"}, "API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT": {"Ref": "apifullswingflightGraphQLAPIIdOutput"}, "API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT": {"Ref": "apifullswingflightGraphQLAPIEndpointOutput"}, "API_FULLSWINGFLIGHT_LMUSERTABLE_NAME": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmUserTable:Name"}}, "API_FULLSWINGFLIGHT_LMUSERTABLE_ARN": {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmUserTable:Name"}}]]}, "API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamPlayersTable:Name"}}, "API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_ARN": {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamPlayersTable:Name"}}]]}, "API_FULLSWINGFLIGHT_LMTEAMINVITECODESTABLE_NAME": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamInviteCodesTable:Name"}}, "API_FULLSWINGFLIGHT_LMTEAMINVITECODESTABLE_ARN": {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamInviteCodesTable:Name"}}]]}, "API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_NAME": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmCoachPlayersTable:Name"}}, "API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_ARN": {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmCoachPlayersTable:Name"}}]]}, "API_FULLSWINGFLIGHT_LMTEAMTABLE_NAME": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamTable:Name"}}, "API_FULLSWINGFLIGHT_LMTEAMTABLE_ARN": {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamTable:Name"}}]]}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs22.x", "MemorySize": 3008, "EphemeralStorage": {"Size": 1024}, "Layers": [{"Ref": "functionfullswingflightstatsSharedArn"}], "Timeout": 60}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "fullswingflightLambdaRole8b907a58", {"Fn::Join": ["", ["fullswingflightLambdaRole8b907a58", "-", {"Ref": "env"}]]}]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}]}}}, "AmplifyResourcesPolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "amplify-lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["appsync:GraphQL"], "Resource": [{"Fn::Join": ["", ["arn:aws:appsync:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":apis/", {"Ref": "apifullswingflightGraphQLAPIIdOutput"}, "/types/Mutation/*"]]}]}, {"Effect": "Allow", "Action": ["dynamodb:Put*", "dynamodb:Create*", "dynamodb:BatchWriteItem", "dynamodb:PartiQLInsert", "dynamodb:Get*", "dynamodb:BatchGetItem", "dynamodb:List*", "dynamodb:Describe*", "dynamodb:<PERSON><PERSON>", "dynamodb:Query", "dynamodb:PartiQLSelect", "dynamodb:Update*", "dynamodb:RestoreTable*", "dynamodb:PartiQLUpdate"], "Resource": [{"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamPlayersTable:Name"}}]]}, {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamPlayersTable:Name"}}, "/index/*"]]}]}, {"Effect": "Allow", "Action": ["dynamodb:Get*", "dynamodb:BatchGetItem", "dynamodb:List*", "dynamodb:Describe*", "dynamodb:<PERSON><PERSON>", "dynamodb:Query", "dynamodb:PartiQLSelect", "dynamodb:Update*", "dynamodb:RestoreTable*", "dynamodb:PartiQLUpdate"], "Resource": [{"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamTable:Name"}}]]}, {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamTable:Name"}}, "/index/*"]]}]}, {"Effect": "Allow", "Action": ["dynamodb:Get*", "dynamodb:BatchGetItem", "dynamodb:List*", "dynamodb:Describe*", "dynamodb:<PERSON><PERSON>", "dynamodb:Query", "dynamodb:PartiQLSelect"], "Resource": [{"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamInviteCodesTable:Name"}}]]}, {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamInviteCodesTable:Name"}}, "/index/*"]]}]}, {"Effect": "Allow", "Action": ["dynamodb:Put*", "dynamodb:Create*", "dynamodb:BatchWriteItem", "dynamodb:PartiQLInsert", "dynamodb:Get*", "dynamodb:BatchGetItem", "dynamodb:List*", "dynamodb:Describe*", "dynamodb:<PERSON><PERSON>", "dynamodb:Query", "dynamodb:PartiQLSelect", "dynamodb:Update*", "dynamodb:RestoreTable*", "dynamodb:PartiQLUpdate"], "Resource": [{"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmCoachPlayersTable:Name"}}]]}, {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmCoachPlayersTable:Name"}}, "/index/*"]]}]}, {"Effect": "Allow", "Action": ["dynamodb:Put*", "dynamodb:Create*", "dynamodb:BatchWriteItem", "dynamodb:PartiQLInsert", "dynamodb:Get*", "dynamodb:BatchGetItem", "dynamodb:List*", "dynamodb:Describe*", "dynamodb:<PERSON><PERSON>", "dynamodb:Query", "dynamodb:PartiQLSelect", "dynamodb:Update*", "dynamodb:RestoreTable*", "dynamodb:PartiQLUpdate"], "Resource": [{"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmUserTable:Name"}}]]}, {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmUserTable:Name"}}, "/index/*"]]}]}]}}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}}}