/* Amplify Params - DO NOT EDIT
	ENV
	REGION
	AUTH_FULLSWING_PORTAL_URL
	BRANCH_IO_API_HOST
	INVITATION_DEEP_LINK_PATH
Amplify Params - DO NOT EDIT */
const AWS = require('aws-sdk');
const axios = require("axios");
const {success, error} = require('/opt/nodejs/response');
const {returnErrorFn} = require('/opt/nodejs/util');

const ERROR = {
    INVITE_CODE_INVALID: {
        errorType: "errors.invite.code_invalid",
        message: "Invite code invalid."
    },
    MISSING_SSM_KEY: {
        errorType: "errors.common.missing_ssm_key",
        message: "SSM key is required."
    },
    BRANCH_IO_ERROR: {
        errorType: "errors.invite.branch_io_error",
        message: "BranchIO error. Please try again later."
    },
};
const AUTH_FULLSWING_PORTAL_URL = process.env.AUTH_FULLSWING_PORTAL_URL;
/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log('===env===', event);
    try {
        const inviteCode = event['inviteCode'];
        const teamName = event['teamName'];
        const teamImage = event['teamImage'];
        console.log('Creating BranchIO link for invite code: ', inviteCode);
        if (!inviteCode || inviteCode === "") return returnErrorFn(ERROR.INVITE_CODE_INVALID);
        const input = {
            "branch_key": process.env.BRANCH_IO_KEY,
            "feature": "Invitation",
            "tags": ["baseball", "invitation", "team"],
            "data": {
                "$marketing_title": "Baseball Invitation",
                "$deeplink_path": `${process.env.INVITATION_DEEP_LINK_PATH}/${inviteCode}`,
                "$desktop_url": `${AUTH_FULLSWING_PORTAL_URL}/create-account?invite_code=${inviteCode}&device_type=desktop`,
                "$ios_url": `${AUTH_FULLSWING_PORTAL_URL}/create-account?invite_code=${inviteCode}&device_type=ios`,
                "$android_url": `${AUTH_FULLSWING_PORTAL_URL}/create-account?invite_code=${inviteCode}&device_type=android`,
                "$ipad_url": `${AUTH_FULLSWING_PORTAL_URL}/create-account?invite_code=${inviteCode}&device_type=ios`,
                "$samsung_url": `${AUTH_FULLSWING_PORTAL_URL}/create-account?invite_code=${inviteCode}&device_type=android`,
                "$huawei_url": `${AUTH_FULLSWING_PORTAL_URL}/create-account?invite_code=${inviteCode}&device_type=android`,
                "$windows_phone_url": `${AUTH_FULLSWING_PORTAL_URL}/create-account?invite_code=${inviteCode}&device_type=win_phone`,
                "$blackberry_url": `${AUTH_FULLSWING_PORTAL_URL}/create-account?invite_code=${inviteCode}&device_type=android`,
                "$fire_url": `${AUTH_FULLSWING_PORTAL_URL}/create-account?invite_code=${inviteCode}&device_type=fire`,
                "$og_title": `Become a Player on Team Baseball ${teamName}!`,
                "$og_description": `Baseball ${teamName} is calling on passionate players to join our team! Whether you’re a seasoned athlete or just love the game, we welcome you to grow, train, and win with us. Let’s hit it out of the park — together!`,
                "invite_code": inviteCode
            }
        }
        if (teamImage) input.data['$og_image_url'] = teamImage;
        const result = await axios.post(process.env.BRANCH_IO_API_HOST + '/url', input);
        if (result.status !== 200) {
            console.error(`Create BranchIO link for invite code [${inviteCode}] has error`, result.data);
            return returnErrorFn(ERROR.BRANCH_IO_ERROR);
        }
        const link = result.data['url'];
        console.log('Created BranchIO link for invite code: ', inviteCode, link);
        return success({link});
    } catch (e) {
        console.error(e)
        return error(e.name === "CUSTOM_ERROR" ? {errorType: e.code, message: e.message} : undefined);
    }
};
