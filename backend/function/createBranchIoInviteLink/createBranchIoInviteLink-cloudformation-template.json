{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"13.0.1\",\"stackType\":\"function-Lambda\",\"metadata\":{\"whyContinueWithGen1\":\"\"}}", "Parameters": {"CloudWatchRule": {"Type": "String", "Default": "NONE", "Description": " Schedule Expression"}, "deploymentBucketName": {"Type": "String"}, "env": {"Type": "String"}, "s3Key": {"Type": "String"}, "functionfullswingflightstatsSharedArn": {"Type": "String", "Default": "functionfullswingflightstatsSharedArn"}, "authFullswingPortalUrl": {"Type": "String"}, "branchIoApiHost": {"Type": "String"}, "invitationDeepLinkPath": {"Type": "String"}, "branchIoKey": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "createBranchIoInviteLink", {"Fn::Join": ["", ["createBranchIoInviteLink", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "REGION": {"Ref": "AWS::Region"}, "AUTH_FULLSWING_PORTAL_URL": {"Ref": "authFullswingPortalUrl"}, "BRANCH_IO_API_HOST": {"Ref": "branchIoApiHost"}, "INVITATION_DEEP_LINK_PATH": {"Ref": "invitationDeepLinkPath"}, "BRANCH_IO_KEY": {"Ref": "branchIoKey"}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs22.x", "Layers": [{"Ref": "functionfullswingflightstatsSharedArn"}], "Timeout": 25}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "fullswingflightLambdaRole9b012162", {"Fn::Join": ["", ["fullswingflightLambdaRole9b012162", "-", {"Ref": "env"}]]}]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}]}}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}}}