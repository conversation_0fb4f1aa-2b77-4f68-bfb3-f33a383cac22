{"lambdaLayers": [{"type": "ProjectLayer", "resourceName": "fullswingflightstatsShared", "version": "Always choose latest version", "isLatestVersionSelected": true, "env": "dev"}], "environmentVariableList": [{"cloudFormationParameterName": "authFullswingPortalUrl", "environmentVariableName": "AUTH_FULLSWING_PORTAL_URL"}, {"cloudFormationParameterName": "branchIoApiHost", "environmentVariableName": "BRANCH_IO_API_HOST"}, {"cloudFormationParameterName": "invitationDeepLinkPath", "environmentVariableName": "INVITATION_DEEP_LINK_PATH"}, {"cloudFormationParameterName": "branchIoKey", "environmentVariableName": "BRANCH_IO_KEY"}], "secretNames": []}