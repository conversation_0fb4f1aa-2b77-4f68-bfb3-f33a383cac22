/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	API_FULLSWINGFLIGHT_LMSESSIONTABLE_ARN
	API_FULLSWINGFLIGHT_LMSESSIONTABLE_NAME
	ENV
	REGION
Amplify Params - DO NOT EDIT */

const AWS = require('aws-sdk');

/*
* DynamoDB
*/
const docClient = new AWS.DynamoDB.DocumentClient();

exports.handler = async (event) => {
    //console.log('===env===');
    //console.log(process.env);
    //console.log('===event===');
    //console.log(event);

    var resolution = event.arguments.resolution;

    var sessionparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMSESSIONTABLE_NAME,
        Limit: 50000,
        IndexName: 'byUserInRange',
        KeyConditionExpression: '#owner = :owner AND #timestamp BETWEEN :start AND :end',
        ProjectionExpression: 'startTimestamp',
        ExpressionAttributeValues: { 
            ':owner': event.arguments.owner,
            ':start': event.arguments.start,
            ':end': event.arguments.end
        },
        ExpressionAttributeNames: { 
            '#owner': 'owner',
            '#timestamp': 'startTimestamp'
        },
    }

    try {
        // Prepare results
        var valHsh = {};
        var returnVal = [];

        // Fetch Sessions
        const sdata = await docClient.query(sessionparams).promise();
        // console.log('===sessions===');
        // console.log(JSON.stringify(sdata));
        for (const session of sdata['Items']) {
            var timestamp = session['startTimestamp'];
            // Create a new JavaScript Date object based on the timestamp
            // multiplied by 1000 so that the argument is in milliseconds, not seconds.
            var date = new Date(timestamp * 1000);
            var dateBin = date;

            //console.log('Session with timestamp: ', timestamp);
            switch (resolution) {
            case 'Day':
                dateBin = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                break;
            case 'Month':
                dateBin = new Date(date.getFullYear(), date.getMonth(), 1);
                break;
            case 'Year':
                dateBin = new Date(date.getFullYear(), 1, 1);
                break;
            }

            valHsh[dateBin] = valHsh[dateBin] || 0;
            valHsh[dateBin] += 1;
        }
        for (const bin in valHsh) {
            var startd = new Date(bin);
            var endd = startd;
            switch (resolution) {
            case 'Day':
                endd = new Date(startd.getFullYear(), startd.getMonth(), startd.getDate() + 1);
                break;
            case 'Month':
                endd = new Date(startd.getFullYear(), startd.getMonth() + 1, 1);
                break;
            case 'Year':
                endd = new Date(startd.getFullYear() + 1, 1, 1);
                break;
            }
            let startTS = Math.floor(startd.getTime() / 1000);
            let endTS = Math.floor(endd.getTime() / 1000);
            returnVal.push({start: startTS, end: endTS, count: valHsh[startd]});
        }
        // console.log('===return===');
        // console.log(JSON.stringify(returnVal));

        return returnVal;
    } catch (err) {
        console.log('===ERROR===');
        console.log(err);
        return { error: err }
    }
}