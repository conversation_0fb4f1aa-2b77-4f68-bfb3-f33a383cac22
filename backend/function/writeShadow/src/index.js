const AWS = require('aws-sdk');
const IOT = new AWS.IotData({endpoint: 'a279eql4kdsugs-ats.iot.us-east-1.amazonaws.com'});
/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log('===env===');
    //console.log(process.env);
    console.log('===event===');
    console.log(event);
    var registration = null;
    //FIXME: This ignores when the device was first found, right?
    // Should query the database and fetch LmDevice createdAt, failing over to current date.
    var date = new Date();
    var listparams = {
        thingName: event.arguments.deviceId, /* required */
        pageSize: '10'
    };
    var fetchparams = {
        thingName: event.arguments.deviceId, /* required */
        shadowName: 'registration'
    };
    try {
        // Fetch Shadow list
        const slistdata = await IOT.listNamedShadowsForThing(listparams).promise();
        console.log(slistdata);
        if (!slistdata.results.includes('registration')) {
            console.log('===creating registration shadow===');
            var shadowPayload = {
                state: {
                    desired: {
                        registration: {
                            regUser: event.arguments.regUser,
                            prodRegDate: date.toISOString()
                        }
                    }
                }
            };
            var writeparams = {
                payload: JSON.stringify(shadowPayload),
                thingName: event.arguments.deviceId, /* required */
                shadowName: 'registration'
            };
            // Write Shadow
            const sdata = await IOT.updateThingShadow(writeparams).promise();
            console.log(sdata);
            var payload = JSON.parse(sdata.payload);
            registration = payload.state.desired.registration;
            return registration;
        }
        console.log('===updating registration shadow===');
        // Fetch Shadow
        const fdata = await IOT.getThingShadow(fetchparams).promise();
        console.log(fdata);
        var payload = JSON.parse(fdata.payload);
        registration = payload.state.desired.registration;
        var shadowPayload = {
            state: {
                desired: {
                    registration: {
                        regUser: event.arguments.regUser,
                        prodRegDate: registration.prodRegDate
                    }
                }
            }
        };
        var writeparams = {
            payload: JSON.stringify(shadowPayload),
            thingName: event.arguments.deviceId, /* required */
            shadowName: 'registration'
        };
        // Update Shadow
        const sdata = await IOT.updateThingShadow(writeparams).promise();
        console.log('===updateThingShadow===');
        console.log(sdata);
        var payload = JSON.parse(sdata.payload);
        registration = payload.state.desired.registration;
        // console.log('===shadow===');
        // console.log(JSON.stringify(sdata));
        return registration;
    } catch (err) {
        console.log('===ERROR===');
        console.log(err);
        return { error: err }
    }
};