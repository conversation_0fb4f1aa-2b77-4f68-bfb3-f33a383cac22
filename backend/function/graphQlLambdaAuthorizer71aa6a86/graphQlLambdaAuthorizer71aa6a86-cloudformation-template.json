{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"Linux\",\"createdBy\":\"Amplify\",\"createdWith\":\"13.0.0\",\"stackType\":\"function-Lambda\",\"metadata\":{\"whyContinueWithGen1\":\"\"}}", "Parameters": {"CloudWatchRule": {"Type": "String", "Default": "NONE", "Description": " Schedule Expression"}, "deploymentBucketName": {"Type": "String"}, "env": {"Type": "String"}, "s3Key": {"Type": "String"}, "authClientId": {"Type": "String"}, "authWebClientId": {"Type": "String"}, "functionfullswingflightstatsSharedArn": {"Type": "String", "Default": "functionfullswingflightstatsSharedArn"}, "apifullswingflightGraphQLAPIIdOutput": {"Type": "String", "Default": "apifullswingflightGraphQLAPIIdOutput"}, "apifullswingflightGraphQLAPIEndpointOutput": {"Type": "String", "Default": "apifullswingflightGraphQLAPIEndpointOutput"}, "authfullswingflight03ff7ff503ff7ff5UserPoolId": {"Type": "String", "Default": "authfullswingflight03ff7ff503ff7ff5UserPoolId"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}, "Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "graphQlLambdaAuthorizer71aa6a86", {"Fn::Join": ["", ["graphQlLambdaAuthorizer71aa6a86", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "REGION": {"Ref": "AWS::Region"}, "AUTH_CLIENT_ID": {"Ref": "authClientId"}, "AUTH_WEB_CLIENT_ID": {"Ref": "authWebClientId"}, "API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT": {"Ref": "apifullswingflightGraphQLAPIIdOutput"}, "API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT": {"Ref": "apifullswingflightGraphQLAPIEndpointOutput"}, "AUTH_FULLSWINGFLIGHT03FF7FF503FF7FF5_USERPOOLID": {"Ref": "authfullswingflight03ff7ff503ff7ff5UserPoolId"}, "API_FULLSWINGFLIGHT_LMTEAMCOACHESTABLE_NAME": {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamCoachesTable:Name"}}, "API_FULLSWINGFLIGHT_LMTEAMCOACHESTABLE_ARN": {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamCoachesTable:Name"}}]]}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs18.x", "Layers": [{"Ref": "functionfullswingflightstatsSharedArn"}], "Timeout": 25}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "fullswingflightLambdaRole03bafb71", {"Fn::Join": ["", ["fullswingflightLambdaRole03bafb71", "-", {"Ref": "env"}]]}]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}]}}}, "PermissionForAppSyncToInvokeLambda": {"Type": "AWS::Lambda::Permission", "Properties": {"FunctionName": {"Ref": "LambdaFunction"}, "Action": "lambda:InvokeFunction", "Principal": "appsync.amazonaws.com"}}, "AmplifyResourcesPolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "amplify-lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["appsync:GraphQL"], "Resource": [{"Fn::Join": ["", ["arn:aws:appsync:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":apis/", {"Ref": "apifullswingflightGraphQLAPIIdOutput"}, "/types/Query/*"]]}]}, {"Effect": "Allow", "Action": ["cognito-identity:Describe*", "cognito-identity:Get*", "cognito-identity:List*", "cognito-idp:Describe*", "cognito-idp:AdminGetDevice", "cognito-idp:AdminGetUser", "cognito-idp:AdminList*", "cognito-idp:List*", "cognito-sync:Describe*", "cognito-sync:Get*", "cognito-sync:List*", "iam:ListOpenIdConnectProviders", "iam:ListRoles", "sns:ListPlatformApplications"], "Resource": [{"Fn::Join": ["", ["arn:aws:cognito-idp:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":userpool/", {"Ref": "authfullswingflight03ff7ff503ff7ff5UserPoolId"}]]}]}, {"Effect": "Allow", "Action": ["dynamodb:Get*", "dynamodb:BatchGetItem", "dynamodb:List*", "dynamodb:Describe*", "dynamodb:<PERSON><PERSON>", "dynamodb:Query"], "Resource": [{"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamCoachesTable:Name"}}]]}, {"Fn::Join": ["", ["arn:aws:dynamodb:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":table/", {"Fn::ImportValue": {"Fn::Sub": "${apifullswingflightGraphQLAPIIdOutput}:GetAtt:LmTeamCoachesTable:Name"}}, "/index/*"]]}]}]}}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}}}