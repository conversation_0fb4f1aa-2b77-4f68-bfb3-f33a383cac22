{"lambdaLayers": [{"type": "ProjectLayer", "resourceName": "fullswingflightstatsShared", "version": "Always choose latest version", "isLatestVersionSelected": true, "env": "dev"}], "permissions": {"api": {"fullswingflight": ["Query"]}, "auth": {"fullswingflight03ff7ff503ff7ff5": ["read"]}, "storage": {"LmTeamCoaches:@model(appsync)": ["read"]}}, "environmentVariableList": [{"cloudFormationParameterName": "authClientId", "environmentVariableName": "AUTH_CLIENT_ID"}, {"cloudFormationParameterName": "authWebClientId", "environmentVariableName": "AUTH_WEB_CLIENT_ID"}]}