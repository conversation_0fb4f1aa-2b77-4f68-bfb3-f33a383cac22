/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAP<PERSON>NDPOINTOUTPUT
	API_FULLSWINGFLIGHT_GRAPH<PERSON>APIIDOUTPUT
	AUTH_FULLSWINGFLIGHT03FF7FF503FF7FF5_USERPOOLID
	ENV
	REGION
Amplify Params - DO NOT EDIT */// This is sample code. Please update this to suite your schema

/**
 * Event
 {
    "authorizationToken": "ExampleAuthToken123123123", # Authorization token specified by client
    "requestContext": {
        "apiId": "nacwtkdzd5cphifqs7egbm23h4", # AppSync API ID
        "accountId": "************", # AWS Account ID
        "requestId": "e92d414a-aba5-4f27-8804-453b2773ef3e",
        "queryString": "query MyQuery {\n  dataSessionsByTeam(lmTeamId: \"700113B7-08CC-4586-9AE7-2211B921153F\") {\n    items {\n      address\n      deviceID\n      id\n    }\n  }\n}",
        "operationName": "MyQuery", # GraphQL operation name
        "variables": {} # any additional variables supplied to the operation
    }
}
 * Response
{
  // required
  "isAuthorized": true, // if "false" then an UnauthorizedException is raised, access is denied
  "resolverContext": { "banana": "very yellow" }, // JSON object visible as $ctx.identity.resolverContext in VTL resolver templates

  // optional
  "deniedFields": ["TypeName.FieldName"], // Forces the fields to "null" when returned to the client
  "ttlOverride": 10 // The number of seconds that the response should be cached for. Overrides default specified in "amplify update api"
}
 */
const AWS = require('aws-sdk');
const { CognitoJwtVerifier } = require("aws-jwt-verify");

/* MARK: Global Objects */

// Create the verifier outside the Lambda handler (= during cold start),
// so the cache can be reused for subsequent invocations. Then, only during the
// first invocation, will the verifier actually need to fetch the JWKS.
const jwtVerifier = CognitoJwtVerifier.create({
    userPoolId: process.env.AUTH_FULLSWINGFLIGHT03FF7FF503FF7FF5_USERPOOLID,
    tokenUse: "access", // token type
    clientId: [
        process.env.AUTH_CLIENT_ID, // Mobile App Client
        process.env.AUTH_WEB_CLIENT_ID] // Web App Client
});
const docClient = new AWS.DynamoDB.DocumentClient();

/* MARK: AWS DynamoDB Get Functions */

async function getLmTeamsByUser(userId) {
    var qparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMCOACHESTABLE_NAME,
        IndexName: 'byLmUser',
        KeyConditionExpression: '#lmUserId = :lmUserId',
        ProjectionExpression: 'id',
        ExpressionAttributeValues: { 
            ':lmUserId': userId
        },
        ExpressionAttributeNames: { 
            '#lmUserId': 'lmUserId'
        },
    };
    const response = await docClient.query(qparams).promise();
    if (response.Items.length > 0) 
        return response.Items;

    console.log('LmTeam Not Found for owner: ', userId);
    return [];
}

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log(`EVENT: ${JSON.stringify(event)}`);
    const {
        authorizationToken,
        requestContext: { apiId, accountId, queryString },
    } = event;
    // Extract TeamID from query string
    const match = queryString.match(/lmTeamId: "([\d\w\-]+)"/);
    const teamId = match[1];
    console.log(`TEAM: ${teamId}`);

    // Use JWT to decode token
    // verify kid in header
    // extract scope, exp and username
    let isAuthorized = false;
    let accessToken = authorizationToken.replace("token=", "");
    try {
        const payload = await jwtVerifier.verify(accessToken);
        console.log("Token is valid, payload: ", payload);

        // Extract user ID from JWT token.
        const { username } = payload;
        // Query that user has coach access to read team data
        const teams = await getLmTeamsByUser(username);
        console.log(`TEAMS: ${teams}`);
        if (teams.find(e => e.id === teamId)) {
            console.log("User has coach access to team.");
            isAuthorized = true;
        } else {
            console.log("User does not have coach access to team.");
        }
    } catch {
        console.log("Token is invalid");
    }

  /* JWT Header
  {
  "kid": "AzzCL+7eW0L8/X5WQxU/7UNRb1Ua8kqixx5HiTLgO6k=",
  "alg": "RS256"
  }
  */
  /* JWT Payload
  {
    "sub": "26c151fe-54e4-49bd-a0d1-22aa13b3587b",
    "cognito:groups": [
      "Users"
    ],
    "iss": "https://cognito-idp.us-east-1.amazonaws.com/us-east-1_QXkqgwCpu",
    "client_id": "2qsljpeaguf0b4gun2hi3bassf",
    "event_id": "db28ab62-74d7-4e8c-9dba-cdb8131ed021",
    "token_use": "access",
    "scope": "aws.cognito.signin.user.admin",
    "auth_time": 1734541807,
    "exp": **********,
    "iat": **********,
    "jti": "63f5975c-8f86-40de-9035-8cac59f234ea",
    "username": "26c151fe-54e4-49bd-a0d1-22aa13b3587b"
  }
  */

    const response = {
        isAuthorized: isAuthorized,
        resolverContext: {
        // eslint-disable-next-line spellcheck/spell-checker
        userid: 'user-id',
        info: 'contextual information A',
        more_info: 'contextual information B',
        },
        deniedFields: [
        `arn:aws:appsync:${process.env.AWS_REGION}:${accountId}:apis/${apiId}/types/Event/fields/comments`,
        `Mutation.createEvent`,
        `lmProfileId`
        ],
        ttlOverride: 300,
    };
    console.log(`response >`, JSON.stringify(response, null, 2));
    return response;
};
