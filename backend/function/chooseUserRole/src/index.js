/* Amplify Params - DO NOT EDIT
	AUTH_FULLSWINGFLIGHT03FF7FF503FF7FF5_USERPOOLID
	ENV
	REGION
Amplify Params - DO NOT EDIT */

const {
    CognitoIdentityProviderClient,
    AdminUpdateUserAttributesCommand,
    AdminAddUserToGroupCommand,
    AdminListGroupsForUserCommand,
    AdminRemoveUserFromGroupCommand,
    AdminGetUserCommand
} = require('@aws-sdk/client-cognito-identity-provider');
const cognitoIdentityServiceProvider = new CognitoIdentityProviderClient({});

const USERS_ROLE = "Users";
const COACHES_ROLE = "Coaches";
const ERROR = {
    ROLE_ALREADY_ASSIGNED: {
        errorType: "errors.auth.role_already_assigned",
        message: "User already has a role assigned. Role can only be set once."
    },
    INVALID_ROLE_SELECTION: {
        errorType: "errors.auth.invalid_role_selection",
        message: "The selected role is invalid. Please choose a valid role."
    },
    INTERNAL_SERVER_ERROR: {
        errorType: "errors.common.internal_server_error",
        message: "Internal Server Error"
    }
}
const USER_POOL_ID = process.env.AUTH_FULLSWINGFLIGHT03FF7FF503FF7FF5_USERPOOLID

const returnErrorFn = (error, params) => {
    const err = new Error(error.message + (params ? ` ${params}` : ''));
    err.name = "CUSTOM_ERROR";
    err.code = error.errorType
    throw err;
}

const getUserInfoFn = async (userName) => {
    console.log(`Getting user info of user [${userName}]`)
    const command = new AdminGetUserCommand({
        UserPoolId: USER_POOL_ID,
        Username: userName,
    });
    const response = await cognitoIdentityServiceProvider.send(command);
    const userInfo = {};
    (response?.UserAttributes || []).forEach(attr => {
        userInfo[attr.Name] = attr.Value;
    });
    console.log(`Got user info of user [${userName}]`)
    return userInfo;
}

const updateCustomRoleFn = async (role, userName) => {
    console.log(`Updating user custom role [${role}] of user [${userName}]`)
    const updateUserAttributesParams = {
        UserPoolId: USER_POOL_ID,
        Username: userName,
        UserAttributes: [
            {
                Name: "custom:role",
                Value: role
            }
        ]
    };
    await cognitoIdentityServiceProvider.send(
        new AdminUpdateUserAttributesCommand(updateUserAttributesParams)
    );
    console.log(`Updated user custom role [${role}] of user [${userName}]`)
}
const addUserToGroupFn = async (group, userName) => {
    console.log(`Adding user [${userName}] to group [${group}]`)
    const result = await cognitoIdentityServiceProvider.send(
        new AdminListGroupsForUserCommand({
            UserPoolId: USER_POOL_ID,
            Username: userName
        })
    );
    const addUserParams = {
        UserPoolId: USER_POOL_ID,
        Username: userName,
    };
    const removeUserParams = {
        UserPoolId: USER_POOL_ID,
        Username: userName,
    }
    const existGroups = result.Groups?.map(group => group.GroupName) || [];
    if (existGroups.includes(group)) {
        console.log(`User [${userName}] already in group [${USERS_ROLE}]`)
        return;
    } else {
        addUserParams.GroupName = group;
        if (group === COACHES_ROLE && existGroups.includes(USERS_ROLE)) {
            removeUserParams.GroupName = USERS_ROLE;
        } else if (group === USERS_ROLE && existGroups.includes(COACHES_ROLE)) {
            removeUserParams.GroupName = COACHES_ROLE;
        }
    }
    await cognitoIdentityServiceProvider.send(new AdminAddUserToGroupCommand(addUserParams));
    if (removeUserParams.GroupName) {
        console.log(`Removing user [${userName}] from group [${removeUserParams.GroupName}]`)
        await cognitoIdentityServiceProvider.send(new AdminRemoveUserFromGroupCommand(removeUserParams));
    }
    console.log(`Added user [${userName}] to group [${group}]`)
}
/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log('===env===', event);
    let role;
    let userInfo;
    let userName;
    try {
        role = event.arguments.role;
        userName = event.identity.username;
        userInfo = await getUserInfoFn(userName);
        console.log(`Assigning user [${userName}] role [${role}]`)
        const customRole = userInfo['custom:role'];
        // if (customRole) return returnErrorFn(ERROR.ROLE_ALREADY_ASSIGNED)
        if (![USERS_ROLE, COACHES_ROLE].includes(role)) return returnErrorFn(ERROR.INVALID_ROLE_SELECTION)
        if (!customRole) await updateCustomRoleFn(role, userName);
        await addUserToGroupFn(role, userName);
        console.log(`Assigned user [${userName}] role [${role}]`)
        return {
            status: "200",
        };
    } catch (e) {
        if (userName && role) console.error(`Assign user [${userName}] role [${role}] has error`)
        console.error(e)
        return {
            status: "500",
            errors: [e.name === "CUSTOM_ERROR" ? {errorType: e.code, message: e.message} : ERROR.INTERNAL_SERVER_ERROR]
        }
    }
};
