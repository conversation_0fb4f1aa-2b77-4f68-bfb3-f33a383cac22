{"permissions": {"api": {"fullswingflight": ["Mutation"]}, "storage": {"LmSession:@model(appsync)": ["read"], "LmShot:@model(appsync)": ["read"], "LmClub:@model(appsync)": ["read"], "LmShareSession:@model(appsync)": ["create", "read", "update"]}}, "lambdaLayers": [{"type": "ProjectLayer", "resourceName": "fullswingflightstatsShared", "version": "Always choose latest version", "isLatestVersionSelected": true, "env": "dev"}]}