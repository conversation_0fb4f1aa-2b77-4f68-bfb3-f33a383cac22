const AWS = require("aws-sdk");
const IOT = new AWS.IotData({
  endpoint: "a279eql4kdsugs-ats.iot.us-east-1.amazonaws.com",
});
/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
  console.log("===env===");
  //console.log(process.env);
  console.log("===event===");
  console.log(event);
  var registration = null;

  try {
    // Admin only request to reset current registration date
    if (!event.identity.claims["cognito:groups"].includes("Admin")) {
      console.log("===Admin Required===");
      return { error: "Admin access required" };
    }

    // Fetch Shadow list
    console.log("===fetching shadow list===");
    var listparams = {
      thingName: event.arguments.deviceId /* required */,
      pageSize: "10",
    };
    const slistdata = await IOT.listNamedShadowsForThing(listparams).promise();
    console.log(slistdata);
    if (slistdata.results.includes("registration")) {
      console.log("===deleting registration shadow===");
    } else {
      console.log("===registration shadow not found===");
      return { regUser: event.arguments.regUser, prodRegDate: "" };
    }
    // Always overwrite Shadow list
    console.log("===clearing registration shadow===");
    var deleteparams = {
      thingName: event.arguments.deviceId /* required */,
      shadowName: "registration",
    };
    // Delete Shadow
    const sdata = await IOT.deleteThingShadow(deleteparams).promise();
    console.log(sdata);
    console.log("===done===");
    return { regUser: event.arguments.regUser, prodRegDate: "" };;
  } catch (err) {
    console.log("===ERROR===");
    console.log(err);
    return { error: err };
  }
};
