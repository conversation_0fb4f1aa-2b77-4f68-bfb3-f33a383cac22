/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	API_FULLSWINGFLIGHT_LMUSERTABLE_ARN
	API_FULLSWINGFLIGHT_LMUSERTABLE_NAME
	ENV
	REGION
Amplify Params - DO NOT EDIT */
const AWS = require('aws-sdk');
const {success, error} = require('/opt/nodejs/response')
const docClient = new AWS.DynamoDB.DocumentClient();
const gql = require('graphql-tag');
const AppsyncClient = require('appsync-client').default;

const client = new AppsyncClient({
    // Required
    apiUrl: process.env.API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT,
    // Optional - these will default to process.env values (e.g. the IAM
    // role of the Lambda)
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    sessionToken: process.env.AWS_SESSION_TOKEN
});
const createLmUserQuery = gql`
    mutation createLmUser($input: CreateLmUserInput!) {
        createLmUser(input: $input) {
            id
            owner
        }}`
const isExistLmUserFn = async (lmUserId) => {
    console.log('Getting user', lmUserId);
    const result = await docClient.get({
        TableName: process.env.API_FULLSWINGFLIGHT_LMUSERTABLE_NAME,
        Key: {
            id: lmUserId
        }
    }).promise();
    console.log('Got user', lmUserId);
    return !!(result && result.Item)
}

const syncLmUserFn = async (userId, email, fullName) => {
    console.log('Syncing user', userId, email, fullName);
    const result = await client.request({
        query: createLmUserQuery,
        variables: {
            input: {setupComplete: false, id: userId, fullName, email}
        }
    })
    if (!result || !result['createLmUser'] || !result['createLmUser']['id']) throw new Error('Sync user fn fail');
    const params = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMUSERTABLE_NAME,
        Key: {
            id: userId
        },
        UpdateExpression: "set #owner = :owner",
        ExpressionAttributeNames: {
            "#owner": "owner",
        },
        ExpressionAttributeValues: {
            ":owner": userId,
        },
        ReturnValues: "ALL_NEW",
    };
    await docClient.update(params).promise();
    console.log('Synced user', userId);
}

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log("===event=== ", event);
    const userId = event['userId'];
    const email = event['email'];
    const fullName = event['fullName'];
    try {
        console.log('Syncing user: ', userId, email, fullName);
        const isExistLmUser = await isExistLmUserFn(userId);
        if (isExistLmUser) {
            console.log('User already exist in LM, skip syncing');
            return success();
        }
        await syncLmUserFn(userId, email, fullName);
        console.log('Synced user: ', userId, email, fullName);
        return success();
    } catch (e) {
        console.error(e)
        return error(e.name === "CUSTOM_ERROR" ? {errorType: e.code, message: e.message} : undefined);
    }
};
