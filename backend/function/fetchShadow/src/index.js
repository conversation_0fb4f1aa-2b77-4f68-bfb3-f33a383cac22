/* Amplify Params - DO NOT EDIT
	ENV
	REGION
Amplify Params - DO NOT EDIT */

const AWS = require('aws-sdk');
const IOT = new AWS.IotData({endpoint: 'a279eql4kdsugs-ats.iot.us-east-1.amazonaws.com'});

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log('===env===');
    //console.log(process.env);
    console.log('===event===');
    console.log(event);

    var listparams = {
        thingName: event.arguments.deviceId, /* required */
        pageSize: '10'
    };
    var params = {
        thingName: event.arguments.deviceId, /* required */
        shadowName: 'registration'
    };

    try {
        // Fetch Shadow list
        const slistdata = await IOT.listNamedShadowsForThing(listparams).promise();
        console.log(slistdata);

        if (!slistdata.results.includes('registration')) {
            console.log('===creating registration shadow===');

            var date = new Date();
            var shadowPayload = {
                state: {
                    desired: {
                        registration: {
                            regUser: '',
                            prodRegDate: date.toISOString()
                        }
                    }
                }
            };
            var writeparams = {
                payload: JSON.stringify(shadowPayload),
                thingName: event.arguments.deviceId, /* required */
                shadowName: 'registration'
            };

            // Write Shadow
            const sdata = await IOT.updateThingShadow(writeparams).promise();
            console.log(sdata);
            var payload = JSON.parse(sdata.payload);
            var registration = payload.state.desired.registration;

            return registration;
        }

        // Fetch Shadow
        const sdata = await IOT.getThingShadow(params).promise();
        console.log(sdata);
        var payload = JSON.parse(sdata.payload);
        var registration = payload.state.desired.registration;

        // console.log('===shadow===');
        // console.log(JSON.stringify(sdata));

        return registration;
    } catch (err) {
        console.log('===ERROR===');
        console.log(err);
        return { error: err }
    }
};
