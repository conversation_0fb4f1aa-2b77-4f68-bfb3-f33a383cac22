/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAP<PERSON>NDPOINTOUTPUT
	API_FULLSWINGFLIGHT_GRAPHQL<PERSON>IIDOUTPUT
	ENV
	REGION
Amplify Params - DO NOT EDIT */

const AWS = require('aws-sdk');
const gql = require('graphql-tag');
const graphql = require('graphql');
const AppsyncClient = require('appsync-client').default;

const client = new AppsyncClient({
    // Required
    apiUrl: process.env.API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT,
    // Optional - these will default to process.env values (e.g. the IAM
    // role of the Lambda)
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    sessionToken: process.env.AWS_SESSION_TOKEN
});

const getLmFeature = gql`
query GetLmFeature($id: ID!) {
    getLmFeature(id: $id) {
      id
      lmProfileId
      owner
      featureName
      enabled
      expiresAt
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`

const updateLmFeature = gql`
mutation UpdateLmFeature(
  $id: ID!
  $enabled: Boolean
  $version: Int
) {
  updateLmFeature(input: {id: $id, enabled: $enabled, _version: $version}) {
    id
    lmProfileId
    owner
    featureName
    enabled
    expiresAt
    createdAt
    updatedAt
    _version
    _deleted
    _lastChangedAt
    __typename
  }
}
`

/* Helper Functions */

/* AWS GraphQL Functions */

async function queryFeature(featureId) {
    const result = await client.request({
        query: getLmFeature,
        variables: {
            id: featureId
        }
    });
    console.log(JSON.stringify(result));
    return result.getLmFeature;
}

async function pushFeatureMutation(feature) {
    const sresult = await client.request({
        query: updateLmFeature,
        variables: {
            id: feature.id,
            enabled: feature.enabled,
            version: feature._version
        }
    });
    console.log(JSON.stringify(sresult));
}

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log('===env===');
    //console.log(process.env);
    console.log('===event===');
    console.log(JSON.stringify(event, null, 2));

    const featureId = event.arguments.featureId;
    const enabled = event.arguments.enabled;
    const owner = event.identity.claims.sub;

    try {
        console.log('Updating Feature: ', featureId);
        var feature = await queryFeature(featureId);

        if (feature === null) {
            console.log('Feature not found, aborting.');
            return null
        }
        console.log('Fetched Feature: ', feature.featureName);
        feature.enabled = enabled;

        await pushFeatureMutation(feature);

        console.log('===SUCCESS===');
        return feature;
    } catch (err) {
        console.log('===ERROR===');
        console.log(err);
        return { error: err }
    }
};
