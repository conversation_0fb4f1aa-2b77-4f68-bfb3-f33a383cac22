/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAP<PERSON>NDPOINTOUTPUT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	API_FULLSWINGFLIGHT_LMCLUBTABLE_ARN
	API_FULLSWINGFLIGHT_LMCLUBTABLE_NAME
	API_FULLSWINGFLIGHT_LMSHOTTABLE_ARN
	API_FULLSWINGFLIGHT_LMSHOTTABLE_NAME
	ENV
	REGION
Amplify Params - DO NOT EDIT */

const AWS = require('aws-sdk');

/*
* DynamoDB
*/
const docClient = new AWS.DynamoDB.DocumentClient();

exports.handler = async (event) => {
    //console.log('===env===');
    //console.log(process.env);
    //console.log('===event===');
    //console.log(event);

    var valueType = event.arguments.value;
    valueType = valueType[0].toLowerCase() + valueType.slice(1);

    var shotparams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMSHOTTABLE_NAME,
        Limit: 50000,
        IndexName: 'byUserInRange',
        KeyConditionExpression: '#owner = :owner AND #timestamp BETWEEN :start AND :end',
        ProjectionExpression: `clubId, ${valueType}`,
        ExpressionAttributeValues: { 
            ':owner': event.arguments.owner,
            ':start': event.arguments.start,
            ':end': event.arguments.end
        },
        ExpressionAttributeNames: { 
            '#owner': 'owner',
            '#timestamp': 'timestamp'
        },
    }

    try {
        // Prepare results
        var clubHsh = {};
        var returnVal = [];

        // Fetch Shots
        const sdata = await docClient.query(shotparams).promise();
        // console.log('===shots===');
        // console.log(JSON.stringify(sdata));
        for (const shot of sdata['Items']) {
            var clubId = shot['clubId'];
            var value = shot[valueType];
            //console.log('Shot with clubId: ', clubId);

            clubHsh[clubId] = clubHsh[clubId] || [0,0];
            clubHsh[clubId] = [clubHsh[clubId][0] + value, clubHsh[clubId][1] + 1];
        }
        for (const clubId in clubHsh) {
            var avg = clubHsh[clubId][0] / clubHsh[clubId][1];
            returnVal.push({clubId: clubId, average: avg});
        }
        // console.log('===return===');
        // console.log(JSON.stringify(returnVal));

        return returnVal;
    } catch (err) {
        console.log('===ERROR===');
        console.log(err);
        return { error: err }
    }
}