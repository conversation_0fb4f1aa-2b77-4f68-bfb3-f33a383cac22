/**
 * @type {import('@types/aws-lambda').VerifyAuthChallengeResponseTriggerHandler}
 */
const { CognitoJwtVerifier } = require('aws-jwt-verify');

// Configure verifier for your Auth Portal
const verifier = CognitoJwtVerifier.create({
  userPoolId: process.env.AUTH_PORTAL_USER_POOL_ID,
  tokenUse: 'id',
  clientId: process.env.AUTH_PORTAL_CLIENT_ID,
});

exports.handler = async (event) => {
  console.log('VerifyAuthChallenge event:', JSON.stringify(event, null, 2));
  console.log('Environment variables:');
  console.log('AUTH_PORTAL_USER_POOL_ID:', process.env.AUTH_PORTAL_USER_POOL_ID);
  console.log('AUTH_PORTAL_CLIENT_ID:', process.env.AUTH_PORTAL_CLIENT_ID);

  try {
    const idToken = event.request.challengeAnswer;
    const expectedEmail = event.request.privateChallengeParameters.expectedEmail || event.request.userAttributes.email;
    
    console.log('ID Token length:', idToken ? idToken.length : 'undefined');
    console.log('Expected email:', expectedEmail);

    // Verify the ID token from Auth Portal
    const payload = await verifier.verify(idToken);
    
    // Check if the email matches
    if (payload.email === expectedEmail) {
      event.response.answerCorrect = true;
    } else {
      console.log('Email mismatch:', payload.email, 'vs', expectedEmail);
      event.response.answerCorrect = false;
    }
    
  } catch (error) {
    console.error('ID token verification failed:', error);
    event.response.answerCorrect = false;
  }

  return event;
};
