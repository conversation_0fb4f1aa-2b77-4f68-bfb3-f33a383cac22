{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"Amplify\",\"createdWith\":\"13.0.1\",\"stackType\":\"function-Lambda\",\"metadata\":{\"whyContinueWithGen1\":\"\"}}", "Parameters": {"authPortalUserPoolId": {"Type": "String", "Default": ""}, "authPortalClientId": {"Type": "String", "Default": ""}, "modules": {"Type": "String", "Default": "", "Description": "Comma-delimited list of modules to be executed by a lambda trigger. Sent to resource as an env variable."}, "resourceName": {"Type": "String", "Default": ""}, "trigger": {"Type": "String", "Default": "true"}, "functionName": {"Type": "String", "Default": ""}, "roleName": {"Type": "String", "Default": ""}, "parentResource": {"Type": "String", "Default": ""}, "parentStack": {"Type": "String", "Default": ""}, "env": {"Type": "String"}, "deploymentBucketName": {"Type": "String"}, "s3Key": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse", {"Fn::Join": ["", ["fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "MODULES": {"Ref": "modules"}, "REGION": {"Ref": "AWS::Region"}, "AUTH_PORTAL_USER_POOL_ID": {"Ref": "authPortalUserPoolId"}, "AUTH_PORTAL_CLIENT_ID": {"Ref": "authPortalClientId"}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs22.x", "Timeout": 25, "Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse", {"Fn::Join": ["", ["fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse", "-", {"Ref": "env"}]]}]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}]}}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}}}