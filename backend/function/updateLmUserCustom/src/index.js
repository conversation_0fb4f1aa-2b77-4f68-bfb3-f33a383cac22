/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	API_FULLSWINGFLIGHT_LMUSERTABLE_ARN
	API_FULLSWINGFLIGHT_LMUSERTABLE_NAME
	AUTH_FULLSWINGFLIGHT03FF7FF503FF7FF5_USERPOOLID
	ENV
	REGION
Amplify Params - DO NOT EDIT */
const AWS = require('aws-sdk');
const {success, error} = require('/opt/nodejs/response');
const {returnErrorFn, getValidArgument} = require('/opt/nodejs/util');
const docClient = new AWS.DynamoDB.DocumentClient();
const gql = require('graphql-tag');
const AppsyncClient = require('appsync-client').default;
const {
    CognitoIdentityProviderClient,
    AdminListGroupsForUserCommand,
    AdminAddUserToGroupCommand,
    AdminRemoveUserFromGroupCommand
} = require('@aws-sdk/client-cognito-identity-provider');
const cognitoIdentityServiceProvider = new CognitoIdentityProviderClient({});
const client = new AppsyncClient({
    // Required
    apiUrl: process.env.API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT,
    // Optional - these will default to process.env values (e.g. the IAM
    // role of the Lambda)
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    sessionToken: process.env.AWS_SESSION_TOKEN
});
const USERS_ROLE = "Users";
const COACHES_ROLE = "Coaches";
const updateLmUserQuery = gql`
    mutation updateLmUser($input: UpdateLmUserInput!) {
        updateLmUser(input: $input) {
            id
            setupComplete
            email
            phone
            fullName
            profileImage
            userType
            gender
            handedness
            birthdate
            companyName
            shippingAddressLine1
            shippingAddressLine2
            shippingPostcode
            shippingLocality
            shippingRegion
            shippingCountry
            subscriptions
            competitiveLevel
            teamName
            organizationSchool
            baseballPlayerPosition
            playerTeams {
                items {
                    id
                    owner
                    readers
                    type
                    status
                    email
                    joined_at
                    lmTeamId
                    lmPlayerId
                    lmPlayerName
                    lineupOrderBatting
                    lineupOrderPitching
                    team {
                        id
                        owner
                        name
                        profileImage
                        pitcherId
                        createdAt
                        updatedAt
                        _version
                        _deleted
                        _lastChangedAt
                        __typename
                    }
                    createdAt
                    updatedAt
                    _version
                    _deleted
                    _lastChangedAt
                    __typename
                }
                nextToken
                startedAt
                __typename
            }
            createdAt
            updatedAt
            _version
            _deleted
            _lastChangedAt
            owner
            __typename
        }}`

const ERROR = {
    INVALID_ROLE_SELECTION: {
        errorType: "errors.auth.invalid_role_selection",
        message: "The selected role is invalid. Please choose a valid role."
    },
    USER_NOT_FOUND: {
        errorType: "errors.common.user_not_found",
        message: "does not exist or has been deleted"
    }
}
const USER_POOL_ID = process.env.AUTH_FULLSWINGFLIGHT03FF7FF503FF7FF5_USERPOOLID;

const updateUserRoleFn = async (userName, group) => {
    console.log('Updating user role for user: ', userName, group);
    const result = await cognitoIdentityServiceProvider.send(
        new AdminListGroupsForUserCommand({
            UserPoolId: USER_POOL_ID,
            Username: userName
        })
    );
    const existGroups = result.Groups?.map(group => group.GroupName) || [];
    if (existGroups.includes(group)) {
        console.log(`User [${userName}] already in group [${USERS_ROLE}]`)
    } else {
        const addUserParams = {
            UserPoolId: USER_POOL_ID,
            Username: userName,
            GroupName: group,
        };
        const removeUserParams = {
            UserPoolId: USER_POOL_ID,
            Username: userName,
        }
        if (group === COACHES_ROLE && existGroups.includes(USERS_ROLE)) {
            removeUserParams.GroupName = USERS_ROLE;
        } else if (group === USERS_ROLE && existGroups.includes(COACHES_ROLE)) {
            removeUserParams.GroupName = COACHES_ROLE;
        }
        await cognitoIdentityServiceProvider.send(new AdminAddUserToGroupCommand(addUserParams));
        if (removeUserParams.GroupName) {
            console.log(`Removing user [${userName}] from group [${removeUserParams.GroupName}]`)
            await cognitoIdentityServiceProvider.send(new AdminRemoveUserFromGroupCommand(removeUserParams));
        }
    }
    console.log('Updated user role for user: ', userName, group);
}

const getLmUserFn = async (userId) => {
    console.log('Getting user', userId);
    const params = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMUSERTABLE_NAME,
        Key: {
            id: userId
        }
    };
    const result = await docClient.get(params).promise();
    if (!result || !result.Item) return returnErrorFn(ERROR.USER_NOT_FOUND);
    console.log('Got user', userId);
    return result.Item;
}

const updateLmUserFn = async (userId, input) => {
    console.log('Updating user fn', userId);
    const user = await getLmUserFn(userId);
    const competitiveLevel = getValidArgument(input['competitiveLevel']);
    const teamName = getValidArgument(input['teamName']);
    const organizationSchool = getValidArgument(input['organizationSchool']);
    const baseballPlayerPosition = getValidArgument(input['baseballPlayerPosition']);
    const firstName = getValidArgument(input['firstName']);
    const lastName = getValidArgument(input['lastName']);
    const birthdate = getValidArgument(input['birthdate']);
    const phone = getValidArgument(input['phone']);
    const handedness = getValidArgument(input['handedness']);
    const updateParams = {};
    if (competitiveLevel) updateParams['competitiveLevel'] = competitiveLevel;
    if (teamName) updateParams['teamName'] = teamName;
    if (organizationSchool) updateParams['organizationSchool'] = organizationSchool;
    if (baseballPlayerPosition) updateParams['baseballPlayerPosition'] = baseballPlayerPosition;
    if (firstName) {
        updateParams['fullName'] = firstName;
        if (lastName) updateParams['fullName'] += ` ${lastName}`;
    }
    if (birthdate) updateParams['birthdate'] = birthdate;
    if (phone) updateParams['phone'] = phone;
    if (handedness) {
        if (!['Left', 'Right'].includes(handedness)) updateParams['handedness'] = 'Right';
        else updateParams['handedness'] = handedness;
    }
    if (Object.keys(updateParams).length === 0) return;
    updateParams['id'] = userId;
    updateParams['_version'] = user['_version'];
    const result = await client.request({
        query: updateLmUserQuery,
        variables: {
            input: updateParams
        }
    });
    if (!result || !result['updateLmUser'] || !result['updateLmUser']['id']) throw new Error('Update user fn fail');
    console.log('Updated user fn', userId);
}
/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log('===env===', event);
    try {
        const userName = event.identity.username;
        const input = event.arguments.input;
        const role = input['role'];
        console.log('Updating user: ', userName);
        if (role && ![USERS_ROLE, COACHES_ROLE].includes(role)) return returnErrorFn(ERROR.INVALID_ROLE_SELECTION)
        if (role) await updateUserRoleFn(userName, role);
        await updateLmUserFn(userName, input);
        console.log('Updated user: ', userName);
        return success();
    } catch (e) {
        console.error(e)
        return error(e.name === "CUSTOM_ERROR" ? {errorType: e.code, message: e.message} : undefined);
    }
};
