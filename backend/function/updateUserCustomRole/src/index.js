/* Amplify Params - DO NOT EDIT
	AUTH_AFGEN1DB337F14DB337F14_USERPOOLID
	ENV
	REGION
Amplify Params - DO NOT EDIT */

const {
    CognitoIdentityProviderClient,
    AdminUpdateUserAttributesCommand
} = require('@aws-sdk/client-cognito-identity-provider');

const cognitoIdentityServiceProvider = new CognitoIdentityProviderClient({});
/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    const role = event.role
    const userPoolId = event.userPoolId
    const userName = event.userName
    console.log(`Updating user custom role [${role}] of user [${userName}]`)
    try{
        const updateUserAttributesParams = {
            UserPoolId: userPoolId,
            Username: userName,
            UserAttributes: [
                {
                    Name: "custom:role",
                    Value: role
                }
            ]
        };
        await cognitoIdentityServiceProvider.send(
            new AdminUpdateUserAttributesCommand(updateUserAttributesParams)
        );
        console.log(`Updated user custom role [${role}] of user [${userName}]`)
    }catch (e) {
        console.error(`Cannot update user custom role [${role}] of user [${userName}]`, e)
    }
    return {
        statusCode: 200,
    };
};
