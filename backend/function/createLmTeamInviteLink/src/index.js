/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	API_FULLSWINGFLIGHT_LMTEAMINVITECODESTABLE_ARN
	API_FULLSWINGFLIGHT_LMTEAMINVITECODESTABLE_NAME
	API_FULLSWINGFLIGHT_LMTEAMTABLE_ARN
	API_FULLSWINGFLIGHT_LMTEAMTABLE_NAME
	ENV
	FUNCTION_CREATEBRANCHIOINVITELINK_NAME
	REGION
Amplify Params - DO NOT EDIT */
const AWS = require('aws-sdk');
const {v4: uuidv4} = require('uuid');
const lambda = new AWS.Lambda();
const {success, error} = require('/opt/nodejs/response')
const {returnErrorFn} = require('/opt/nodejs/util')

const docClient = new AWS.DynamoDB.DocumentClient();
const ERROR = {
    TEAM_NOT_FOUND: {
        errorType: "errors.team.not_found",
        message: "Team does not exist or has been deleted."
    },
    NOT_TEAM_OWNER: {
        errorType: "errors.team.not_owner",
        message: "Only the team owner can perform this action."
    },
    MISSING_INVITE_EMAIL: {
        errorType: "errors.invite.missing_email",
        message: "Invite email is required."
    },
    MISSING_SSM_KEY: {
        errorType: "errors.common.missing_ssm_key",
        message: "SSM key is required."
    },
    BRANCH_IO_ERROR: {
        errorType: "errors.invite.branch_io_error",
        message: "BranchIO error. Please try again later."
    },
    INTERNAL_SERVER_ERROR: {
        errorType: "errors.common.internal_server_error",
        message: "Internal Server Error"
    }
}

const branchIOCreateLinkFn = async (inviteCode, teamName, teamImage) => {
    console.log('Creating BranchIO link for invite code: ', inviteCode);
    const result = await lambda
        .invoke({
            FunctionName: process.env.FUNCTION_CREATEBRANCHIOINVITELINK_NAME,
            InvocationType: 'RequestResponse',
            Payload: JSON.stringify({inviteCode, teamName, teamImage})
        })
        .promise();
    const payload = JSON.parse(result.Payload);
    if (!payload || !payload['link']) return returnErrorFn(ERROR.INVITE_LINK_INVALID);
    const link = payload['link'];
    console.log('Created BranchIO link for invite code: ', inviteCode, link);
    return link;
}

const getTeamByIdFn = async (lmTeamId) => {
    const result = await docClient.get({
        TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMTABLE_NAME,
        Key: {
            id: lmTeamId
        }
    }).promise();
    const team = result.Item;
    if (!team) return returnErrorFn(ERROR.TEAM_NOT_FOUND);
    return team;
}
/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
    console.log('===env===', event);
    const lmTeamId = event.arguments.lmTeamId;
    const inviteType = 'ShareInvitation';
    try {
        console.log('Creating invite link for team: ', lmTeamId);
        const team = await getTeamByIdFn(lmTeamId);
        if (!team) return returnErrorFn(ERROR.TEAM_NOT_FOUND);
        if (team['owner'] !== event.identity.username) return returnErrorFn(ERROR.NOT_TEAM_OWNER);
        const inviteId = uuidv4();
        const link = await branchIOCreateLinkFn(inviteId, team.name, team.profileImage);
        const inviteCode = {
            id: inviteId,
            link,
            owner: event.identity.username,
            sport: team.sport,
            type: inviteType,
            lmTeamId: lmTeamId
        };
        await docClient.put({
            TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMINVITECODESTABLE_NAME,
            Item: inviteCode
        }).promise();
        console.log('Created invite link for team: ', team.name, link);
        return success({link});
    } catch (e) {
        console.error(e)
        return error(e.name === "CUSTOM_ERROR" ? {errorType: e.code, message: e.message} : undefined);
    }
};
