{"permissions": {"function": {"createBranchIoInviteLink": ["read"]}, "storage": {"LmTeam:@model(appsync)": ["read"], "LmTeamInviteCodes:@model(appsync)": ["create", "read"]}}, "lambdaLayers": [{"type": "ProjectLayer", "resourceName": "fullswingflightstatsShared", "version": "Always choose latest version", "isLatestVersionSelected": true, "env": "dev"}], "environmentVariableList": [{"cloudFormationParameterName": "branchIoApiHost", "environmentVariableName": "BRANCH_IO_API_HOST"}, {"cloudFormationParameterName": "invitationDeepLinkPath", "environmentVariableName": "INVITATION_DEEP_LINK_PATH"}, {"cloudFormationParameterName": "authFullswingPortalUrl", "environmentVariableName": "AUTH_FULLSWING_PORTAL_URL"}], "secretNames": []}