{"permissions": {"api": {"fullswingflight": ["Mutation"]}, "auth": {"fullswingflight03ff7ff503ff7ff5": ["read"]}, "storage": {"LmTeamPlayers:@model(appsync)": ["create", "read", "update"], "LmTeam:@model(appsync)": ["read", "update"], "LmTeamInviteCodes:@model(appsync)": ["read"], "LmCoachPlayers:@model(appsync)": ["create", "read", "update"]}}, "lambdaLayers": [{"type": "ProjectLayer", "resourceName": "fullswingflightstatsShared", "version": "Always choose latest version", "isLatestVersionSelected": true, "env": "dev"}]}