/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT
	API_FULLSWINGFLIGHT_GRAPHQLAPIIDOUTPUT
	API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_ARN
	API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_NAME
	API_FULLSWINGFLIGHT_LMDATARESULTSTABLE_ARN
	API_FULLSWINGFLIGHT_LMDATARESULTSTABLE_NAME
	API_FULLSWINGFLIGHT_LMDATASESSIONTABLE_ARN
	API_FULLSWINGFLIGHT_LMDATASESSIONTABLE_NAME
	API_FULLSWINGFLIGHT_LMTEAMINVITECODESTABLE_ARN
	API_FULLSWINGFLIGHT_LMTEAMINVITECODESTABLE_NAME
	API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_ARN
	API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME
	API_FULLSWINGFLIGHT_LMTEAMTABLE_ARN
	API_FULLSWINGFLIGHT_LMTEAMTABLE_NAME
	AUTH_FULLSWINGFLIGHT03FF7FF503FF7FF5_USERPOOLID
	ENV
	REGION
Amplify Params - DO NOT EDIT */
const AWS = require("aws-sdk");
const {
  CognitoIdentityProviderClient,
  AdminGetUserCommand,
} = require("@aws-sdk/client-cognito-identity-provider");
const { success, error } = require("/opt/nodejs/response");
const { returnErrorFn } = require("/opt/nodejs/util");
const docClient = new AWS.DynamoDB.DocumentClient();
const gql = require("graphql-tag");
const AppsyncClient = require("appsync-client").default;
const cognitoIdentityServiceProvider = new CognitoIdentityProviderClient({});
const client = new AppsyncClient({
  // Required
  apiUrl: process.env.API_FULLSWINGFLIGHT_GRAPHQLAPIENDPOINTOUTPUT,
  // Optional - these will default to process.env values (e.g. the IAM
  // role of the Lambda)
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  sessionToken: process.env.AWS_SESSION_TOKEN,
});
const createLmTeamPlayerQuery = gql`
  mutation createLmTeamPlayers($input: CreateLmTeamPlayersInput!) {
    createLmTeamPlayers(input: $input) {
      id
      owner
      readers
      type
      status
      email
      joined_at
      lmTeamId
      lmPlayerId
      lmPlayerName
      lineupOrderBatting
      lineupOrderPitching
      team {
        id
        owner
        name
        profileImage
        pitcherId
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
const updateLmTeamPlayerQuery = gql`
  mutation updateLmTeamPlayers($input: UpdateLmTeamPlayersInput!) {
    updateLmTeamPlayers(input: $input) {
      id
      owner
      readers
      type
      status
      email
      joined_at
      lmTeamId
      lmPlayerId
      lmPlayerName
      lineupOrderBatting
      lineupOrderPitching
      team {
        id
        owner
        name
        profileImage
        pitcherId
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
const updateLmTeamQuery = gql`
  mutation updateLmTeam($input: UpdateLmTeamInput!) {
    updateLmTeam(input: $input) {
      _deleted
      _lastChangedAt
      _version
      createdAt
      id
      name
      owner
      pitcherId
      profileImage
      updatedAt
      readers
    }
  }
`;
const MAX_TEAM_PLAYERS = 30;

const ERROR = {
  INVITE_CODE_NOT_FOUND: {
    errorType: "errors.invite.code_not_found",
    message: "Invite code does not exist or has been deleted.",
  },
  TEAM_PLAYER_LIMIT_REACHED: {
    errorType: "errors.team.player_limit_reached",
    message: `A team can have a maximum of ${MAX_TEAM_PLAYERS} players.`,
  },
  INVITE_CODE_INVALID: {
    errorType: "errors.invite.code_invalid",
    message: "Invite code invalid.",
  },
  INVITE_CODE_PERMISSION: {
    errorType: "errors.invite.code_permission",
    message: "You do not have permission to use this invite code.",
  },
  PLAYER_ALREADY_IN_TEAM: {
    errorType: "errors.invite.player_already_in_team",
    message: "Player is already a member of this team.",
  },
  INTERNAL_SERVER_ERROR: {
    errorType: "errors.common.internal_server_error",
    message: "Internal Server Error",
  },
  SELF_INVITE_ACTION_FORBIDDEN: {
    errorType: "errors.invite.self_action_forbidden",
    message: "You cannot perform actions on invites you created.",
  },
};
const createLmCoachPlayersQuery = gql`
  mutation createLmCoachPlayers($input: CreateLmCoachPlayersInput!) {
    createLmCoachPlayers(input: $input) {
      _deleted
      _lastChangedAt
      _version
      createdAt
      id
      lmCoachId
      lmPlayerEmail
      lmPlayerId
      lmPlayerName
      owner
      teamAssigned
      updatedAt
    }
  }
`;
const updateLmCoachPlayersQuery = gql`
  mutation updateLmCoachPlayers($input: UpdateLmCoachPlayersInput!) {
    updateLmCoachPlayers(input: $input) {
      id
      lmCoachId
      owner
      lmPlayerId
      lmPlayerName
      status
      lmPlayerEmail
      teamAssigned
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
const updateLmDataSession = gql`
  mutation updateLmDataSession($input: UpdateLmDataSessionInput!) {
    updateLmDataSession(input: $input) {
      id
      deviceID
      lmProfileId
      lmTeamId
      owner
      sport
      startTimestamp
      endTimestamp
      duration
      readers
      name
      details
      city
      state
      country
      address
      locationName
      elevation
      latitude
      longitude
      temperature
      humidity
      location
      playMode
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
const updateLmDataResults = gql`
  mutation updateLmDataResults($input: UpdateLmDataResultsInput!) {
    updateLmDataResults(input: $input) {
      id
      resultId
      sequenceNumber
      lmSessionId
      owner
      sport
      readers
      timestamp
      isFavorite
      videoKey
      pointCloudKey
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;

const verifyInviteInfoFn = async (inviteCode, userName) => {
  console.log(`Verifying invite code [${inviteCode}]`);
  if (!inviteCode || inviteCode === "") {
    return returnErrorFn(ERROR.INVITE_CODE_INVALID);
  }
  const result = await docClient
    .get({
      TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMINVITECODESTABLE_NAME,
      Key: {
        id: inviteCode,
      },
    })
    .promise();
  if (!result || !result.Item) {
    return returnErrorFn(ERROR.INVITE_CODE_NOT_FOUND, inviteCode);
  }
  const inviteInfo = result.Item;
  console.log(`Verified invite code [${inviteCode}]. Info: ${JSON.stringify(inviteInfo)}`);
  return inviteInfo;
};

const updateReadersOfTeamFn = async (lmTeamId, reader) => {
  try {
    console.log(
      `Updating readers of team [${lmTeamId}] add reader [${reader}]`
    );
    if (!lmTeamId || !reader) {
      console.log(`Invalid team ID [${lmTeamId}] or reader [${reader}]`);
      return;
    }
    const result = await docClient
      .get({
        TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMTABLE_NAME,
        Key: {
          id: lmTeamId,
        },
      })
      .promise();
    if (!result || !result.Item) {
      throw new Error(`Team [${lmTeamId}] not found`);
    }
    const item = result.Item;
    const readers = item["readers"] || [];
    if (readers.includes(reader)) {
      // Reader already exists, no need to add
      console.log(`Reader [${reader}] already exists in team [${lmTeamId}]`);
      return;
    }
    readers.push(reader);
    const resultUpdate = await client.request({
      query: updateLmTeamQuery,
      variables: {
        input: {
          id: item["id"],
          _version: item["_version"],
          readers,
        },
      },
    });
    if (
      !resultUpdate ||
      !resultUpdate["updateLmTeam"] ||
      !resultUpdate["updateLmTeam"]["id"]
    ) {
      console.error(
        `Fail updating readers of team [${lmTeamId}] add reader [${reader}]`,
        resultUpdate
      );
    } else {
      console.log(
        `Updated readers of team [${lmTeamId}] add reader [${reader}]`
      );
    }
  } catch (e) {
    console.error(
      `Error updating readers of team [${lmTeamId}] add reader [${reader}]`,
      e
    );
  }
};

const addUserToTeamFn = async (
  lmTeamId,
  lmPlayerId,
  owner,
  fullName,
  email
) => {
  console.log(`Adding user [${lmPlayerId}] to team [${lmTeamId}]`);
  const params = {
    TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME,
    IndexName: "byTeam",
    KeyConditionExpression: "lmTeamId = :lmTeamId",
    ExpressionAttributeValues: {
      ":lmTeamId": lmTeamId,
    },
  };
  const result0 = await docClient.query(params).promise();
  let count = 0;
  let lineupOrderBatting = 1;
  if (result0 && result0["Items"] && result0["Items"].length > 0) {
    count = result0["Items"].length;
    if (count >= MAX_TEAM_PLAYERS)
      return returnErrorFn(ERROR.TEAM_PLAYER_LIMIT_REACHED);
    const battingPlayers = result0["Items"].filter(
      (item) => item["type"] === "Batting" || item["type"] === "Both"
    );
    const maxOrder = Math.max(
      0,
      ...battingPlayers.map((o) => o.lineupOrderBatting)
    );
    lineupOrderBatting =
      maxOrder > battingPlayers.length
        ? maxOrder + 1
        : battingPlayers.length + 1;
    const match = result0["Items"].find(
      (item) => item["lmPlayerId"] === lmPlayerId
    );
    if (match) {
      console.log(
        `Player [${lmPlayerId}] already exists in team [${lmTeamId}]`
      );
      return;
    }
    // return returnErrorFn(
    //   ERROR.PLAYER_ALREADY_IN_TEAM,
    //   `playerId: ${lmPlayerId}, teamId: ${lmTeamId}`
    // );
  }
  const input = {
    lmTeamId,
    lmPlayerId,
    owner,
    readers: [lmPlayerId],
    type: "Batting",
    lineupOrderBatting,
    lineupOrderPitching: 0,
    status: "Accepted",
    joined_at: new Date().toISOString(),
  };
  if (fullName) input["lmPlayerName"] = fullName;
  if (email) input["email"] = email;
  const result = await client.request({
    query: createLmTeamPlayerQuery,
    variables: {
      input,
    },
  });
  if (
    !result ||
    !result["createLmTeamPlayers"] ||
    !result["createLmTeamPlayers"]["id"]
  )
    throw new Error(`Add player [${lmPlayerId}] to team [${lmTeamId}] failed`);
  console.log(`Added user [${lmPlayerId}] to team [${lmTeamId}]`);
};

const addCoachPlayerFn = async (
  lmTeamId,
  lmCoachId,
  lmPlayerId,
  lmPlayerEmail,
  teamAssigned,
  fullName,
  email
) => {
  console.log(
    `Adding coach player, coach [${lmCoachId}] player [${
      lmPlayerId || lmPlayerEmail
    }]`
  );

  // Check if the team exists
  const params1 = {
    TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME,
    IndexName: "byTeam",
    KeyConditionExpression: "lmTeamId = :lmTeamId",
    ExpressionAttributeValues: {
      ":lmTeamId": lmTeamId,
    },
  };
  const result0 = await docClient.query(params1).promise();
  if (result0 && result0["Items"] && result0["Items"].length > 0) {
    const match = result0["Items"].find(
      (item) => item["lmPlayerId"] === lmPlayerId
    );
    if (match)
      console.log(
        `Player [${lmPlayerId}] already exists in team [${lmTeamId}]. match = ${JSON.stringify(
          match
        )}`
      );
    // Player already exists in the team
    return lmPlayerId;
  }

  let playerName;
  try {
    // Check if the coach player already exists
    const params = {
      TableName: process.env.API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_NAME,
      IndexName: "byCoach",
      KeyConditionExpression: "lmCoachId = :lmCoachId",
      FilterExpression: "lmPlayerId = :lmPlayerId",
      ExpressionAttributeValues: {
        ":lmCoachId": lmCoachId,
        ":lmPlayerEmail": lmPlayerEmail,
        ":lmPlayerId": lmPlayerId,
      },
    };
    if (!lmPlayerId) {
      params["FilterExpression"] = "lmPlayerEmail = :lmPlayerEmail";
    }
    const result = await docClient.query(params).promise();
    if (!result || !result["Items"] || result["Items"].length === 0) {
      const input = {
        lmCoachId,
        owner: lmCoachId,
        teamAssigned,
        status: "Accepted",
      };
      if (lmPlayerId) {
        input["lmPlayerId"] = lmPlayerId;
      }
      if (email) input["lmPlayerEmail"] = email;
      if (fullName) input["lmPlayerName"] = fullName;
      const result = await client.request({
        query: createLmCoachPlayersQuery,
        variables: {
          input,
        },
      });
      if (
        !result ||
        !result["createLmCoachPlayers"] ||
        !result["createLmCoachPlayers"]["id"]
      )
        throw new Error(
          `Add coach player, coach [${lmCoachId}] player [${
            lmPlayerId || lmPlayerEmail
          }] failed`
        );
      console.log(
        `Added coach player, coach [${lmCoachId}] player [${
          lmPlayerId || lmPlayerEmail
        }]`
      );
    } else if (result && result["Items"] && result["Items"].length > 0) {
      if (teamAssigned === "Y" && result["Items"][0]["teamAssigned"] === "N") {
        playerName = result["Items"][0]["lmPlayerName"];
        await updateCoachPlayerTeamAssignedFn(result["Items"][0], "Y");
      } else
        console.log(
          `Coach player, coach [${lmCoachId}] player [${
            lmPlayerId || lmPlayerEmail
          }] already exists`
        );
    }
  } catch (e) {
    console.error(
      `Adding coach player, coach [${lmCoachId}] player [${
        lmPlayerId || lmPlayerEmail
      }] has error`,
      e
    );
  }
  return playerName;
};

const updateTeamPlayerFn = async (lmTeamId, lmPlayerId) => {
  if (lmTeamId) {
    console.log(
      `Updating team player, team [${lmTeamId}] player [${lmPlayerId}]`
    );
    const params = {
      TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME,
      IndexName: "byPlayer",
      KeyConditionExpression: "lmPlayerId = :lmPlayerId",
      FilterExpression: "lmTeamId = :lmTeamId",
      ExpressionAttributeValues: {
        ":lmPlayerId": lmPlayerId,
        ":lmTeamId": lmTeamId,
      },
    };
    const result = await docClient.query(params).promise();
    if (result && result["Items"] && result["Items"].length > 0) {
      const lmTeamPlayer = result["Items"][0];
      if (result["Items"][0]["status"] === "Pending") {
        await updateReadersOfTeamFn(lmTeamId, lmPlayerId);
        const resultUpdateLmTeamPlayer = await client.request({
          query: updateLmTeamPlayerQuery,
          variables: {
            input: {
              id: lmTeamPlayer["id"],
              _version: lmTeamPlayer["_version"],
              status: "Accepted",
              joined_at: new Date().toISOString(),
            },
          },
        });
        if (
          !resultUpdateLmTeamPlayer ||
          !resultUpdateLmTeamPlayer["updateLmTeamPlayers"] ||
          !resultUpdateLmTeamPlayer["updateLmTeamPlayers"]["id"]
        ) {
          console.error("Update team player fn fail", resultUpdateLmTeamPlayer);
        } else {
          console.log(
            `Updated team player, team [${lmTeamId}] player [${lmPlayerId}]`
          );
        }
      }
    } else {
      console.log(
        `Team player, team [${lmTeamId}] player [${lmPlayerId}] does not exist`
      );
    }
  }
};
const updateCoachPlayerFn = async (lmPlayerId, lmCoachId) => {
  console.log(
    `Updating coach player, coach [${lmCoachId}] player [${lmPlayerId}]`
  );
  const params = {
    TableName: process.env.API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_NAME,
    IndexName: "byCoach",
    KeyConditionExpression: "lmCoachId = :lmCoachId",
    FilterExpression: "lmPlayerId = :lmPlayerId",
    ExpressionAttributeValues: {
      ":lmCoachId": lmCoachId,
      ":lmPlayerId": lmPlayerId,
    },
  };
  const result = await docClient.query(params).promise();
  if (result && result["Items"] && result["Items"].length > 0) {
    const lmCoachPlayer = result["Items"][0];
    if (result["Items"][0]["status"] === "Pending") {
      const resultUpdateLmCoachPlayer = await client.request({
        query: updateLmCoachPlayersQuery,
        variables: {
          input: {
            id: lmCoachPlayer["id"],
            status: "Accepted",
            _version: lmCoachPlayer["_version"],
          },
        },
      });
      if (
        !resultUpdateLmCoachPlayer ||
        !resultUpdateLmCoachPlayer["updateLmCoachPlayers"] ||
        !resultUpdateLmCoachPlayer["updateLmCoachPlayers"]["id"]
      ) {
        console.error("Update coach player fn fail", resultUpdateLmCoachPlayer);
      } else {
        console.log(
          `Updated coach player, coach [${lmCoachId}] player [${lmPlayerId}]`
        );
      }
    }
  } else {
    console.log(
      `Coach player, coach [${lmCoachId}] player [${lmPlayerId}] does not exist`
    );
  }
};
const updateCoachPlayerTeamAssignedFn = async (item, teamAssigned) => {
  console.log(
    `Updating coach player [${item["id"]}] team assigned to ${teamAssigned}]`
  );
  try {
    const result = await client.request({
      query: updateLmCoachPlayersQuery,
      variables: {
        input: {
          id: item["id"],
          teamAssigned,
          _version: item["_version"],
        },
      },
    });
    if (
      !result ||
      !result["updateLmCoachPlayers"] ||
      !result["updateLmCoachPlayers"]["id"]
    ) {
      console.error("Update coach player fn fail", result);
    } else {
      console.log(
        `Updated coach player [${item["id"]}] team assigned to ${teamAssigned}]`
      );
    }
  } catch (e) {
    console.log(
      `Error updating coach player [${item["id"]}] team assigned to ${teamAssigned}]`,
      e
    );
  }
};

const getCognitoUserByIdFn = async (userId) => {
  console.log("Getting Cognito user by ID: ", userId);
  const command = new AdminGetUserCommand({
    UserPoolId: process.env.AUTH_FULLSWINGFLIGHT03FF7FF503FF7FF5_USERPOOLID,
    Username: userId,
  });

  const user = await cognitoIdentityServiceProvider.send(command);
  if (!user) return returnErrorFn(ERROR.USER_NOT_FOUND, userId);
  const userInfo = {};
  userInfo["userName"] = user["Username"];
  userInfo["userStatus"] = user["UserStatus"];
  user.UserAttributes.forEach((attr) => {
    userInfo[attr.Name] = attr.Value;
  });
  console.log("Got Cognito user by ID: ", userId, userInfo);
  return userInfo;
};

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event) => {
  console.log("===env===", event);
  try {
    const inviteCode = event.arguments["inviteCode"];
    const userName = event.identity["username"];
    console.log(`User [${userName}] accepting team invitation [${inviteCode}]`);
    const inviteInfo = await verifyInviteInfoFn(inviteCode, userName);
    if (inviteInfo["owner"] === userName) {
      return returnErrorFn(ERROR.SELF_INVITE_ACTION_FORBIDDEN);
    }
    // Get user info from Cognito
    const user = await getCognitoUserByIdFn(userName);
    const fullName = user["name"];
    const email = user["email"];
    console.log("User info: ", JSON.stringify(user));
    if (inviteInfo["type"] === "AddPlayerInvitation") {
      if (inviteInfo["email"] !== email) {
        return returnErrorFn(ERROR.INVITE_CODE_PERMISSION);
      }
      await Promise.all([
        updateTeamPlayerFn(inviteInfo["lmTeamId"], userName),
        updateCoachPlayerFn(userName, inviteInfo["owner"]),
      ]);
    } else {
      const [playerName] = await Promise.all([
        addCoachPlayerFn(
          inviteInfo["lmTeamId"],
          inviteInfo["owner"],
          userName,
          undefined,
          "Y",
          inviteInfo["lmPlayerName"] || fullName,
          email
        ),
        updateReadersOfTeamFn(inviteInfo["lmTeamId"], userName),
      ]);
      await addUserToTeamFn(
        inviteInfo["lmTeamId"],
        userName,
        inviteInfo["owner"],
        playerName || fullName,
        email
      );
    }
    // update readers of data session and data result
    await updateReadersFn(userName, email, inviteInfo["lmTeamId"]);
    console.log(`User [${userName}] accepted team invitation [${inviteCode}]`);
    return success();
  } catch (e) {
    console.error(e);
    return error(
      e.name === "CUSTOM_ERROR"
        ? { errorType: e.code, message: e.message }
        : undefined
    );
  }
};

/**
 * Helper function to update readers array in data sessions
 * @param {Array} dataSessions - Array of data session objects
 * @param {string} pendingReaderId - The pending reader ID to replace
 * @param {string} newUserId - The new user ID to replace with
 */
const updateDataSessionReadersFn = async (dataSessions, newUserId) => {
  for (const dataSession of dataSessions) {
    const readers = dataSession.readers || [];
    if (readers.includes(newUserId)) {
      console.log(
        `Data session [${dataSession.id}] already has new user [${newUserId}]`
      );
      continue;
    }
    readers.push(newUserId);
    // Update the data session with the new readers list
    const response = await client.request({
      query: updateLmDataSession,
      variables: {
        input: {
          id: dataSession.id,
          _version: dataSession._version,
          readers,
        },
      },
    });
    if (response && response["updateLmDataSession"] && response["updateLmDataSession"]["id"]) {
      console.log(`Updated readers of data session [${dataSession.id}]`);
    } else {
      console.error(`Error updating readers of data session [${dataSession.id}]`, response);
    }
  }
};

/**
 * Helper function to update readers array in data results
 * @param {Array} dataResults - Array of data result objects
 * @param {string} newUserId - The new user ID to replace with
 */
const updateDataResultReadersFn = async (dataResults, newUserId) => {
  for (const dataResult of dataResults) {
    const readers = dataResult.readers || [];
    if (readers.includes(newUserId)) {
      console.log(
        `Data result [${dataResult.id}] already has new user [${newUserId}]`
      );
      continue;
    }
    readers.push(newUserId);
    // Update the data result with the new readers list
    const response = await client.request({
      query: updateLmDataResults,
      variables: {
        input: {
          id: dataResult.id,
          _version: dataResult._version,
          readers,
        },
      },
    });
    if (response && response["updateLmDataResults"] && response["updateLmDataResults"]["id"]) {
      console.log(`Updated readers of data result [${dataResult.id}]`);
    } else {
      console.error(`Error updating readers of data result [${dataResult.id}]`, response);
    }
  }
};

/**
 * Helper function to check if query result has items
 * @param {Object} result - DynamoDB query/scan result
 * @returns {boolean} - True if result has items, false otherwise
 */
const hasItems = (result) => {
  return result && result.Items && result.Items.length > 0;
};

/**
 * Update readers of data sessions for a team
 * LmDataSession.readers is the list of readers
 * There're 2 cases:
 * 1. Player is added to a team
 *    LmDataSession.lmTeamId => LmTeam.id
 *    LmDataSession.readers is the list of LmTeamPlayers.id (if player is not a user, lmPlayerId is null, we use email to find the player)
 *    When player created a user account, the we got LmTeamPlayers updated
 *    Then we need to update LmDataSession.readers to include the new player (by replace the old LmTeamPlayers.id with the new LmTeamPlayers.lmPlayerId)
 * 2. Player is added to a coach
 *    LmDataSession.lmTeamId is null
 *    LmDataSession.readers is the list of LmCoachPlayers.id (if player is not a user, lmPlayerId is null, we use email to find the player)
 *    When player created a user account, the we got LmCoachPlayer updated
 *    Then we need to update LmDataSession.readers to include the new player (by replace the old LmCoachPlayers.id with the new LmCoachPlayers.lmPlayerId)
 * @param {string} newUserId - The ID of the the new user
 * @param {string} userEmail - The email of the the new user
 * @param {string} teamId - The ID of the team, if null, it means the player is added to a coach
 */

const updateReadersFn = async (newUserId, userEmail, teamId = null) => {
  try {
    console.log(
      `Updating readers of data sessions for user [${newUserId}], email [${userEmail}], team [${teamId}]`
    );
    let pendingReaderIds = [];
    if (teamId) {
      // Case 1: Player is added to a team
      // Find the LmTeamPlayer record for this user and team
      // First try to find by lmPlayerId (if player already had a user account)
      const teamPlayerParams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMTEAMPLAYERSTABLE_NAME,
        IndexName: "byTeam",
        KeyConditionExpression: "lmTeamId = :lmTeamId",
        FilterExpression: "email = :email", // AND attribute_not_exists(lmPlayerId)
        ExpressionAttributeValues: {
          ":lmTeamId": teamId,
          ":email": userEmail,
        },
      };
      const teamPlayerResult = await docClient
        .query(teamPlayerParams)
        .promise();
      if (!hasItems(teamPlayerResult)) {
        console.log(
          `No team player record found for email [${userEmail}] and team [${teamId}]`
        );
        return;
      }
      teamPlayerResult.Items.forEach((item) => {
        pendingReaderIds.push(item.id); // The LmTeamPlayers.id that might be in readers
      });
    } else {
      // Case 2: Player is added to a coach
      // For coach players, we need to scan all records and filter by email
      // since there's no direct email index for coach players
      const coachPlayerParams = {
        TableName: process.env.API_FULLSWINGFLIGHT_LMCOACHPLAYERSTABLE_NAME,
        FilterExpression: "lmPlayerEmail = :email", // attribute_not_exists(lmPlayerId)
        ExpressionAttributeValues: {
          ":email": userEmail,
        },
      };

      const coachPlayerResult = await docClient
        .scan(coachPlayerParams)
        .promise();
      if (!hasItems(coachPlayerResult)) {
        console.log(`No coach player records found for email [${userEmail}]`);
        return;
      }
      coachPlayerResult.Items.forEach((item) => {
        pendingReaderIds.push(item.id); // The LmCoachPlayers.id that might be in readers
      });
    }
    console.log(`Found old reader IDs: ${pendingReaderIds.join(", ")}`);
    // Update readers of data sessions
    if (pendingReaderIds.length > 0) {
      for (const pendingReaderId of pendingReaderIds) {
        await updateReadersOfDataSessionsFn(newUserId, pendingReaderId);
      }
    }

    // Update readers of data results
    if (pendingReaderIds.length > 0) {
      for (const pendingReaderId of pendingReaderIds) {
        await updateReadersOfDataResultsFn(newUserId, pendingReaderId);
      }
    }

    console.log(
      `Completed updating readers of data sessions for user [${newUserId}]`
    );
  } catch (e) {
    console.error(
      `Error updating readers of data sessions for user [${newUserId}]. ${e.message}`,
      e
    );
  }
};

const updateReadersOfDataSessionsFn = async (newUserId, pendingReaderId) => {
  // Query all data sessions for the team where readers contains pendingReaderId
  const sessionParams = {
    TableName: process.env.API_FULLSWINGFLIGHT_LMDATASESSIONTABLE_NAME,
    FilterExpression: "contains(readers, :pendingReaderId)",
    ExpressionAttributeValues: {
      ":pendingReaderId": pendingReaderId,
    },
  };
  
  // Handle pagination to get all sessions
  let allSessions = [];
  let lastEvaluatedKey = undefined;
  
  do {
    sessionParams.ExclusiveStartKey = lastEvaluatedKey;
    const sessionResult = await docClient.scan(sessionParams).promise();
    allSessions = allSessions.concat(sessionResult.Items);
    lastEvaluatedKey = sessionResult.LastEvaluatedKey;
  } while (lastEvaluatedKey !== undefined);

  console.log(
    `Found ${allSessions.length} [${allSessions.map((i) => i.id).join(
      ", "
    )}] data sessions for pending reader [${pendingReaderId}]`
  );
  if (allSessions.length > 0) {
    await updateDataSessionReadersFn(allSessions, newUserId);
  }
};

/**
 * Update readers of data results for a team
 * @param {string} newUserId - The ID of the the new user
 * @param {string} pendingReaderId - The ID of the the pending reader
 */
const updateReadersOfDataResultsFn = async (newUserId, pendingReaderId) => {
  try {
    console.log(
      `Updating readers of data results for user [${newUserId}], player.id [${pendingReaderId}]`
    );

    // Query all data results where readers contains pendingReaderId
    const resultParams = {
      TableName: process.env.API_FULLSWINGFLIGHT_LMDATARESULTSTABLE_NAME,
      FilterExpression: "contains(readers, :pendingReaderId)",
      ExpressionAttributeValues: {
        ":pendingReaderId": pendingReaderId,
      },
    };

    // Handle pagination to get all results
    let allResults = [];
    let lastEvaluatedKey = undefined;
    
    do {
      resultParams.ExclusiveStartKey = lastEvaluatedKey;
      const resultQuery = await docClient.scan(resultParams).promise();
      allResults = allResults.concat(resultQuery.Items);
      lastEvaluatedKey = resultQuery.LastEvaluatedKey;
    } while (lastEvaluatedKey !== undefined);

    console.log(
      `Found ${allResults.length} [${allResults.map((i) => i.id).join(
        ", "
      )}] data results for pending reader [${pendingReaderId}]`
    );
    if (allResults.length > 0) {
      await updateDataResultReadersFn(allResults, newUserId);
    }

    console.log(
      `Completed updating readers of data results for user [${newUserId}]`
    );
  } catch (e) {
    console.error(
      `Error updating readers of data results for user [${newUserId}]. ${e.message}`,
      e
    );
  }
};
