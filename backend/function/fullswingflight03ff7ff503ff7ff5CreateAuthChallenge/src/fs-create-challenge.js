/**
 * @type {import('@types/aws-lambda').CreateAuthChallengeTriggerHandler}
 */
exports.handler = async (event) => {
  console.log('CreateAuthChallenge event:', JSON.stringify(event, null, 2));

  if (event.request.challengeName === 'CUSTOM_CHALLENGE') {
    event.response.publicChallengeParameters = {
      challengeType: 'ID_TOKEN_VERIFICATION',
      message: 'Please provide your Auth Portal ID token'
    };
    
    event.response.privateChallengeParameters = {
      // Store any private data needed for verification
      expectedEmail: event.request.userAttributes.email
    };
  }
  
  return event;
};
