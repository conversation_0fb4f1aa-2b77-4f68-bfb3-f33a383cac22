{"trigger": true, "modules": ["custom"], "parentResource": "fullswingflight03ff7ff503ff7ff5", "functionName": "fullswingflight03ff7ff503ff7ff5PreTokenGeneration", "resourceName": "fullswingflight03ff7ff503ff7ff5PreTokenGeneration", "parentStack": "auth", "triggerEnvs": "[]", "triggerDir": "/snapshot/amplify-cli/build/node_modules/@aws-amplify/amplify-category-auth/provider-utils/awscloudformation/triggers/PreTokenGeneration", "triggerTemplate": "PreTokenGeneration.json.ejs", "triggerEventPath": "PreTokenGeneration.event.json", "roleName": "fullswingflight03ff7ff503ff7ff5PreTokenGeneration", "skipEdit": true, "environmentVariableList": [{"cloudFormationParameterName": "accessTokenGeneratorSecret", "environmentVariableName": "ACCESS_TOKEN_GENERATOR_SECRET"}]}