/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event, context) => {
  console.log("Event: ", JSON.stringify(event, null, 2));
  const clientId = event.callerContext.clientId;
 
  // check if the client is a machine client (machine client has client metadata)
  const isMachineClient = event.request.clientMetadata && Object.keys(event.request.clientMetadata).length > 0
  // check if we have a secret in the client metadata
  const isMachineClientWithSecret = isMachineClient && event.request.clientMetadata.secret === process.env.ACCESS_TOKEN_GENERATOR_SECRET
  // add custom claims to the scope
  if (isMachineClientWithSecret) {
    event.response = {
      claimsAndScopeOverrideDetails: {
        accessTokenGeneration: {
          claimsToAddOrOverride: {
            "custom:company_id": event.request.clientMetadata.companyId
          },
        }
      }
    };
  }
 
  return event;
};