const {
  CognitoIdentityProviderClient,
  AdminAddUserToGroupCommand,
  GetGroupCommand,
  CreateGroupCommand,
} = require('@aws-sdk/client-cognito-identity-provider');
const AWS = require('aws-sdk');

const cognitoIdentityServiceProvider = new CognitoIdentityProviderClient({});
const lambda = new AWS.Lambda();
const USERS_ROLE = "Users";
const COACHES_ROLE = "Coaches";
/**
 * @type {import('@types/aws-lambda').PostConfirmationTriggerHandler}
 */
exports.handler = async (event) => {
  let group = process.env.GROUP;
  const role = event.request.userAttributes["custom:role"]
  if (role) {
    if ([USERS_ROLE, COACHES_ROLE].includes(role)) {
      group = role;
    } else {
      await lambda
          .invoke({
            FunctionName: process.env.FUNCTION_UPDATEUSERCUSTOMROLE_NAME,
            InvocationType: 'Event',
            Payload: JSON.stringify({role: group, userPoolId: event.userPoolId, userName: event.userName})
          })
          .promise();
    }
  }
  const groupParams = {
    GroupName: group,
    UserPoolId: event.userPoolId,
  };
  const addUserParams = {
    GroupName: group,
    UserPoolId: event.userPoolId,
    Username: event.userName,
  };
  /**
   * Check if the group exists; if it doesn't, create it.
   */
  try {
    await cognitoIdentityServiceProvider.send(new GetGroupCommand(groupParams));
  } catch (e) {
    await cognitoIdentityServiceProvider.send(new CreateGroupCommand(groupParams));
  }
  /**
   * Then, add the user to the group.
   */
  await cognitoIdentityServiceProvider.send(new AdminAddUserToGroupCommand(addUserParams));

  return event;
};
