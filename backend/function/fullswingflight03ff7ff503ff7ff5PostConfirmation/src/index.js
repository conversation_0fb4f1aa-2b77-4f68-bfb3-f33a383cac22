/* Amplify Params - DO NOT EDIT
	API_FULLSWINGFLIGHT_GRAPHQLAP<PERSON>NDPOINTOUTPUT
	API_FULLSWINGFLIGHT_GRAPH<PERSON>APIIDOUTPUT
	ENV
	FUNCTION_ASYNCHANDLECOGNITOPOSTCONFIRM_NAME
	FUNCTION_UPDATEUSERCUSTOMROLE_NAME
	REGION
Amplify Params - DO NOT EDIT */
/**
 * @fileoverview
 *
 * This CloudFormation Trigger creates a handler which awaits the other handlers
 * specified in the `MODULES` env var, located at `./${MODULE}`.
 */
const AWS = require('aws-sdk');
const lambda = new AWS.Lambda();
/**
 * The names of modules to load are stored as a comma-delimited string in the
 * `MODULES` env var.
 */
const moduleNames = process.env.MODULES.split(',');
/**
 * The array of imported modules.
 */
const modules = moduleNames.map((name) => require(`./${name}`));

const asyncHandleCognitoPostConfirmFn = async (userId, email, fullName, inviteCode) => {
    try {
        console.log('Syncing user', userId, email, fullName, inviteCode);
        await lambda
            .invoke({
                FunctionName: process.env.FUNCTION_ASYNCHANDLECOGNITOPOSTCONFIRM_NAME,
                InvocationType: 'Event',
                Payload: JSON.stringify({userId, email, fullName, inviteCode})
            })
            .promise();
        console.log('Trigger syncing user', userId, email, fullName, 'done');
    } catch (e) {
        console.error('Sync user fn fail', e)
    }
}
/**
 * This async handler iterates over the given modules and awaits them.
 *
 * @see https://docs.aws.amazon.com/lambda/latest/dg/nodejs-handler.html#nodejs-handler-async
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 *
 */
exports.handler = async (event, context) => {
    /**
     * Instead of naively iterating over all handlers, run them concurrently with
     * `await Promise.all(...)`. This would otherwise just be determined by the
     * order of names in the `MODULES` var.
     */
    await Promise.all(modules.map((module) => module.handler(event, context)));
    const inviteCode = event.request.userAttributes["custom:invite_code"];
    const email = event.request.userAttributes["email"];
    await asyncHandleCognitoPostConfirmFn(event.userName, email, event.request.userAttributes["name"], inviteCode)
    return event;
};
