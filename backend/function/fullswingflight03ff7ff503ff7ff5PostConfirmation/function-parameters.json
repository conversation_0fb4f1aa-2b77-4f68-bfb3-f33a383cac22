{"trigger": true, "modules": ["add-to-group"], "parentResource": "fullswingflight03ff7ff503ff7ff5", "functionName": "fullswingflight03ff7ff503ff7ff5PostConfirmation", "resourceName": "fullswingflight03ff7ff503ff7ff5PostConfirmation", "parentStack": "auth", "triggerEnvs": "[]", "triggerDir": "/snapshot/amplify-cli/build/node_modules/@aws-amplify/amplify-category-auth/provider-utils/awscloudformation/triggers/PostConfirmation", "triggerTemplate": "PostConfirmation.json.ejs", "triggerEventPath": "PostConfirmation.event.json", "roleName": "fullswingflight03ff7ff503ff7ff5PostConfirmation", "skipEdit": true, "permissions": {"api": {"fullswingflight": ["Mutation"]}, "function": {"updateUserCustomRole": ["read"], "asyncHandleCognitoPostConfirm": ["read"]}}, "lambdaLayers": [{"type": "ProjectLayer", "resourceName": "fullswingflightstatsShared", "version": "Always choose latest version", "isLatestVersionSelected": true, "env": "dev"}]}