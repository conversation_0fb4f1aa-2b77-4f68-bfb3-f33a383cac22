{"api": {"fullswingflight": {"dependsOn": [{"attributes": ["UserPoolId"], "category": "auth", "resourceName": "fullswingflight03ff7ff503ff7ff5"}], "output": {"authConfig": {"additionalAuthenticationProviders": [{"authenticationType": "AWS_IAM"}, {"authenticationType": "AWS_LAMBDA", "lambdaAuthorizerConfig": {"lambdaFunction": "graphQlLambdaAuthorizer71aa6a86", "ttlSeconds": "300"}}], "defaultAuthentication": {"authenticationType": "AMAZON_COGNITO_USER_POOLS", "userPoolConfig": {"userPoolId": "authfullswingflight03ff7ff503ff7ff5"}}}}, "providerPlugin": "awscloudformation", "service": "AppSync"}}, "auth": {"fullswingflight03ff7ff503ff7ff5": {"customAuth": false, "dependsOn": [{"attributes": ["<PERSON><PERSON>", "Name"], "category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CustomMessage", "triggerProvider": "Cognito"}, {"attributes": ["<PERSON><PERSON>", "Name"], "category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5PostAuthentication", "triggerProvider": "Cognito"}, {"attributes": ["<PERSON><PERSON>", "Name"], "category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5PostConfirmation", "triggerProvider": "Cognito"}, {"attributes": ["<PERSON><PERSON>", "Name"], "category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5PreTokenGeneration", "triggerProvider": "Cognito"}, {"attributes": ["<PERSON><PERSON>", "Name"], "category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CreateAuthChallenge", "triggerProvider": "Cognito"}, {"attributes": ["<PERSON><PERSON>", "Name"], "category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5DefineAuthChallenge", "triggerProvider": "Cognito"}, {"attributes": ["<PERSON><PERSON>", "Name"], "category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse", "triggerProvider": "Cognito"}], "frontendAuthConfig": {"mfaConfiguration": "OFF", "mfaTypes": ["SMS"], "passwordProtectionSettings": {"passwordPolicyCharacters": ["REQUIRES_LOWERCASE", "REQUIRES_UPPERCASE", "REQUIRES_NUMBERS", "REQUIRES_SYMBOLS"], "passwordPolicyMinLength": 8}, "signupAttributes": ["EMAIL", "NAME"], "socialProviders": [], "usernameAttributes": ["EMAIL"], "verificationMechanisms": ["EMAIL"]}, "providerPlugin": "awscloudformation", "service": "Cognito"}, "userPoolGroups": {"dependsOn": [{"attributes": ["UserPoolId", "AppClientIDWeb", "AppClientID", "IdentityPoolId"], "category": "auth", "resourceName": "fullswingflight03ff7ff503ff7ff5"}], "providerPlugin": "awscloudformation", "service": "Cognito-UserPool-Groups"}}, "custom": {}, "function": {"acceptTeamInvite": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["UserPoolId"], "category": "auth", "resourceName": "fullswingflight03ff7ff503ff7ff5"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "addPlayerToTeam": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["UserPoolId"], "category": "auth", "resourceName": "fullswingflight03ff7ff503ff7ff5"}, {"attributes": ["Name"], "category": "function", "resourceName": "createBranchIoInviteLink"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "asyncHandleCognitoPostConfirm": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "chooseUserRole": {"build": true, "dependsOn": [{"attributes": ["UserPoolId"], "category": "auth", "resourceName": "fullswingflight03ff7ff503ff7ff5"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "clearShadow": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "clubaverages": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}, {"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "createBranchIoInviteLink": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "createLmTeamInviteLink": {"build": true, "dependsOn": [{"attributes": ["Name"], "category": "function", "resourceName": "createBranchIoInviteLink"}, {"attributes": ["GraphQLAPIIdOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "deleteLmTeamCustom": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "deleteLmTeamPlayersCustom": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "fetchShadow": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "fullswingflight03ff7ff503ff7ff5CreateAuthChallenge": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "fullswingflight03ff7ff503ff7ff5CustomMessage": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "fullswingflight03ff7ff503ff7ff5DefineAuthChallenge": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "fullswingflight03ff7ff503ff7ff5PostAuthentication": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "fullswingflight03ff7ff503ff7ff5PostConfirmation": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["Name"], "category": "function", "resourceName": "updateUserCustomRole"}, {"attributes": ["Name"], "category": "function", "resourceName": "asyncHandleCognitoPostConfirm"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "fullswingflight03ff7ff503ff7ff5PreTokenGeneration": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse": {"build": true, "providerPlugin": "awscloudformation", "service": "Lambda"}, "fullswingflightstatsShared": {"build": true, "providerPlugin": "awscloudformation", "service": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "graphQlLambdaAuthorizer71aa6a86": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["UserPoolId"], "category": "auth", "resourceName": "fullswingflight03ff7ff503ff7ff5"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "leaveTeamCustom": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "productRegistrationReport": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "removeUserAccountAndData": {"build": true, "dependsOn": [{"attributes": ["UserPoolId"], "category": "auth", "resourceName": "fullswingflight03ff7ff503ff7ff5"}, {"attributes": ["BucketName"], "category": "storage", "resourceName": "fullswingFlightStorage"}, {"attributes": ["GraphQLAPIIdOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "resultsByTeamSession": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "sessionsByTeam": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "sessionsinrange": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}, {"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "sessionstats": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "shareSession": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "sharedsessions": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "shotsbycategory": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "shotsbyclub": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "shotsinrange": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "syncLmUserCognito": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "updateFeature": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "updateLmUserCustom": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["UserPoolId"], "category": "auth", "resourceName": "fullswingflight03ff7ff503ff7ff5"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "updateUserCustomRole": {"build": true, "dependsOn": [{"attributes": ["UserPoolId"], "category": "auth", "resourceName": "fullswingflight03ff7ff503ff7ff5"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "updateUserFeatureAccess": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}, {"attributes": ["GraphQLAPIIdOutput", "GraphQLAPIEndpointOutput"], "category": "api", "resourceName": "fullswingflight"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "userExists": {"build": true, "dependsOn": [{"attributes": ["UserPoolId"], "category": "auth", "resourceName": "fullswingflight03ff7ff503ff7ff5"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "userstats": {"build": true, "dependsOn": [{"attributes": ["GraphQLAPIIdOutput"], "category": "api", "resourceName": "fullswingflight"}, {"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "writeShadow": {"build": true, "dependsOn": [{"attributes": ["<PERSON><PERSON>"], "category": "function", "resourceName": "fullswingflightstatsShared"}], "providerPlugin": "awscloudformation", "service": "Lambda"}}, "parameters": {"AMPLIFY_function_acceptTeamInvite_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "acceptTeamInvite"}]}, "AMPLIFY_function_acceptTeamInvite_s3Key": {"usedBy": [{"category": "function", "resourceName": "acceptTeamInvite"}]}, "AMPLIFY_function_addPlayerToTeam_awsSesSender": {"usedBy": [{"category": "function", "resourceName": "addPlayerToTeam"}]}, "AMPLIFY_function_addPlayerToTeam_awsSesSenderName": {"usedBy": [{"category": "function", "resourceName": "addPlayerToTeam"}]}, "AMPLIFY_function_addPlayerToTeam_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "addPlayerToTeam"}]}, "AMPLIFY_function_addPlayerToTeam_s3Key": {"usedBy": [{"category": "function", "resourceName": "addPlayerToTeam"}]}, "AMPLIFY_function_asyncHandleCognitoPostConfirm_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "asyncHandleCognitoPostConfirm"}]}, "AMPLIFY_function_asyncHandleCognitoPostConfirm_s3Key": {"usedBy": [{"category": "function", "resourceName": "asyncHandleCognitoPostConfirm"}]}, "AMPLIFY_function_chooseUserRole_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "chooseUserRole"}]}, "AMPLIFY_function_chooseUserRole_s3Key": {"usedBy": [{"category": "function", "resourceName": "chooseUserRole"}]}, "AMPLIFY_function_clearShadow_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "clearShadow"}]}, "AMPLIFY_function_clearShadow_s3Key": {"usedBy": [{"category": "function", "resourceName": "clearShadow"}]}, "AMPLIFY_function_clubaverages_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "clubaverages"}]}, "AMPLIFY_function_clubaverages_s3Key": {"usedBy": [{"category": "function", "resourceName": "clubaverages"}]}, "AMPLIFY_function_createBranchIoInviteLink_authFullswingPortalUrl": {"usedBy": [{"category": "function", "resourceName": "createBranchIoInviteLink"}]}, "AMPLIFY_function_createBranchIoInviteLink_branchIoApiHost": {"usedBy": [{"category": "function", "resourceName": "createBranchIoInviteLink"}]}, "AMPLIFY_function_createBranchIoInviteLink_branchIoKey": {"usedBy": [{"category": "function", "resourceName": "createBranchIoInviteLink"}]}, "AMPLIFY_function_createBranchIoInviteLink_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "createBranchIoInviteLink"}]}, "AMPLIFY_function_createBranchIoInviteLink_invitationDeepLinkPath": {"usedBy": [{"category": "function", "resourceName": "createBranchIoInviteLink"}]}, "AMPLIFY_function_createBranchIoInviteLink_s3Key": {"usedBy": [{"category": "function", "resourceName": "createBranchIoInviteLink"}]}, "AMPLIFY_function_createLmTeamInviteLink_authFullswingPortalUrl": {"usedBy": [{"category": "function", "resourceName": "createLmTeamInviteLink"}]}, "AMPLIFY_function_createLmTeamInviteLink_branchIoApiHost": {"usedBy": [{"category": "function", "resourceName": "createLmTeamInviteLink"}]}, "AMPLIFY_function_createLmTeamInviteLink_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "createLmTeamInviteLink"}]}, "AMPLIFY_function_createLmTeamInviteLink_invitationDeepLinkPath": {"usedBy": [{"category": "function", "resourceName": "createLmTeamInviteLink"}]}, "AMPLIFY_function_createLmTeamInviteLink_s3Key": {"usedBy": [{"category": "function", "resourceName": "createLmTeamInviteLink"}]}, "AMPLIFY_function_deleteLmTeamCustom_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "deleteLmTeamCustom"}]}, "AMPLIFY_function_deleteLmTeamCustom_s3Key": {"usedBy": [{"category": "function", "resourceName": "deleteLmTeamCustom"}]}, "AMPLIFY_function_deleteLmTeamPlayersCustom_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "deleteLmTeamPlayersCustom"}]}, "AMPLIFY_function_deleteLmTeamPlayersCustom_s3Key": {"usedBy": [{"category": "function", "resourceName": "deleteLmTeamPlayersCustom"}]}, "AMPLIFY_function_fetchShadow_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "fetchShadow"}]}, "AMPLIFY_function_fetchShadow_s3Key": {"usedBy": [{"category": "function", "resourceName": "fetchShadow"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5CreateAuthChallenge_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CreateAuthChallenge"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5CreateAuthChallenge_s3Key": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CreateAuthChallenge"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5CustomMessage_authClientId": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CustomMessage"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5CustomMessage_authUrl": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CustomMessage"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5CustomMessage_bayManagerAdminClientId": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CustomMessage"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5CustomMessage_bayManagerClientId": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CustomMessage"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5CustomMessage_bayManagerResetUrl": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CustomMessage"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5CustomMessage_bayManagerResidentialClientId": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CustomMessage"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5CustomMessage_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CustomMessage"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5CustomMessage_leaguesAdminClientId": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CustomMessage"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5CustomMessage_leaguesAdminUrl": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CustomMessage"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5CustomMessage_leaguesClientId": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CustomMessage"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5CustomMessage_leaguesUrl": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CustomMessage"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5CustomMessage_s3Key": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CustomMessage"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5CustomMessage_userPoolId": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CustomMessage"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5CustomMessage_version": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CustomMessage"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5DefineAuthChallenge_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5DefineAuthChallenge"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5DefineAuthChallenge_s3Key": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5DefineAuthChallenge"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5PostAuthentication_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5PostAuthentication"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5PostAuthentication_s3Key": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5PostAuthentication"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5PostConfirmation_GROUP": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5PostConfirmation"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5PostConfirmation_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5PostConfirmation"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5PostConfirmation_s3Key": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5PostConfirmation"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5PreTokenGeneration_accessTokenGeneratorSecret": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5PreTokenGeneration"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5PreTokenGeneration_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5PreTokenGeneration"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5PreTokenGeneration_s3Key": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5PreTokenGeneration"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse_authPortalClientId": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse_authPortalUserPoolId": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse"}]}, "AMPLIFY_function_fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse_s3Key": {"usedBy": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse"}]}, "AMPLIFY_function_fullswingflightstatsShared_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "fullswingflightstatsShared"}]}, "AMPLIFY_function_fullswingflightstatsShared_s3Key": {"usedBy": [{"category": "function", "resourceName": "fullswingflightstatsShared"}]}, "AMPLIFY_function_graphQlLambdaAuthorizer71aa6a86_authClientId": {"usedBy": [{"category": "function", "resourceName": "graphQlLambdaAuthorizer71aa6a86"}]}, "AMPLIFY_function_graphQlLambdaAuthorizer71aa6a86_authWebClientId": {"usedBy": [{"category": "function", "resourceName": "graphQlLambdaAuthorizer71aa6a86"}]}, "AMPLIFY_function_graphQlLambdaAuthorizer71aa6a86_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "graphQlLambdaAuthorizer71aa6a86"}]}, "AMPLIFY_function_graphQlLambdaAuthorizer71aa6a86_s3Key": {"usedBy": [{"category": "function", "resourceName": "graphQlLambdaAuthorizer71aa6a86"}]}, "AMPLIFY_function_leaveTeamCustom_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "leaveTeamCustom"}]}, "AMPLIFY_function_leaveTeamCustom_s3Key": {"usedBy": [{"category": "function", "resourceName": "leaveTeamCustom"}]}, "AMPLIFY_function_productRegistrationReport_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "productRegistrationReport"}]}, "AMPLIFY_function_productRegistrationReport_s3Key": {"usedBy": [{"category": "function", "resourceName": "productRegistrationReport"}]}, "AMPLIFY_function_productRegistrationReport_sendTo": {"usedBy": [{"category": "function", "resourceName": "productRegistrationReport"}]}, "AMPLIFY_function_removeUserAccountAndData_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "removeUserAccountAndData"}]}, "AMPLIFY_function_removeUserAccountAndData_s3Key": {"usedBy": [{"category": "function", "resourceName": "removeUserAccountAndData"}]}, "AMPLIFY_function_resultsByTeamSession_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "resultsByTeamSession"}]}, "AMPLIFY_function_resultsByTeamSession_s3Key": {"usedBy": [{"category": "function", "resourceName": "resultsByTeamSession"}]}, "AMPLIFY_function_sessionsByTeam_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "sessionsByTeam"}]}, "AMPLIFY_function_sessionsByTeam_s3Key": {"usedBy": [{"category": "function", "resourceName": "sessionsByTeam"}]}, "AMPLIFY_function_sessionsinrange_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "<PERSON><PERSON><PERSON><PERSON>"}]}, "AMPLIFY_function_sessionsinrange_s3Key": {"usedBy": [{"category": "function", "resourceName": "<PERSON><PERSON><PERSON><PERSON>"}]}, "AMPLIFY_function_sessionstats_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "sessionstats"}]}, "AMPLIFY_function_sessionstats_s3Key": {"usedBy": [{"category": "function", "resourceName": "sessionstats"}]}, "AMPLIFY_function_shareSession_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "shareSession"}]}, "AMPLIFY_function_shareSession_s3Key": {"usedBy": [{"category": "function", "resourceName": "shareSession"}]}, "AMPLIFY_function_sharedsessions_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "sharedsessions"}]}, "AMPLIFY_function_sharedsessions_s3Key": {"usedBy": [{"category": "function", "resourceName": "sharedsessions"}]}, "AMPLIFY_function_shotsbycategory_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "shotsbycategory"}]}, "AMPLIFY_function_shotsbycategory_s3Key": {"usedBy": [{"category": "function", "resourceName": "shotsbycategory"}]}, "AMPLIFY_function_shotsbyclub_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "shotsbyclub"}]}, "AMPLIFY_function_shotsbyclub_s3Key": {"usedBy": [{"category": "function", "resourceName": "shotsbyclub"}]}, "AMPLIFY_function_shotsinrange_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "<PERSON><PERSON><PERSON><PERSON>"}]}, "AMPLIFY_function_shotsinrange_s3Key": {"usedBy": [{"category": "function", "resourceName": "<PERSON><PERSON><PERSON><PERSON>"}]}, "AMPLIFY_function_syncLmUserCognito_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "syncLmUserCognito"}]}, "AMPLIFY_function_syncLmUserCognito_s3Key": {"usedBy": [{"category": "function", "resourceName": "syncLmUserCognito"}]}, "AMPLIFY_function_updateFeature_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "updateFeature"}]}, "AMPLIFY_function_updateFeature_s3Key": {"usedBy": [{"category": "function", "resourceName": "updateFeature"}]}, "AMPLIFY_function_updateLmUserCustom_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "updateLmUserCustom"}]}, "AMPLIFY_function_updateLmUserCustom_s3Key": {"usedBy": [{"category": "function", "resourceName": "updateLmUserCustom"}]}, "AMPLIFY_function_updateUserCustomRole_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "updateUserCustomRole"}]}, "AMPLIFY_function_updateUserCustomRole_s3Key": {"usedBy": [{"category": "function", "resourceName": "updateUserCustomRole"}]}, "AMPLIFY_function_updateUserFeatureAccess_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "updateUserFeatureAccess"}]}, "AMPLIFY_function_updateUserFeatureAccess_s3Key": {"usedBy": [{"category": "function", "resourceName": "updateUserFeatureAccess"}]}, "AMPLIFY_function_userExists_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "userExists"}]}, "AMPLIFY_function_userExists_s3Key": {"usedBy": [{"category": "function", "resourceName": "userExists"}]}, "AMPLIFY_function_userstats_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "userstats"}]}, "AMPLIFY_function_userstats_s3Key": {"usedBy": [{"category": "function", "resourceName": "userstats"}]}, "AMPLIFY_function_writeShadow_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "writeShadow"}]}, "AMPLIFY_function_writeShadow_s3Key": {"usedBy": [{"category": "function", "resourceName": "writeShadow"}]}}, "storage": {"fullswingFlightStorage": {"dependsOn": [{"attributes": ["UserPoolId"], "category": "auth", "resourceName": "fullswingflight03ff7ff503ff7ff5"}, {"attributes": ["UsersGroupRole"], "category": "auth", "resourceName": "userPoolGroups"}, {"attributes": ["CoachesGroupRole"], "category": "auth", "resourceName": "userPoolGroups"}, {"attributes": ["AdminGroupRole"], "category": "auth", "resourceName": "userPoolGroups"}], "providerPlugin": "awscloudformation", "service": "S3"}}}