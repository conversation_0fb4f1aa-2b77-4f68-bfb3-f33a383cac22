{"version": "1", "cognitoConfig": {"identityPoolName": "fullswingflight03ff7ff5_identitypool_03ff7ff5", "allowUnauthenticatedIdentities": true, "resourceNameTruncated": "fullsw03ff7ff5", "userPoolName": "fullswingflight03ff7ff5_userpool_03ff7ff5", "autoVerifiedAttributes": ["email"], "mfaConfiguration": "OFF", "mfaTypes": ["SMS Text Message"], "smsAuthenticationMessage": "Your authentication code is {####}", "smsVerificationMessage": "Your verification code is {####}", "emailVerificationSubject": "Full Swing Confirmation", "emailVerificationMessage": "Please click the link below to verify your email address. {####}", "defaultPasswordPolicy": false, "passwordPolicyMinLength": 8, "passwordPolicyCharacters": ["Requires Lowercase", "Requires Uppercase", "Requires Numbers", "Requires Symbols"], "requiredAttributes": ["email", "name"], "aliasAttributes": [], "userpoolClientGenerateSecret": true, "userpoolClientRefreshTokenValidity": 90, "userpoolClientWriteAttributes": ["email"], "userpoolClientReadAttributes": ["email"], "userpoolClientLambdaRole": "fullsw03ff7ff5_userpoolclient_lambda_role", "userpoolClientSetAttributes": false, "authSelections": "identityPoolAndUserPool", "resourceName": "fullswingflight03ff7ff503ff7ff5", "serviceName": "Cognito", "useDefault": "manual", "sharedId": "03ff7ff5", "userPoolGroupList": ["Users", "Coaches", "Admin"], "userPoolGroups": true, "usernameAttributes": ["email"], "adminQueries": false, "hostedUI": false, "triggers": {"CreateAuthChallenge": ["fs-create-challenge"], "CustomMessage": ["custom"], "DefineAuthChallenge": ["fs-define-challenge"], "PostAuthentication": ["custom"], "PostConfirmation": ["add-to-group"], "VerifyAuthChallengeResponse": ["fs-verify-auth-challenge"], "PreTokenGeneration": ["custom"]}, "authRoleArn": {"Fn::GetAtt": ["AuthRole", "<PERSON><PERSON>"]}, "unauthRoleArn": {"Fn::GetAtt": ["UnauthRole", "<PERSON><PERSON>"]}, "breakCircularDependency": true, "useEnabledMfas": false, "dependsOn": [{"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CustomMessage", "triggerProvider": "Cognito", "attributes": ["<PERSON><PERSON>", "Name"]}, {"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5PostAuthentication", "triggerProvider": "Cognito", "attributes": ["<PERSON><PERSON>", "Name"]}, {"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5PostConfirmation", "triggerProvider": "Cognito", "attributes": ["<PERSON><PERSON>", "Name"]}, {"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5PreTokenGeneration", "triggerProvider": "Cognito", "attributes": ["<PERSON><PERSON>", "Name"]}, {"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5CreateAuthChallenge", "triggerProvider": "Cognito", "attributes": ["<PERSON><PERSON>", "Name"]}, {"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5DefineAuthChallenge", "triggerProvider": "Cognito", "attributes": ["<PERSON><PERSON>", "Name"]}, {"category": "function", "resourceName": "fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse", "triggerProvider": "Cognito", "attributes": ["<PERSON><PERSON>", "Name"]}], "authProviders": [], "parentStack": {"Ref": "AWS::StackId"}, "permissions": ["{\n  \"policyName\": \"AddToGroup<PERSON>ognito\",\n  \"trigger\": \"PostConfirmation\",\n  \"effect\": \"Allow\",\n  \"actions\": [\n    \"cognito-idp:AdminAddUserToGroup\",\n    \"cognito-idp:GetGroup\",\n    \"cognito-idp:CreateGroup\"\n  ],\n  \"resource\": {\n    \"paramType\": \"!GetAtt\",\n    \"keys\": [\n      \"UserPool\",\n      \"Arn\"\n    ]\n  }\n}"], "thirdPartyAuth": true, "authTriggerConnections": "[\n  {\n    \"triggerType\": \"CreateAuthChallenge\",\n    \"lambdaFunctionName\": \"fullswingflight03ff7ff503ff7ff5CreateAuthChallenge\"\n  },\n  {\n    \"triggerType\": \"CustomMessage\",\n    \"lambdaFunctionName\": \"fullswingflight03ff7ff503ff7ff5CustomMessage\"\n  },\n  {\n    \"triggerType\": \"DefineAuthChallenge\",\n    \"lambdaFunctionName\": \"fullswingflight03ff7ff503ff7ff5DefineAuthChallenge\"\n  },\n  {\n    \"triggerType\": \"PostAuthentication\",\n    \"lambdaFunctionName\": \"fullswingflight03ff7ff503ff7ff5PostAuthentication\"\n  },\n  {\n    \"triggerType\": \"PostConfirmation\",\n    \"lambdaFunctionName\": \"fullswingflight03ff7ff503ff7ff5PostConfirmation\"\n  },\n  {\n    \"triggerType\": \"VerifyAuthChallengeResponse\",\n    \"lambdaFunctionName\": \"fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse\"\n  },\n  {\n    \"triggerType\": \"PreTokenGeneration\",\n    \"lambdaFunctionName\": \"fullswingflight03ff7ff503ff7ff5PreTokenGeneration\"\n  }\n]"}}