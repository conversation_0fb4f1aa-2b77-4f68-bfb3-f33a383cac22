import {AmplifyAuthCognitoStackTemplate, AmplifyProjectInfo} from '@aws-amplify/cli-extensibility-helper';
import * as fs from 'fs';

export function override(resources: AmplifyAuthCognitoStackTemplate, amplifyProjectInfo: AmplifyProjectInfo) {
    // console.log("\n===amplifyProjectInfo===\n")
    // console.log(amplifyProjectInfo);
    // console.log("\n===resources===\n")
    // console.log(resources);

    // Enable explicit auth flows for custom auth challenge
    // This is normally not defined, which defaults to refresh, srp, and custom
    resources.userPoolClient.explicitAuthFlows = [
        ...(resources.userPoolClient.explicitAuthFlows || []),
        "ALLOW_USER_AUTH",
        "ALLOW_REFRESH_TOKEN_AUTH",
        "ALLOW_USER_SRP_AUTH",
        "ALLOW_CUSTOM_AUTH"
    ];
    resources.userPoolClientWeb.explicitAuthFlows = [
        ...(resources.userPoolClientWeb.explicitAuthFlows || []),
        "ALLOW_USER_AUTH",
        "ALLOW_REFRESH_TOKEN_AUTH",
        "ALLOW_USER_SRP_AUTH",
        "ALLOW_CUSTOM_AUTH"
    ];

    resources.userPool.emailVerificationMessage = undefined
    resources.userPool.emailVerificationSubject = undefined
    resources.userPool.policies = {
        passwordPolicy: {
            ...resources.userPool.policies['passwordPolicy'],
        }
    }

    // Auth Tokens
    resources.userPoolClient.accessTokenValidity = 1
    resources.userPoolClient.idTokenValidity = 60
    resources.userPoolClient.refreshTokenValidity = 90
    resources.userPoolClient.tokenValidityUnits = {
        accessToken: 'days',
        idToken: 'minutes',
        refreshToken: 'days'
    }

    // Preserve verified attributes
    resources.userPool.addPropertyOverride('UserAttributeUpdateSettings', {
        AttributesRequireVerificationBeforeUpdate: ['email']
    })

    // Email verify configuration
    resources.userPool.addPropertyOverride('EmailConfiguration', {
        ConfigurationSet: 'fullswingapps_com-configuration-set',
        EmailSendingAccount: 'DEVELOPER',
        From: '<EMAIL>',
        ReplyToEmailAddress: '<EMAIL>',
        SourceArn: 'arn:aws:ses:us-east-1:************:identity/<EMAIL>'
    })
    resources.userPool.addPropertyOverride('VerificationMessageTemplate', {
        DefaultEmailOption: 'CONFIRM_WITH_LINK',
        EmailMessageByLink: 'Please click the link below to verify your email address. {##Verify Email##}',
        EmailSubjectByLink: 'Full Swing Confirmation'
    })
    
    // Cognito Lambda Config
    // WARNING: This is overridden by the Cognito auth trigger custom stack. See below for hack to prevent CLI from overwriting.  Post hook is available to ensure this is set redundantly.
    // Modifying the authTrigger custom CloudFormation lambdas can allow us to configure the lambda triggers here.  Though it is not clear what the best approach is to ensure that lambda is set correctly.
    const tokenLambdaArnRoot = 'arn:aws:lambda:us-east-1:************:function:fullswingflight03ff7ff503ff7ff5';
    const customMessagArn = `${tokenLambdaArnRoot}CustomMessage-${amplifyProjectInfo.envName}`;
    const postAuthArn = `${tokenLambdaArnRoot}PostAuthentication-${amplifyProjectInfo.envName}`;
    const postConfirmationArn = `${tokenLambdaArnRoot}PostConfirmation-${amplifyProjectInfo.envName}`;
    const preTokenGenArn = `${tokenLambdaArnRoot}PreTokenGeneration-${amplifyProjectInfo.envName}`;
    resources.userPool.addPropertyOverride('LambdaConfig.CustomMessage', customMessagArn);
    resources.userPool.addPropertyOverride('LambdaConfig.PostAuthentication', postAuthArn);
    resources.userPool.addPropertyOverride('LambdaConfig.PostConfirmation', postConfirmationArn);
    resources.userPool.addPropertyOverride('LambdaConfig.PreTokenGeneration', preTokenGenArn);
    resources.userPool.addPropertyOverride('LambdaConfig.PreTokenGenerationConfig', {
        LambdaArn: preTokenGenArn,
        LambdaVersion: 'V3_0',
    });

    // Custom Auth Challenge Lambda ARNs
    const defineAuthChallengeArn = `${tokenLambdaArnRoot}DefineAuthChallenge-${amplifyProjectInfo.envName}`;
    const createAuthChallengeArn = `${tokenLambdaArnRoot}CreateAuthChallenge-${amplifyProjectInfo.envName}`;
    const verifyAuthChallengeArn = `${tokenLambdaArnRoot}VerifyAuthChallengeResponse-${amplifyProjectInfo.envName}`;

    // Custom Auth Challenge Lambda Config
    resources.userPool.addPropertyOverride(
      "LambdaConfig.DefineAuthChallenge",
      defineAuthChallengeArn
    );
    resources.userPool.addPropertyOverride(
      "LambdaConfig.CreateAuthChallenge",
      createAuthChallengeArn
    );
    resources.userPool.addPropertyOverride(
      "LambdaConfig.VerifyAuthChallengeResponse",
      verifyAuthChallengeArn
    );

    // Clear auth trigger lambda config and modify deployed CF lambda to not rewite UserPool configuration
    // This modifies the custom CloudFormation lambda related to the auth triggers.  The lambda itself only hijacks itself to write the lambda config we set above.  I can't find any other merit to it. - Chad Godsey
    // WARNING: Overwriting Amplify CLI provided lambda.  Be sure to keep this updated if any changes are made to the CLI.
    const authTriggerContents = fs.readFileSync('amplify/backend/auth/fullswingflight03ff7ff503ff7ff5/build/auth-trigger-cloudformation-template.json','utf8');
    let authTriggerConf = JSON.parse(authTriggerContents);
    // authTriggerConf.Resources.CustomAuthTriggerResource.Properties.lambdaConfig = []; // 12/08/25: Comment below line out due to enable custom auth. - Luong Le
    authTriggerConf.Resources.authTriggerFn7FCFA449.Properties.Code.ZipFile = "const response = require('cfn-response');\nconst {\n  CognitoIdentityProviderClient,\n  DescribeUserPoolCommand,\n  UpdateUserPoolCommand,\n} = require('@aws-sdk/client-cognito-identity-provider');\n\nexports.handler = (event, context) => {\n  // Don't return promise, response.send() marks context as done internally\n  void tryHandleEvent(event, context);\n};\n\nasync function tryHandleEvent(event, context) {\n  const physicalResourceId =\n    event.RequestType === 'Update' ? event.PhysicalResourceId : `${event.LogicalResourceId}-${event.ResourceProperties.userpoolId}`;\n  try {\n    await handleEvent(event);\n    response.send(event, context, response.SUCCESS, {}, physicalResourceId);\n  } catch (err) {\n    console.log(err.stack);\n    response.send(event, context, response.FAILED, { err }, physicalResourceId);\n  }\n}\n\nasync function handleEvent(event) {\n  const userPoolId = event.ResourceProperties.userpoolId;\n  const { lambdaConfig } = event.ResourceProperties;\n  const config = {};\n  const cognitoClient = new CognitoIdentityProviderClient({});\n  const userPoolConfig = await cognitoClient.send(new DescribeUserPoolCommand({ UserPoolId: userPoolId }));\n  const userPoolParams = userPoolConfig.UserPool;\n  // update userPool params\n\n  const updateUserPoolConfig = {\n    UserPoolId: userPoolParams.Id,\n    Policies: userPoolParams.Policies,\n    SmsVerificationMessage: userPoolParams.SmsVerificationMessage,\n    AccountRecoverySetting: userPoolParams.AccountRecoverySetting,\n    AdminCreateUserConfig: userPoolParams.AdminCreateUserConfig,\n    AutoVerifiedAttributes: userPoolParams.AutoVerifiedAttributes,\n    EmailConfiguration: userPoolParams.EmailConfiguration,\n    EmailVerificationMessage: userPoolParams.EmailVerificationMessage,\n    EmailVerificationSubject: userPoolParams.EmailVerificationSubject,\n    VerificationMessageTemplate: userPoolParams.VerificationMessageTemplate,\n    SmsAuthenticationMessage: userPoolParams.SmsAuthenticationMessage,\n    MfaConfiguration: userPoolParams.MfaConfiguration,\n    DeviceConfiguration: userPoolParams.DeviceConfiguration,\n    SmsConfiguration: userPoolParams.SmsConfiguration,\n    UserPoolTags: userPoolParams.UserPoolTags,\n    UserPoolAddOns: userPoolParams.UserPoolAddOns,\n  };\n\n  // removing undefined keys\n  Object.keys(updateUserPoolConfig).forEach((key) => updateUserPoolConfig[key] === undefined && delete updateUserPoolConfig[key]);\n\n  /* removing UnusedAccountValidityDays as deprecated\n    InvalidParameterException: Please use TemporaryPasswordValidityDays in PasswordPolicy instead of UnusedAccountValidityDays\n    */\n  if (updateUserPoolConfig.AdminCreateUserConfig && updateUserPoolConfig.AdminCreateUserConfig.UnusedAccountValidityDays) {\n    delete updateUserPoolConfig.AdminCreateUserConfig.UnusedAccountValidityDays;\n  }\n  lambdaConfig.forEach((lambda) => (config[`${lambda.triggerType}`] = lambda.lambdaFunctionArn));\n  if (event.RequestType === 'Delete') {\n    updateUserPoolConfig.LambdaConfig = {};\n    console.log(`${event.RequestType}:`, JSON.stringify(updateUserPoolConfig));\n    const result = await cognitoClient.send(new UpdateUserPoolCommand(updateUserPoolConfig));\n    console.log(`delete response data ${JSON.stringify(result)}`);\n  } else if (event.RequestType === 'Update' || event.RequestType === 'Create') {\n   // updateUserPoolConfig.LambdaConfig = config;\n   // const result = await cognitoClient.send(new UpdateUserPoolCommand(updateUserPoolConfig));\n   // console.log(`createOrUpdate response data ${JSON.stringify(result)}`);\n  }\n}\n";
    let authTriggerJson = JSON.stringify(authTriggerConf, null, 2);
    fs.writeFileSync('amplify/backend/auth/fullswingflight03ff7ff503ff7ff5/build/auth-trigger-cloudformation-template.json', authTriggerJson, 'utf8');

    // Custom Auth Attributes
    const customAttributes = [{
        attributeDataType: 'String',
        developerOnlyAttribute: false,
        mutable: true,
        name: 'role',
        required: false,
    }, {
        attributeDataType: 'String',
        developerOnlyAttribute: false,
        mutable: true,
        name: 'invite_code',
        required: false,
    }]
    resources.userPool.schema = [
        ...(resources.userPool.schema as any[]),
        ...customAttributes,
    ]
}
