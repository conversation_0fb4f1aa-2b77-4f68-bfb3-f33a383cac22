"""
Sessions for a user are found by searching for sessions for the userId
"""
type LmSession @model
  @auth(rules: [
    { allow: owner, identityClaim: "username" },
    { allow: groups, groups: ["Admin"] }]) {
  id: ID! @primaryKey
  lmProfileId: ID!
    @index(name: "byProfile", queryField: "sessionsByProfile")
  owner: String
    @index(name: "byUser", queryField: "sessionsByUser")
    @index(name: "byUserInRange", sortKeyFields: ["startTimestamp"], queryField: "sessionsByUserInRange")
  deviceID: String

  startTimestamp: Int!
  endTimestamp: Int!
  duration: Int

  name: String
  city: String
  state: String
  country: String
  address: String
  courseName: String
  elevation: Float
  latitude: Float
  longitude: Float
  temperature: Float
  humidity: Float
  location: LmLocation

  normalizedElevation: Float
  normalizedTemperature: Float
  normalizedBallType: LmBallType
  # Normalization choice for combine sessions
  normalized: Boolean

  sessionQuality: Float
  drillTargets: [LmDrillTarget]

  shots: [LmShot] @hasMany(indexName: "bySession", fields: ["id"])
  profile: LmProfile @belongsTo(fields: ["lmProfileId"])
}

"""
If a value is missing from the shot model it means it could not be calculated
and should not be considered as valid data

Additional notes on fields:
  impactKey - Key to retrieve pre-impact photo
  videoKey - Key to retreive shot video
  pointCloudKey - Key to retreive point cloud file
"""
type LmShot @model
  @auth(rules: [
    { allow: owner, identityClaim: "username" },
    { allow: groups, groups: ["Admin"] }]) {
  id: ID! @primaryKey
  lmSessionId: ID!
    @index(name: "bySession", queryField: "shotsBySession")
    @index(name: "bySessionInRange", sortKeyFields: ["timestamp"], queryField: "shotsBySessionInRange")
  lmDrillTargetId: ID
    @index(name: "byDrillTarget", queryField: "shotsByDrillTarget")
    @index(name: "byDrillTargetInRange", sortKeyFields: ["timestamp"], queryField: "shotsByDrillTargetInRange")
  clubId: ID!
    @index(name: "byClub", queryField: "shotsByClub")
    @index(name: "byClubInRange", sortKeyFields: ["timestamp"], queryField: "shotsByClubInRange")
  owner: String
    @index(name: "byUser", queryField: "shotsByUser")
    @index(name: "byUserInRange", sortKeyFields: ["timestamp"], queryField: "shotsByUserInRange")

  clubCategory: String
    @index(name: "byClubCategoryInRange", sortKeyFields: ["timestamp"], queryField: "shotsByClubCategoryInRange")
  pointId: String

  timestamp: Int!
  isFavorite: Boolean

  clubSpeed: Float
  ballSpeed: Float
  smashFactor: Float
  attackAngle: Float
  clubPath: Float
  launchAngle: Float
  horizontalLaunchAngle: Float
  faceAngle: Float
  spinRate: Float
  spinAxis: Float
  carryDistance: Float
  totalDistance: Float
  side: Float
  sideTotal: Float
  apex: Float
  ballDirection: Float
  ballCurve: Float
  descentAngle: Float
  dynamicLoft: Float

  shotQuality: Float
  targetDistance: Float
  distanceToPin: Float

  impactKey: String
  videoKey: String
  pointCloudKey: String
  protobufKey: String

  clubSpeedValid: Boolean
  ballSpeedValid: Boolean
  smashFactorValid: Boolean
  attackAngleValid: Boolean
  clubPathValid: Boolean
  launchAngleValid: Boolean
  horizontalLaunchAngleValid: Boolean
  faceAngleValid: Boolean
  spinRateValid: Boolean
  spinAxisValid: Boolean
  carryDistanceValid: Boolean
  totalDistanceValid: Boolean
  sideValid: Boolean
  sideTotalValid: Boolean
  apexValid: Boolean
  descentAngleValid: Boolean
  dynamicLoftValid: Boolean

  xFit: [Float]
  yFit: [Float]
  zFit: [Float]
  club: LmClub @hasOne(fields: ["clubId"])
  normalizedValues: LmShotNormalizedValues
}
type LmShotNormalizedValues {
  carryDistance: Float
  totalDistance: Float
  side: Float
  sideTotal: Float
  apex: Float
  distanceToPin: Float
}

"""
LM Club
"""
type LmClub @model
  @auth(rules: [
    { allow: owner, identityClaim: "username" },
    { allow: groups, groups: ["Admin"] }]) {
  id: ID! @primaryKey
  lmProfileId: ID!
    @index(name: "byProfile", queryField: "clubsByProfile")

  owner: String @auth(rules: [{ allow: owner, identityClaim: "username" }, { allow: groups, groups: ["Admin"] }])
    @index(name: "byUser", queryField: "clubsByOwner")

  type: LmClubType
  name: String
  brand: String
  model: String
  shaft: String
  color: String
  listOrder: Int @auth(rules: [{ allow: owner, identityClaim: "username" }, { allow: groups, groups: ["Admin"] }])
  isActive: Boolean @auth(rules: [{ allow: owner, identityClaim: "username" }, { allow: groups, groups: ["Admin"] }])
}

enum LmClubType {
  Unknown
  Driver
  Wood2
  Wood3
  Wood4
  Wood5
  Wood6
  Wood7
  Wood8
  Wood9
  Iron1
  Iron2
  Iron3
  Iron4
  Iron5
  Iron6
  Iron7
  Iron8
  Iron9
  PitchingWedge
  SandWedge
  LobWedge
  ApproachWedge
  GapWedge
  Wedge46
  Wedge48
  Wedge50
  Wedge52
  Wedge54
  Wedge56
  Wedge58
  Wedge60
  Wedge62
  Wedge64
  Putter
  Hybrid
  Hybrid1
  Hybrid2
  Hybrid3
  Hybrid4
  Hybrid5
  Hybrid6
  Hybrid7
  Hybrid8
  Hybrid9
  Other
}

enum LmClubCategory {
  All
  Drivers
  Woods
  Hybrids
  Irons
  Wedges
}

enum LmShotType {
  OffTee
  Approach
  AroundGreen
  Putt
}

enum LmShotShapeType {
  Straight
  Fade
  Draw
}

enum LmShotTrajectoryType {
  High
  Normal
  Low
}

"""
Drill Set
Multiple drill types can be defined.
A Drill is either a template or an activity.  Similar to workout tracking apps.
Users, Coaches, FS, others can define a Drill, then a user can select from available drills to run during a session.
"""
enum LmDrillTargetType {
    Distance
    ShotShaping
    UpDown
}

# Should probably bring back LmDrillTemplate
# No owner, full access
type LmDrillTemplate @model
  @auth(rules: [
    { allow: private, operations: [read] },
    { allow: public, operations: [get], provider: iam },
    { allow: groups, groups: ["Admin"] }]) {
  id: ID! @primaryKey

  # Query by type (only distance type for now)
  type: LmDrillTargetType
    @index(name: "byType", queryField: "drillTemplatesByType")

  name: String
    @index(name: "byName", queryField: "drillTemplatesByName")
    @index(name: "byNameAndOwner", sortKeyFields: ["ownerName"], queryField: "drillTemplatesByNameAndOwner")

  ownerName: String
  logoUrl: String
  targets: [LmDrillTarget]
}

# Owner has full access
# Signed in users have read (get, list, etc)
# Public (web, no login) have get only
# Admin have full
type LmDrill @model
  @auth(rules: [
    { allow: owner, identityClaim: "username" },
    { allow: groups, groups: ["Admin"] }]) {
  id: ID! @primaryKey
  owner: String @auth(rules: [
        { allow: owner, identityClaim: "username" },
        { allow: groups, groups: ["Admin"] }])
    @index(name: "byUser", queryField: "drillsByUser")

  # Drills belong to profiles
  lmProfileId: ID
    @index(name: "byProfile", queryField: "drillsByProfile")
  # Store original template so user can "reset"
  templateId: ID
    @index(name: "byTemplate", queryField: "drillsByTempate")

  # Query by type (only distance type for now)
  type: LmDrillTargetType
    @index(name: "byType", queryField: "drillsByType")

  # Common data
  name: String
  logoUrl: String
  targets: [LmDrillTarget]

  # Model relations
  profile: LmProfile @belongsTo(fields: ["lmProfileId"])
}

type LmDrillTarget {
  id: ID
  type: LmDrillTargetType

  targetQuality: Float

  # Common target types
  shotCount: Int
  shotType: LmShotType
  clubCategory: LmClubCategory

  # Optional sub type data
  distance: LmDistanceTarget
  shape: LmShotShapeTarget
  upDown: LmUpDownTarget
}

type LmDistanceRange {
  min: Float
  max: Float
}

type LmDistanceTarget {
  maxDistance: Boolean
  distance: Float
  range: LmDistanceRange
  fairwayWidth: Float
  targetWidth: Float
}

type LmShotShapeTarget {
  shape: LmShotShapeType
  trajectory: LmShotTrajectoryType
}

type LmUpDownTarget {
  numBalls: Int
  swingCount: Int
}

"""
Stats models and types
"""
type LmSessionStats @model
  @auth(rules: [
    { allow: owner, identityClaim: "username" },
    { allow: groups, groups: ["Admin"] }]) {
  id: ID! @primaryKey
  lmSessionId: ID! @index(name: "bySession", queryField: "statsBySession")
  owner: String @index(name: "byUser", queryField: "sessionStatsByOwner")

  shotCount: Int
  values: [ShotValues]
  averages: [ShotAverage]
}

type ShotValues {
  clubId: ID!
  clubSpeed: [Float]
  ballSpeed: [Float]
  smashFactor: [Float]
  attackAngle: [Float]
  clubPath: [Float]
  launchAngle: [Float]
  horizontalLaunchAngle: [Float]
  faceAngle: [Float]
  spinRate: [Float]
  spinAxis: [Float]
  carryDistance: [Float]
  totalDistance: [Float]
  side: [Float]
  sideTotal: [Float]
  apex: [Float]
  ballDirection: [Float]
  ballCurve: [Float]
  descentAngle: [Float]
  dynamicLoft: [Float]
  shotQuality: [Float]

  normalizedCarryDistance: [Float]
  normalizedTotalDistance: [Float]
  normalizedSide: [Float]
  normalizedSideTotal: [Float]
  normalizedApex: [Float]
}

type ShotAverage {
  clubId: ID!
  clubSpeed: Float
  ballSpeed: Float
  smashFactor: Float
  attackAngle: Float
  clubPath: Float
  launchAngle: Float
  horizontalLaunchAngle: Float
  faceAngle: Float
  spinRate: Float
  spinAxis: Float
  carryDistance: Float
  totalDistance: Float
  side: Float
  sideTotal: Float
  apex: Float
  ballDirection: Float
  ballCurve: Float
  descentAngle: Float
  dynamicLoft: Float

  normalizedCarryDistance: Float
  normalizedTotalDistance: Float
  normalizedSide: Float
  normalizedSideTotal: Float
  normalizedApex: Float
}
