"""
Deprecated models go here.
We aren't planning on removing these from the backend, but they are not used in any local Datastore
"""

type LmUserStats @model
  @auth(rules: [
    { allow: private },
    { allow: owner, identityClaim: "username" },
    { allow: groups, groups: ["Admin"] }]) {
  id: ID! @primaryKey
  lmProfileId: ID! @index(name: "byProfile", queryField: "statsByProfile")
  owner: String @index(name: "byUser", queryField: "statsByOwner")

  sessionCount: Int
  shotCount: Int
  clubCount: Int
}
