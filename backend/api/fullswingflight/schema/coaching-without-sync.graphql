
enum LmInviteCodeType {
    ShareInvitation
    AddPlayerInvitation
    AddPlayerInvitationWithoutTeam
}

type LmTeamInviteCodes @model
@auth(rules: [
    { allow: owner, identityClaim: "username", operations: [update, delete, read] },
    { allow: private, provider: iam, operations: [create] },
    { allow: groups, groups: ["Admin"] }]) {
    id: ID! @primaryKey
    link: String
    owner: String
    sport: LmSportType
    type: LmInviteCodeType!
    email: String
    lmPlayerName: String
    lmTeamId: ID
    @index(name: "byTeam", queryField: "teamInviteCodesByTeam")
    expired_at: AWSDateTime
}
