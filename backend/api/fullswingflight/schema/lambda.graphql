"""
Lambda queries and types
"""
type RangeCount {
  range: Int!
  count: Int!
}

type TimeWindowCount {
  start: Int!
  end: Int!
  count: Int!
}

type ClubCount {
  clubId: ID!
  count: Int!
}

type ClubAverage {
  clubId: ID!
  average: Float!
}

enum DateResolution {
  Day
  Week
  Month
}

type RegisterShadow {
  regUser: String!
  prodRegDate: String!
}

type GraphQLResult {
    status: String!
}

type CustomError {
    errorType: String
    message: String
}

type GraphQLResultAuth @aws_cognito_user_pools{
    status: String!
    errors: [CustomError]
}

type GraphQLDeleteResultAuth @aws_cognito_user_pools{
    status: String!
    errors: [CustomError]
    id: ID
}

type LmDataSessionListResult {
    nextToken: String
    items: [LmDataSession]
}
type LmDataResultsListResult {
    nextToken: String
    items: [LmDataResults]
}

input LmUserInfoUpdateInput {
    firstName: String
    lastName: String
    birthdate: Float
    phone: String
    role: LmUserRole
    competitiveLevel: CompetitiveLevel
    teamName: String
    organizationSchool: String
    baseballPlayerPosition: String
    handedness: LmHandedness
}

type Query {
  # unused
  accuracy(owner: String, clubCategory: String, start: Int, end: Int): [RangeCount] @function(name: "shotsbycategory-${env}")
  countByClub(owner: String, start: Int, end: Int): [ClubCount] @function(name: "shotsbyclub-${env}")
  sessionCount(owner: String, resolution: DateResolution, start: Int, end: Int): [TimeWindowCount] @function(name: "sessionsinrange-${env}")
  shotCount(owner: String, resolution: DateResolution, start: Int, end: Int): [TimeWindowCount] @function(name: "shotsinrange-${env}")
  averageByClub(owner: String, value: ValueType, start: Int, end: Int): [ClubAverage] @function(name: "clubaverages-${env}")
  # Product Registration
  getShadow(deviceId: String): RegisterShadow @function(name: "fetchShadow-${env}")
  # User sign up/change email
  userExists(email: String): GraphQLResult @function(name: "userExists-${env}")
  # Teams
  sessionsByTeam(teamId: String, nextToken: String): LmDataSessionListResult @function(name: "sessionsByTeam-${env}")
  resultsByTeamSession(sessionId: String, nextToken: String): LmDataResultsListResult @function(name: "resultsByTeamSession-${env}")
}

type Mutation {
  # Share session
  shareSession(sessionId: String): LmShareSession @function(name: "shareSession-${env}")
  # Device Registration
  writeShadow(deviceId: String, regUser: String): RegisterShadow @function(name: "writeShadow-${env}")
  clearShadow(deviceId: String, regUser: String): RegisterShadow @function(name: "clearShadow-${env}")
    @auth(rules: [{ allow: groups, groups: ["Admin"] }])
  # User data
  clearUserAccount(userId: String): GraphQLResult @function(name: "removeUserAccountAndData-${env}")
  # App Feature
  updateFeature(featureId: String, enabled: Boolean): LmFeature @function(name: "updateFeature-${env}")
  #
  chooseUserRole(role: LmUserRole!): GraphQLResultAuth
    @auth(rules: [{ allow: private, provider: userPools }])
    @function(name: "chooseUserRole-${env}")
  createLmTeamInviteLink(lmTeamId: String!): LmTeamInviteLinkResult  #@Baseball
    @auth(rules: [{ allow: private, provider: userPools }])  #@Baseball
    @function(name: "createLmTeamInviteLink-${env}")  #@Baseball
  acceptTeamInvite(inviteCode: String!): GraphQLResultAuth  #@Baseball
    @auth(rules: [{ allow: private, provider: userPools }])  #@Baseball
    @function(name: "acceptTeamInvite-${env}")  #@Baseball
  addPlayerToTeam(lmTeamId: String, email: String, userId: String, fullName: String, playerType: LmTeamPlayerType): LmTeamPlayerAddResult  #@Baseball
    @auth(rules: [{ allow: private, provider: userPools }])  #@Baseball
    @function(name: "addPlayerToTeam-${env}")  #@Baseball
  updateLmUserCustom(input: LmUserInfoUpdateInput!): GraphQLResultAuth
    @auth(rules: [{ allow: private, provider: userPools }])
    @function(name: "updateLmUserCustom-${env}")
  deleteLmTeamPlayersCustom(lmTeamPlayerId: ID!): LmTeamPlayerDeleteResult  #@Baseball
    @auth(rules: [{ allow: private, provider: userPools }])  #@Baseball
    @function(name: "deleteLmTeamPlayersCustom-${env}")  #@Baseball
  deleteLmTeamCustom(lmTeamId: ID!): LmTeamDeleteResult  #@Baseball
    @auth(rules: [{ allow: private, provider: userPools }])  #@Baseball
    @function(name: "deleteLmTeamCustom-${env}")  #@Baseball
  leaveTeamCustom(lmTeamPlayerId: ID!): GraphQLDeleteResultAuth  #@Baseball
    @auth(rules: [{ allow: private, provider: userPools }])  #@Baseball
    @function(name: "leaveTeamCustom-${env}")  #@Baseball
}
