"""
Coaching
Custom auth doesn't work with native iOS
Intead, add functions to query LmDataSession by team, and LmDataResults by team session.
"""
type LmTeamPlayers @model
@auth(rules: [
    { allow: owner, ownerField: "readers", operations: [read] },
    { allow: owner, identityClaim: "username" },
    { allow: private, provider: iam, operations: [create, update, read, delete] },
    #    { allow: private, operations: [read] },
    { allow: groups, groups: ["Admin"] }]) {
  id: ID! @primaryKey
  owner: String
    @index(name: "byOwner", queryField: "teamPlayersByUser")
  email: String
    @index(name: "byEmail", queryField: "teamPlayersByEmail")
  lmTeamId: ID
    @index(name: "byTeam", queryField: "teamPlayersByTeam")
    @index(name: "byTeamAndJoinedAt", queryField: "teamPlayersByTeamAndJoinedAt", sortKeyFields: ["joined_at"])
  lmPlayerId: ID
    @index(name: "byPlayer", queryField: "teamPlayersByPlayer")

  readers: [String]
  type: LmTeamPlayerType
  status: LmTeamPlayerStatus
  joined_at: AWSDateTime
  lmPlayerName: String

  lineupOrderBatting: Int
  lineupOrderPitching: Int

  team: LmTeam
    @belongsTo(fields: ["lmTeamId"])
}

type LmTeamCoaches @model
@auth(rules: [
    { allow: owner, identityClaim: "username" },
    { allow: private, operations: [read] },
    { allow: groups, groups: ["Admin"] }]) {
  id: ID! @primaryKey
  owner: String @auth(rules: [
      { allow: owner, identityClaim: "username" },
      { allow: private, operations: [read] },
      { allow: groups, groups: ["Admin"] }])
    @index(name: "byOwner", queryField: "teamCoachesByUser")

  lmTeamId: ID
    @index(name: "byTeam", queryField: "teamCoachesByTeam")
  lmCoachId: ID
    @index(name: "byCoach", queryField: "teamCoachesByCoach")
}

"""
Note: This model uses an overridden pipeline function to add the owner data to the model,
located at resolvers/Mutation.createLmTeam.auth.1.req.vtl.
"""
type LmTeam @model
@auth(rules: [
    { allow: owner, ownerField: "readers", operations: [read] },
    { allow: owner, identityClaim: "username", operations: [read, update] },
    { allow: private, provider: iam, operations: [delete, update] },
    { allow: private, operations: [get] },
    { allow: groups, groups: ["Coaches"], operations: [create] },
    { allow: groups, groups: ["Admin"] }]) {
  id: ID! @primaryKey
  owner: String
    @index(name: "byOwner", queryField: "teamsByOwner")
  readers: [String]

  name: String
  profileImage: String
  pitcherId: ID

  players: [LmTeamPlayers] @hasMany(indexName: "byTeam", fields: ["id"])
  coaches: [LmTeamCoaches] @hasMany(indexName: "byTeam", fields: ["id"])
  dataSessions: [LmDataSession] @hasMany(indexName: "byTeam", fields: ["id"])
}

enum LmCoachPlayersTeamAssigned {
    Y,N
}

enum LmTeamPlayerStatus {
    Pending
    Accepted
}

type LmCoachPlayers @model
@auth(rules: [
    { allow: owner, identityClaim: "username", operations: [update, delete, read] },
    { allow: private, provider: iam, operations: [create, update, delete] },
    { allow: groups, groups: ["Admin"] }]) {
  id: ID! @primaryKey
  owner: String
  lmCoachId: ID!
    @index(name: "byCoach", queryField: "coachPlayersByCoach")
  lmPlayerId: ID
  lmPlayerEmail: String
    @index(name: "byLmPlayerEmail", queryField: "coachPlayersLmPlayerEmail")
  teamAssigned: LmCoachPlayersTeamAssigned
    @index(name: "byTeamAssigned", queryField: "coachPlayersByTeamAssigned")

  lmPlayerName: String
  status: LmTeamPlayerStatus
}

type LmTeamInviteLinkResult @aws_cognito_user_pools {
    status: String!
    link: String
    errors: [CustomError]
}

type LmTeamPlayerDeleteResult @aws_cognito_user_pools {
    status: String!
    teamAssigned: LmCoachPlayersTeamAssigned
    errors: [CustomError]
}

type LmTeamPlayerAddResult @aws_cognito_user_pools{
    status: String!
    isExist: Boolean
    teamPlayer: LmTeamPlayers
    coachPlayer: LmCoachPlayers
    errors: [CustomError]
}

type LmTeamDeleteResult @aws_cognito_user_pools {
    status: String!
    teamDeleted: LmTeam
    teamPlayersDeleted: [LmTeamPlayers]
    coachPlayersNotAssigned: [LmCoachPlayers]
    errors: [CustomError]
}
