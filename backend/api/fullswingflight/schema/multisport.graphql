"""
LM Types
"""

enum LmSportType {
  Golf
  Baseball
}

type LmDataResults @model
  @auth(rules: [
    { allow: owner, ownerField: "readers", operations: [read] },
    { allow: owner, identityClaim: "username" },
    { allow: groups, groups: ["Admin"] }]) {
  id: ID! @primaryKey

  lmSessionId: ID!
    @index(name: "bySession", queryField: "dataResultsBySession")
    @index(name: "bySessionInRange", sortKeyFields: ["timestamp"], queryField: "dataResultsBySessionInRange")
  owner: String
    @index(name: "byUser", queryField: "dataResultsByUser")
    @index(name: "byUserInRange", sortKeyFields: ["timestamp"], queryField: "dataResultsByUserInRange")
  sport: LmSportType
    @index(name: "bySport", queryField: "dataResultsBySport")
  readers: [String]

  timestamp: Int!
  isFavorite: Boolean

  resultId: String
  sequenceNumber: Int

  # Sport specific data
  baseballResults: LmBaseballResults
  # golfResults: LmGolfResults

  videoKey: String
  pointCloudKey: String
}

type LmDataSession @model
  @auth(rules: [
    { allow: owner, ownerField: "readers", operations: [read] },
    { allow: owner, identityClaim: "username" },
    { allow: groups, groups: ["Admin"] }]) {
  id: ID! @primaryKey
  deviceID: String

  owner: String
    @index(name: "byUser", queryField: "sportSessionsByUser")
    @index(name: "byUserInRange", sortKeyFields: ["startTimestamp"], queryField: "dataSessionsByUserInRange")
  lmProfileId: ID!
    @index(name: "byProfile", queryField: "dataSessionsByProfile")
  lmTeamId: ID
    @index(name: "byTeam", queryField: "dataSessionsByTeam")
  sport: LmSportType
    @index(name: "bySport", queryField: "dataSessionBySport")
  readers: [String]

  startTimestamp: Int!
  endTimestamp: Int!
  duration: Int
  name: String
  details: String

  city: String
  state: String
  country: String
  address: String
  locationName: String
  elevation: Float
  latitude: Float
  longitude: Float

  temperature: Float
  humidity: Float

  location: LmLocation
  playMode: LmDataSessionPlayMode

  results: [LmDataResults] @hasMany(indexName: "bySession", fields: ["id"])
  # profile: LmProfile @belongsTo(fields: ["lmProfileId"])
  team: LmTeam @belongsTo(fields: ["lmTeamId"])
}
