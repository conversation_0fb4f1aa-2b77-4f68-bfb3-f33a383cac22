"""
LM Types
"""

type BaseballBatDataPoints {
  hitDirectionDeg: Float
  hitDistanceMeters: Float
  hitApexMeters: Float
  hitExitVelocityMps: Float
  hitLaunchAngleDeg: Float

  hitSpinTotalRpm: Float
  hitSpinActiveRpm: Float
  hitSpinBackRpm: Float
  hitSpinSideRpm: Float
  hitSpinTopRpm: Float
  hitSpinDirectionClockBearing: LmClockBearing
  hitSpinDirectionEfficiencyPct: Float

  hitBallContactPresence: Boolean
  hitBallContactTimestamp: LmTimestamp
  hitBallContactWorld3dPositionMeters: Lm3dPosition
  hitBallContactRadar3dPositionMetersDeg: LmSphericalPoint3d

  batSpeedMps: Float
  apex: Float
  batRotationalAccelerationGs: Float
  batAttackAngleDeg: Float
  batEarlyConnectionDeg: Float
  batConnectionAtImpactDeg: Float
  batVerticalAngleDeg: Float
  batSwingStartPresence: Boolean
  batSwingStartTimestamp: LmTimestamp
  batPeakHandSpeedMps: Float

  smashFactor: Float
  potentialSmashFactor: Float
  potentialExitVelocityMps: Float
  squaredUpPct: Float

  teedBallLocationWorld3dPositionMeters: Lm3dPosition

  trajectory: LmTrajectory
}

type BaseballPitchDataPoints {
  pitchReleaseTimestamp: LmTimestamp
  pitchReleaseWorld3dPositionMeters: Lm3dPosition
  pitchReleaseRadar3dPositionMetersDeg: LmSphericalPoint3d
  pitchReleaseVelocityMps: Float
  pitchReleaseArmSlotDeg: Float
  pitchReleaseHorizontalAngleDeg: Float
  pitchReleaseVerticalAngleDeg: Float
  pitchReleaseBackwardExtensionMeters: Float

  pitchBreakHorizontalMeters: Float
  pitchBreakVerticalMeters: Float
  pitchBreakInducedVerticalMeters: Float

  pitchSpinTotalRpm: Float
  pitchSpinActiveRpm: Float
  pitchSpinBackRpm: Float
  pitchSpinSideRpm: Float
  pitchSpinTopRpm: Float
  pitchSpinDirectionClockBearing: LmClockBearing
  pitchSpinDirectionEfficiencyPct: Float

  pitchApproachVelocityMps: Float
  pitchApproachPlateTimestamp: LmTimestamp
  pitchApproachPlateWorld3dPositionMeters: Lm3dPosition

  pitchCrossPlateTimestamp: LmTimestamp
  pitchCrossPlateWorld3dPositionMeters: Lm3dPosition

  trajectory: LmTrajectory
}

type LmBaseballResults {
  # User Id
  pitcher: String
  hitter: String
  # User Display Names
  pitcherName: String
  hitterName: String

  # LM Baseball Data
  pitchData: BaseballPitchDataPoints
  batData: BaseballBatDataPoints
}

type LmBaseballStatistics {
    whiffPercent: Float
    swingAndMissPercent: Float
}
