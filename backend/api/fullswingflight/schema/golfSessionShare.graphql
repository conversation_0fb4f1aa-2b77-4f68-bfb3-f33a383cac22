"""
Session export models and types
"""
type LmShareSession @model
  @auth(rules: [
    { allow: owner, identityClaim: "username" },
    # { allow: private, operations: [get] },
    { allow: public, operations: [get], provider: iam },
    { allow: groups, groups: ["Admin"] }]) {
  shareUrl: ID! @primaryKey
  lmSessionId: ID!
    @index(name: "bySession", queryField: "shareBySession")
  owner: String @auth(rules: [{ allow: owner, identityClaim: "username" }, { allow: groups, groups: ["Admin"] }])
    @index(name: "byUser", queryField: "shareByUser")

  shareVideo: Boolean
  expiresAt: String

  session: LmSessionData
}

type LmSessionData {
  startTimestamp: Int
  endTimestamp: Int
  duration: Int

  name: String
  address: String

  elevation: Float
  temperature: Float
  humidity: Float
  location: LmLocation
  normalizedElevation: Float
  normalizedTemperature: Float
  normalizedBallType: LmBallType

  shots: [LmShotData]

  sessionQuality: Float
  drillTargets: [LmDrillTarget]
}

type LmShotData {
  pointId: String
  lmDrillTargetId: ID
  clubId: String
  clubColor: String
  clubCategory: String
  clubName: String
  clubType: LmClubType

  timestamp: Int
  isFavorite: Boolean

  impactUrl: String
  videoUrl: String

  clubSpeed: Float
  ballSpeed: Float
  smashFactor: Float
  attackAngle: Float
  clubPath: Float
  launchAngle: Float
  horizontalLaunchAngle: Float
  faceAngle: Float
  spinRate: Float
  spinAxis: Float
  carryDistance: Float
  totalDistance: Float
  side: Float
  sideTotal: Float
  apex: Float
  descentAngle: Float
  dynamicLoft: Float

  clubSpeedValid: Boolean
  ballSpeedValid: Boolean
  smashFactorValid: Boolean
  attackAngleValid: Boolean
  clubPathValid: Boolean
  launchAngleValid: Boolean
  horizontalLaunchAngleValid: Boolean
  faceAngleValid: Boolean
  spinRateValid: Boolean
  spinAxisValid: Boolean
  carryDistanceValid: Boolean
  totalDistanceValid: Boolean
  sideValid: Boolean
  sideTotalValid: Boolean
  apexValid: Boolean
  descentAngleValid: Boolean
  dynamicLoftValid: Boolean

  xFit: [Float]
  yFit: [Float]
  zFit: [Float]
  normalizedValues: LmShotNormalizedValues

  shotQuality: Float
  targetDistance: Float
  distanceToPin: Float
}
