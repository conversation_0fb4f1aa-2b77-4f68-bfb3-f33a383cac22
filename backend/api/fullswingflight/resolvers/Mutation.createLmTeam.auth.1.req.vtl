## [Start] Authorization Steps. **
$util.qr($ctx.stash.put("hasAuth", true))
#set( $inputFields = $util.parseJson($util.toJson($ctx.args.input.keySet())) )
#set( $isAuthorized = false )
#set( $allowedFields = [] )
#if( $util.authType() == "IAM Authorization" )
  #foreach( $adminRole in $ctx.stash.adminRoles )
    #if( $ctx.identity.userArn.contains($adminRole) && $ctx.identity.userArn != $ctx.stash.authRole && $ctx.identity.userArn != $ctx.stash.unauthRole )
      #return($util.toJson({}))
    #end
  #end
$util.unauthorized()
#end
#if( $util.authType() == "Lambda Authorization" )
$util.unauthorized()
#end
#if( $util.authType() == "User Pool Authorization" )
  #if( !$isAuthorized )
    #set( $staticGroupRoles = [{"claim":"cognito:groups","entity":"Coaches","allowedFields":["id","owner","name","profileImage","players","coaches","dataSessions","_version","_deleted","_lastChangedAt"],"isAuthorizedOnAllFields":true},{"claim":"cognito:groups","entity":"Admin","allowedFields":["id","owner","name","profileImage","players","coaches","dataSessions","_version","_deleted","_lastChangedAt"],"isAuthorizedOnAllFields":true}] )
    #foreach( $groupRole in $staticGroupRoles )
      #set( $groupsInToken = $util.defaultIfNull($ctx.identity.claims.get($groupRole.claim), []) )
      #if( $groupsInToken.contains($groupRole.entity) )
        #if( $groupRole.isAuthorizedOnAllFields )
          #set( $isAuthorized = true )
          #break
        #else
          $util.qr($allowedFields.addAll($groupRole.allowedFields))
        #end
      #end
    #end
  #end
  #set( $ownerEntity0 = $util.defaultIfNull($ctx.args.input.owner, null) )
  #set( $ownerClaim0 = $util.defaultIfNull($ctx.identity.claims.get("username"), $util.defaultIfNull($ctx.identity.claims.get("cognito:username"), null)) )
  #if( !$util.isNull($ownerClaim0) )
    #if( $isAuthorized && $util.isNull($ownerEntity0) && !$ctx.args.input.containsKey("owner") )
        $util.qr($ctx.args.input.put("owner", $ownerClaim0))
    #end
  #end
#end
#if( !$isAuthorized && $allowedFields.isEmpty() )
$util.unauthorized()
#end
#if( !$isAuthorized )
  #set( $deniedFields = $util.list.copyAndRemoveAll($inputFields, $allowedFields) )
  #if( $deniedFields.size() > 0 )
    $util.error("Unauthorized on ${deniedFields}", "Unauthorized")
  #end
#end
$util.toJson({"version":"2018-05-29","payload":{}})
## [End] Authorization Steps. **