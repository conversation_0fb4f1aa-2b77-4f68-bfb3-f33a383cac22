{"version": 1, "serviceConfiguration": {"serviceName": "AppSync", "defaultAuthType": {"mode": "AMAZON_COGNITO_USER_POOLS", "cognitoUserPoolId": "authfullswingflight03ff7ff503ff7ff5"}, "additionalAuthTypes": [{"mode": "AWS_IAM"}, {"mode": "AWS_LAMBDA", "lambdaFunction": "graphQlLambdaAuthorizer71aa6a86", "ttlSeconds": "300"}], "conflictResolution": {"defaultResolutionStrategy": {"type": "AUTOMERGE"}, "perModelResolutionStrategy": [{"resolutionStrategy": {"type": "OPTIMISTIC_CONCURRENCY"}, "entityName": "LmProfile"}]}, "apiName": "fullswingflight"}}