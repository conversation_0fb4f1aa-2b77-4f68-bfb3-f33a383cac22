#!/bin/bash

rm -rf src/graphql
rm -rf src/graphql-with-spec
#
amplify codegen
mkdir -p src/graphql-with-spec
cp -r src/graphql/* src/graphql-with-spec/

chmod +x $PWD/amplify/modify_graphql_codegen.sh
$PWD/amplify/modify_graphql_codegen.sh --remove \
  getLmTeam \
  listLmTeams \
  syncLmTeams \
  teamsByOwner \
  getLmUser \
  listLmUsers \
  syncLmUsers \
  --copy \
  getLmTeam \
  listLmTeams \
  syncLmTeams \
  teamsByOwner \
  getLmUser \
  listLmUsers \
  syncLmUsers


mkdir -p baseball-zip
mkdir -p golf-zip
timestamp=$(date "+%d%m-%H00")
zip -r "baseball-zip/graphql-baseball-${timestamp}.zip" src/graphql-with-spec
zip -r "golf-zip/graphql-golf-${timestamp}.zip" src/graphql-with-spec
