{
  "bbdev": {
    "awscloudformation": {
      "AuthRoleName": "amplify-fullswingflight-bbdev-97779-authRole",
      "UnauthRoleArn": "arn:aws:iam::098884155310:role/amplify-fullswingflight-bbdev-97779-unauthRole",
      "AuthRoleArn": "arn:aws:iam::098884155310:role/amplify-fullswingflight-bbdev-97779-authRole",
      "Region": "us-east-1",
      "DeploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
      "UnauthRoleName": "amplify-fullswingflight-bbdev-97779-unauthRole",
      "StackName": "amplify-fullswingflight-bbdev-97779",
      "StackId": "arn:aws:cloudformation:us-east-1:098884155310:stack/amplify-fullswingflight-bbdev-97779/ff2e01c0-223d-11f0-b973-0affe0f4b89f",
      "AmplifyAppId": "d2q1zlb7j205dz",
      "AuthTriggerTemplateURL": "https://s3.amazonaws.com/amplify-fullswingflight-bbdev-97779-deployment/amplify-cfn-templates/auth/auth-trigger-cloudformation-template.json"
    },
    "categories": {
      "function": {
        "fullswingflight03ff7ff503ff7ff5CustomMessage": {
          "authClientId": "590putas8p0f6v329je7jijoqo",
          "authUrl": "https://devauth.fullswingapps.com",
          "bayManagerAdminClientId": "5lqkbi3b9otvgkveqk0dne0mu",
          "bayManagerClientId": "s8rags8j2nd5ub3jsgausdu6d",
          "bayManagerResetUrl": "https://devbaymanager.fsgapi.net/login/",
          "bayManagerResidentialClientId": "32o11hm9h41bbmaji2osrqi60o",
          "leaguesAdminClientId": "1q7rnkqgaul580u7fptc74hhmc",
          "leaguesAdminUrl": "https://devcompeteadmin.fullswingapps.com",
          "leaguesClientId": "1nrgib91ejq8vg19i0kt1d8f8b",
          "leaguesUrl": "https://devleagues.fullswingapps.com",
          "userPoolId": "us-east-1_B0NCikVLQ",
          "version": "1.0.6",
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5CustomMessage-78336255673267574445-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5PostConfirmation": {
          "GROUP": "Users",
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5PostConfirmation-5350795a536e34784237-build.zip"
        },
        "graphQlLambdaAuthorizer71aa6a86": {
          "authClientId": "2qsljpeaguf0b4gun2hi3bassf",
          "authWebClientId": "1h35hsfemt8l1f80inbsq7rh0f",
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/graphQlLambdaAuthorizer71aa6a86-576f6c535a316349516c-build.zip"
        },
        "productRegistrationReport": {
          "sendTo": "<EMAIL>",
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/productRegistrationReport-635a7338674e37565672-build.zip"
        },
        "clubaverages": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/clubaverages-6b7348366d4c35677552-build.zip"
        },
        "fetchShadow": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/fetchShadow-6c73567*************-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5PostAuthentication": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5PostAuthentication-6a74687061686831362f-build.zip"
        },
        "fullswingflightstatsShared": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/fullswingflightstatsShared-LambdaLayerVersion9274e4c1-build.zip"
        },
        "removeUserAccountAndData": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/removeUserAccountAndData-733468774e4e5439556b-build.zip"
        },
        "resultsByTeamSession": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/resultsByTeamSession-714f2b576a36386c736c-build.zip"
        },
        "sessionsByTeam": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/sessionsByTeam-72524d46427836495236-build.zip"
        },
        "sessionsinrange": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/sessionsinrange-5578694a6f552b357676-build.zip"
        },
        "sessionstats": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/sessionstats-664674622f6165495463-build.zip"
        },
        "shareSession": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/shareSession-476e4a6957737641774d-build.zip"
        },
        "sharedsessions": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/sharedsessions-386859754d4251656654-build.zip"
        },
        "shotsbycategory": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/shotsbycategory-64645a782f4d564b744c-build.zip"
        },
        "shotsbyclub": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/shotsbyclub-784d7938582b7861434f-build.zip"
        },
        "shotsinrange": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/shotsinrange-7a514d4e597133346b50-build.zip"
        },
        "updateFeature": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/updateFeature-536746546d635a424c39-build.zip"
        },
        "updateUserFeatureAccess": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/updateUserFeatureAccess-7251615135337a38632b-build.zip"
        },
        "userExists": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/userExists-46326e412f667a486b44-build.zip"
        },
        "userstats": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/userstats-75336f62656566377938-build.zip"
        },
        "writeShadow": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/writeShadow-397874323431704c4657-build.zip"
        },
        "updateUserCustomRole": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/updateUserCustomRole-3464314e37444151312f-build.zip"
        },
        "chooseUserRole": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/chooseUserRole-67456662374d39553245-build.zip"
        },
        "createLmTeamInviteLink": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/createLmTeamInviteLink-7a78384a7a3554447639-build.zip",
          "secretsPathAmplifyAppId": "d2q1zlb7j205dz",
          "branchIoApiHost": "https://api2.branch.io/v1",
          "invitationDeepLinkPath": "invitation",
          "authFullswingPortalUrl": "https://auth.fullswingapps.com"
        },
        "acceptTeamInvite": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/acceptTeamInvite-366449325377692b4351-build.zip"
        },
        "addPlayersToTeam": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/addPlayersToTeam-754352497176354e6564-build.zip",
          "awsSesSender": "<EMAIL>",
          "awsSesSenderName": "Full Swing Baseball"
        },
        "addPlayerToTeam": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/addPlayerToTeam-6b46372b687a72433561-build.zip",
          "awsSesSender": "<EMAIL>",
          "awsSesSenderName": "Full Swing Baseball"
        },
        "createBranchIoInviteLink": {
          "secretsPathAmplifyAppId": "d2q1zlb7j205dz",
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/createBranchIoInviteLink-41307769314b56747258-build.zip",
          "authFullswingPortalUrl": "https://auth.fullswingapps.com",
          "branchIoApiHost": "https://api2.branch.io/v1",
          "invitationDeepLinkPath": "invitation"
        },
        "updateLmUserCustom": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/updateLmUserCustom-45664a7838767868384b-build.zip"
        },
        "syncLmUserCognito": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/syncLmUserCognito-2f595570487a74386f49-build.zip"
        },
        "asyncHandleCognitoPostConfirm": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/asyncHandleCognitoPostConfirm-342b443356744a484163-build.zip"
        },
        "deleteLmTeamPlayersCustom": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/deleteLmTeamPlayersCustom-3454532f5461704b3470-build.zip"
        },
        "onlyDevModifyDynamodbData": {
          "deploymentBucketName": "amplify-fullswingflight-bbdev-97779-deployment",
          "s3Key": "amplify-builds/onlyDevModifyDynamodbData-696e45446c7069463648-build.zip"
        }
      },
      "auth": {
        "fullswingflight03ff7ff503ff7ff5": {},
        "userPoolGroups": {}
      },
      "storage": {
        "fullswingFlightStorage": {}
      },
      "api": {
        "fullswingflight": {}
      }
    }
  },
  "dev": {
    "awscloudformation": {
      "AuthRoleName": "amplify-fullswingflight-dev-94747-authRole",
      "UnauthRoleArn": "arn:aws:iam::098884155310:role/amplify-fullswingflight-dev-94747-unauthRole",
      "AuthRoleArn": "arn:aws:iam::098884155310:role/amplify-fullswingflight-dev-94747-authRole",
      "Region": "us-east-1",
      "DeploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
      "UnauthRoleName": "amplify-fullswingflight-dev-94747-unauthRole",
      "StackName": "amplify-fullswingflight-dev-94747",
      "StackId": "arn:aws:cloudformation:us-east-1:098884155310:stack/amplify-fullswingflight-dev-94747/6a5aff60-29b5-11eb-a86f-0e69ad9c0ded",
      "AmplifyAppId": "d2q1zlb7j205dz",
      "AuthTriggerTemplateURL": "https://s3.amazonaws.com/amplify-fullswingflight-dev-94747-deployment/amplify-cfn-templates/auth/auth-trigger-cloudformation-template.json"
    },
    "categories": {
      "auth": {
        "fullswingflight03ff7ff503ff7ff5": {},
        "userPoolGroups": {}
      },
      "function": {
        "shotsbycategory": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/shotsbycategory-64645a782f4d564b744c-build.zip"
        },
        "fullswingflightstatsShared": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/fullswingflightstatsShared-LambdaLayerVersioncf601c80-build.zip"
        },
        "shotsbyclub": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/shotsbyclub-784d7938582b7861434f-build.zip"
        },
        "shotsinrange": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/shotsinrange-7a514d4e597133346b50-build.zip"
        },
        "sessionsinrange": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/sessionsinrange-5578694a6f552b357676-build.zip"
        },
        "clubaverages": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/clubaverages-6b7348366d4c35677552-build.zip"
        },
        "sessionstats": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/sessionstats-664674622f6165495463-build.zip"
        },
        "userstats": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/userstats-75336f62656566377938-build.zip"
        },
        "fetchShadow": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/fetchShadow-6c73567*************-build.zip"
        },
        "writeShadow": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/writeShadow-397874323431704c4657-build.zip"
        },
        "productRegistrationReport": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/productRegistrationReport-635a7338674e37565672-build.zip",
          "sendTo": "<EMAIL>"
        },
        "removeUserAccountAndData": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/removeUserAccountAndData-733468774e4e5439556b-build.zip"
        },
        "userExists": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/userExists-46326e412f667a486b44-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5PostConfirmation": {
          "GROUP": "Users",
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5PostConfirmation-5350795a536e34784237-build.zip"
        },
        "sharedsessions": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/sharedsessions-386859754d4251656654-build.zip"
        },
        "shareSession": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/shareSession-476e4a6957737641774d-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5CustomMessage": {
          "authClientId": "590putas8p0f6v329je7jijoqo",
          "authUrl": "https://devauth.fullswingapps.com",
          "bayManagerAdminClientId": "5lqkbi3b9otvgkveqk0dne0mu",
          "bayManagerClientId": "s8rags8j2nd5ub3jsgausdu6d",
          "bayManagerResetUrl": "https://devbaymanager.fsgapi.net/login/",
          "bayManagerResidentialClientId": "32o11hm9h41bbmaji2osrqi60o",
          "leaguesClientId": "1nrgib91ejq8vg19i0kt1d8f8b",
          "leaguesAdminClientId": "1q7rnkqgaul580u7fptc74hhmc",
          "leaguesAdminUrl": "https://devcompeteadmin.fullswingapps.com",
          "leaguesUrl": "https://devleagues.fullswingapps.com",
          "userPoolId": "us-east-1_B0NCikVLQ",
          "version": "1.0.6",
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5CustomMessage-6258513265474565476c-build.zip"
        },
        "updateFeature": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/updateFeature-536746546d635a424c39-build.zip"
        },
        "updateUserFeatureAccess": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/updateUserFeatureAccess-7251615135337a38632b-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5PostAuthentication": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5PostAuthentication-6a74687061686831362f-build.zip"
        },
        "graphQlLambdaAuthorizer71aa6a86": {
          "authClientId": "1gfodgsdraddcp944fkrq12d97",
          "authWebClientId": "5k1i8njllbj5oo5m8m2ila4rqd",
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/graphQlLambdaAuthorizer71aa6a86-576f6c535a316349516c-build.zip"
        },
        "resultsByTeamSession": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/resultsByTeamSession-714f2b576a36386c736c-build.zip"
        },
        "sessionsByTeam": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/sessionsByTeam-72524d46427836495236-build.zip"
        },
        "acceptTeamInvite": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/acceptTeamInvite-577a304e6e6959615833-build.zip"
        },
        "addPlayerToTeam": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/addPlayerToTeam-774d73415858684e5942-build.zip",
          "awsSesSender": "<EMAIL>",
          "awsSesSenderName": "Full Swing Baseball"
        },
        "asyncHandleCognitoPostConfirm": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/asyncHandleCognitoPostConfirm-49565972474c4b5a7567-build.zip"
        },
        "chooseUserRole": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/chooseUserRole-30515944474659533055-build.zip"
        },
        "createBranchIoInviteLink": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/createBranchIoInviteLink-684b355236384a54476a-build.zip",
          "branchIoApiHost": "https://api2.branch.io/v1",
          "invitationDeepLinkPath": "invitation",
          "branchIoKey": "key_test_gErnlYQk1EnKxpyaVXhIJgkkwrpdRlOk",
          "authFullswingPortalUrl": "https://stgauth.fullswingapps.com"
        },
        "createLmTeamInviteLink": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/createLmTeamInviteLink-7a78384a7a3554447639-build.zip",
          "branchIoApiHost": "https://api2.branch.io/v1",
          "invitationDeepLinkPath": "invitation",
          "authFullswingPortalUrl": "https://auth.fullswingapps.com"
        },
        "deleteLmTeamPlayersCustom": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/deleteLmTeamPlayersCustom-2b57624f6972332f716a-build.zip"
        },
        "syncLmUserCognito": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/syncLmUserCognito-30315544534431723774-build.zip"
        },
        "updateLmUserCustom": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/updateLmUserCustom-66796c664b5348394865-build.zip"
        },
        "updateUserCustomRole": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/updateUserCustomRole-3464314e37444151312f-build.zip"
        },
        "deleteLmTeamCustom": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/deleteLmTeamCustom-46384559373752613265-build.zip"
        },
        "leaveTeamCustom": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/leaveTeamCustom-6d4831465267664f2b69-build.zip"
        },
        "clearShadow": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/clearShadow-4a43434f464c73614c66-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5PreTokenGeneration": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5PreTokenGeneration-4236347a3246626d7836-build.zip",
          "accessTokenGeneratorSecret": "s2jc-ssdfjndD902"
        },
        "fullswingflight03ff7ff503ff7ff5CreateAuthChallenge": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5CreateAuthChallenge-327572716d6d6b325930-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5DefineAuthChallenge": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5DefineAuthChallenge-3478665931316e356b34-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse": {
          "deploymentBucketName": "amplify-fullswingflight-dev-94747-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse-516b4a6d742f47416e6f-build.zip",
          "authPortalUserPoolId": "us-east-1_B0NCikVLQ",
          "authPortalClientId": "5lqkbi3b9otvgkveqk0dne0mu"
        },
      },
      "api": {
        "fullswingflight": {}
      },
      "storage": {
        "fullswingFlightStorage": {}
      }
    },
    "nonCFNdata": {
      "function": {
        "fullswingflightstatsShared": {}
      }
    }
  },
  "test": {
    "awscloudformation": {
      "AuthRoleName": "amplify-fullswingflight-test-101733-authRole",
      "UnauthRoleArn": "arn:aws:iam::098884155310:role/amplify-fullswingflight-test-101733-unauthRole",
      "AuthRoleArn": "arn:aws:iam::098884155310:role/amplify-fullswingflight-test-101733-authRole",
      "Region": "us-east-1",
      "DeploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
      "UnauthRoleName": "amplify-fullswingflight-test-101733-unauthRole",
      "StackName": "amplify-fullswingflight-test-101733",
      "StackId": "arn:aws:cloudformation:us-east-1:098884155310:stack/amplify-fullswingflight-test-101733/bbcf6b60-2a82-11eb-a486-12409d333fbd",
      "AmplifyAppId": "d2q1zlb7j205dz",
      "AuthTriggerTemplateURL": "https://s3.amazonaws.com/amplify-fullswingflight-test-101733-deployment/amplify-cfn-templates/auth/auth-trigger-cloudformation-template.json"
    },
    "categories": {
      "auth": {
        "fullswingflight03ff7ff503ff7ff5": {},
        "userPoolGroups": {}
      },
      "function": {
        "shotsbycategory": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/shotsbycategory-64645a782f4d564b744c-build.zip"
        },
        "shotsbyclub": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/shotsbyclub-784d7938582b7861434f-build.zip"
        },
        "shotsinrange": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/shotsinrange-7a514d4e597133346b50-build.zip"
        },
        "sessionsinrange": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/sessionsinrange-5578694a6f552b357676-build.zip"
        },
        "clubaverages": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/clubaverages-6b7348366d4c35677552-build.zip"
        },
        "sessionstats": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/sessionstats-664674622f6165495463-build.zip"
        },
        "userstats": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/userstats-75336f62656566377938-build.zip"
        },
        "fullswingflightstatsShared": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/fullswingflightstatsShared-LambdaLayerVersion6c00e14d-build.zip"
        },
        "fetchShadow": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/fetchShadow-6c73567*************-build.zip"
        },
        "writeShadow": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/writeShadow-397874323431704c4657-build.zip"
        },
        "productRegistrationReport": {
          "sendTo": "<EMAIL>",
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/productRegistrationReport-635a7338674e37565672-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5PostConfirmation": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5PostConfirmation-344f48614d614c673543-build.zip",
          "GROUP": "Users"
        },
        "removeUserAccountAndData": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/removeUserAccountAndData-733468774e4e5439556b-build.zip"
        },
        "userExists": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/userExists-46326e412f667a486b44-build.zip"
        },
        "shareSession": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/shareSession-476e4a6957737641774d-build.zip"
        },
        "sharedsessions": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/sharedsessions-386859754d4251656654-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5CustomMessage": {
          "authClientId": "5htvhjjgp567ujdmo1ubbkd23g",
          "authUrl": "https://internalauth.fullswingapps.com",
          "bayManagerAdminClientId": "5gr4rm55qten2rm3mvh775jokq",
          "bayManagerClientId": "2e25mjfn2ov9ge74s4mqiobds5",
          "bayManagerResetUrl": "https://internalbaymanager.fullswingapps.com/login/",
          "bayManagerResidentialClientId": "589i033hmudigtke4qv64ar3e5",
          "leaguesClientId": "sm7gboaq5lnf2lj74gkfej4lv",
          "leaguesAdminClientId": "7e7hsvpei2de8prnocn2t1nlpe",
          "leaguesAdminUrl": "https://internalcompeteadmin.fullswingapps.com",
          "leaguesUrl": "https://internalcompete.fullswingapps.com",
          "userPoolId": "us-east-1_lQSxB65b8",
          "version": "1.0.6",
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5CustomMessage-78336255673267574445-build.zip"
        },
        "updateFeature": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/updateFeature-536746546d635a424c39-build.zip"
        },
        "updateUserFeatureAccess": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/updateUserFeatureAccess-7251615135337a38632b-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5PostAuthentication": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5PostAuthentication-6a74687061686831362f-build.zip"
        },
        "graphQlLambdaAuthorizer71aa6a86": {
          "authClientId": "6td03aigs7ll0rce2vurnijjra",
          "authWebClientId": "5vohl9m5jis0u79clvbltslii8",
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/graphQlLambdaAuthorizer71aa6a86-576f6c535a316349516c-build.zip"
        },
        "resultsByTeamSession": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/resultsByTeamSession-714f2b576a36386c736c-build.zip"
        },
        "sessionsByTeam": {
          "deploymentBucketName": "amplify-fullswingflight-test-101733-deployment",
          "s3Key": "amplify-builds/sessionsByTeam-72524d46427836495236-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5PreTokenGeneration": {
          "accessTokenGeneratorSecret": "s2jc-ssdfjndD902"
        }
      },
      "api": {
        "fullswingflight": {}
      },
      "storage": {
        "fullswingFlightStorage": {}
      }
    }
  },
  "prod": {
    "awscloudformation": {
      "AuthRoleName": "amplify-fullswingflight-prod-131439-authRole",
      "UnauthRoleArn": "arn:aws:iam::098884155310:role/amplify-fullswingflight-prod-131439-unauthRole",
      "AuthRoleArn": "arn:aws:iam::098884155310:role/amplify-fullswingflight-prod-131439-authRole",
      "Region": "us-east-1",
      "DeploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
      "UnauthRoleName": "amplify-fullswingflight-prod-131439-unauthRole",
      "StackName": "amplify-fullswingflight-prod-131439",
      "StackId": "arn:aws:cloudformation:us-east-1:098884155310:stack/amplify-fullswingflight-prod-131439/9f617840-3989-11eb-94ec-0aa443d7245f",
      "AmplifyAppId": "d2q1zlb7j205dz",
      "AuthTriggerTemplateURL": "https://s3.amazonaws.com/amplify-fullswingflight-prod-131439-deployment/amplify-cfn-templates/auth/auth-trigger-cloudformation-template.json"
    },
    "categories": {
      "auth": {
        "fullswingflight03ff7ff503ff7ff5": {},
        "userPoolGroups": {}
      },
      "function": {
        "shotsbycategory": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/shotsbycategory-646a4470483339533073-build.zip"
        },
        "fullswingflightstatsShared": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/fullswingflightstatsShared-LambdaLayerVersionb92d794e-build.zip"
        },
        "shotsbyclub": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/shotsbyclub-784d7938582b7861434f-build.zip"
        },
        "shotsinrange": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/shotsinrange-7a514d4e597133346b50-build.zip"
        },
        "sessionsinrange": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/sessionsinrange-5578694a6f552b357676-build.zip"
        },
        "clubaverages": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/clubaverages-6b7348366d4c35677552-build.zip"
        },
        "sessionstats": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/sessionstats-664674622f6165495463-build.zip"
        },
        "userstats": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/userstats-75336f62656566377938-build.zip"
        },
        "fetchShadow": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/fetchShadow-6c73567*************-build.zip"
        },
        "writeShadow": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/writeShadow-397874323431704c4657-build.zip"
        },
        "productRegistrationReport": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/productRegistrationReport-635a7338674e37565672-build.zip",
          "sendTo": "<EMAIL>"
        },
        "removeUserAccountAndData": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/removeUserAccountAndData-733468774e4e5439556b-build.zip"
        },
        "userExists": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/userExists-46326e412f667a486b44-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5PostConfirmation": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5PostConfirmation-344f48614d614c673543-build.zip",
          "GROUP": "Users"
        },
        "sharedsessions": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/sharedsessions-386859754d4251656654-build.zip"
        },
        "shareSession": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/shareSession-476e4a6957737641774d-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5CustomMessage": {
          "authClientId": "72j2av2reg092r4dcmejt6h7pi",
          "authUrl": "https://stgauth.fullswingapps.com",
          "bayManagerAdminClientId": "5td7tg19sionpqmfln809n1ld",
          "bayManagerClientId": "tnlq8ak8ud5gnd72987aa289q",
          "bayManagerResetUrl": "https://stgbaymanager.fullswingapps.com/login/",
          "bayManagerResidentialClientId": "3490n5ca67qcq9i6jjb8rfsm3b",
          "leaguesClientId": "7i8tejba4a2s76bccgdnamssoc",
          "leaguesAdminClientId": "3v4ldn7o506f68hit0m65nhv4n",
          "leaguesAdminUrl": "https://stgcompeteadmin.fullswingapps.com",
          "leaguesUrl": "https://stgcompete.fullswingapps.com",
          "userPoolId": "us-east-1_QXkqgwCpu",
          "version": "1.0.6",
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5CustomMessage-78336255673267574445-build.zip"
        },
        "updateFeature": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/updateFeature-536746546d635a424c39-build.zip"
        },
        "updateUserFeatureAccess": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/updateUserFeatureAccess-7251615135337a38632b-build.zip"
        },
        "graphQlLambdaAuthorizer71aa6a86": {
          "authClientId": "2qsljpeaguf0b4gun2hi3bassf",
          "authWebClientId": "1h35hsfemt8l1f80inbsq7rh0f",
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/graphQlLambdaAuthorizer71aa6a86-576f6c535a316349516c-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5PostAuthentication": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5PostAuthentication-6a74687061686831362f-build.zip"
        },
        "sessionsByTeam": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/sessionsByTeam-72524d46427836495236-build.zip"
        },
        "resultsByTeamSession": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/resultsByTeamSession-714f2b576a36386c736c-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5PreTokenGeneration": {
          "accessTokenGeneratorSecret": "s2jc-ssdfjndD902"
        },
        "fullswingflight03ff7ff503ff7ff5CreateAuthChallenge": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5CreateAuthChallenge-327572716d6d6b325930-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5DefineAuthChallenge": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5DefineAuthChallenge-3478665931316e356b34-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse": {
          "deploymentBucketName": "amplify-fullswingflight-prod-131439-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse-516b4a6d742f47416e6f-build.zip",
          "authPortalUserPoolId": "us-east-1_QXkqgwCpu",
          "authPortalClientId": "72j2av2reg092r4dcmejt6h7pi"
        }
      },
      "api": {
        "fullswingflight": {}
      },
      "storage": {
        "fullswingFlightStorage": {}
      }
    },
    "nonCFNdata": {
      "function": {
        "fullswingflightstatsShared": {}
      }
    }
  },
  "live": {
    "awscloudformation": {
      "AuthRoleName": "amplify-fullswingflight-live-145220-authRole",
      "UnauthRoleArn": "arn:aws:iam::098884155310:role/amplify-fullswingflight-live-145220-unauthRole",
      "AuthRoleArn": "arn:aws:iam::098884155310:role/amplify-fullswingflight-live-145220-authRole",
      "Region": "us-east-1",
      "DeploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
      "UnauthRoleName": "amplify-fullswingflight-live-145220-unauthRole",
      "StackName": "amplify-fullswingflight-live-145220",
      "StackId": "arn:aws:cloudformation:us-east-1:098884155310:stack/amplify-fullswingflight-live-145220/47dcce80-10de-11ec-84d3-12ab4fab6419",
      "AmplifyAppId": "d2q1zlb7j205dz",
      "AuthTriggerTemplateURL": "https://s3.amazonaws.com/amplify-fullswingflight-live-145220-deployment/amplify-cfn-templates/auth/auth-trigger-cloudformation-template.json"
    },
    "categories": {
      "auth": {
        "fullswingflight03ff7ff503ff7ff5": {},
        "userPoolGroups": {}
      },
      "function": {
        "shotsbycategory": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/shotsbycategory-64645a782f4d564b744c-build.zip"
        },
        "shotsbyclub": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/shotsbyclub-784d7938582b7861434f-build.zip"
        },
        "shotsinrange": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/shotsinrange-7a514d4e597133346b50-build.zip"
        },
        "sessionsinrange": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/sessionsinrange-5578694a6f552b357676-build.zip"
        },
        "clubaverages": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/clubaverages-6b7348366d4c35677552-build.zip"
        },
        "sessionstats": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/sessionstats-664674622f6165495463-build.zip"
        },
        "userstats": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/userstats-75336f62656566377938-build.zip"
        },
        "fullswingflightstatsShared": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/fullswingflightstatsShared-LambdaLayerVersion0e2d9801-build.zip"
        },
        "fetchShadow": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/fetchShadow-6c73567*************-build.zip"
        },
        "writeShadow": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/writeShadow-397874323431704c4657-build.zip"
        },
        "productRegistrationReport": {
          "sendTo": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/productRegistrationReport-635a7338674e37565672-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5CustomMessage": {
          "authClientId": "16hm0siv8s33b9sk1isdkhcoan",
          "authUrl": "https://auth.fullswingapps.com",
          "bayManagerAdminClientId": "3t3v8hl8c68gisuualujvduppn",
          "bayManagerClientId": "tnfn4vuel4cgep41miqllbuqn",
          "bayManagerResetUrl": "https://baymanager.fullswingapps.com/login/",
          "bayManagerResidentialClientId": "1anudan1safmr5ulqf38q1t9jt",
          "leaguesClientId": "18svcd9kur347h8qe501c2g9m9",
          "leaguesAdminClientId": "4ntb31slqkl0s17brtjmkpib8i",
          "leaguesAdminUrl": "https://admin.fscompete.com",
          "leaguesUrl": "https://fscompete.com",
          "userPoolId": "us-east-1_KWjsmZvMg",
          "version": "1.0.6",
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5CustomMessage-78336255673267574445-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5PostConfirmation": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5PostConfirmation-344f48614d614c673543-build.zip",
          "GROUP": "Users"
        },
        "removeUserAccountAndData": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/removeUserAccountAndData-733468774e4e5439556b-build.zip"
        },
        "shareSession": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/shareSession-476e4a6957737641774d-build.zip"
        },
        "sharedsessions": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/sharedsessions-386859754d4251656654-build.zip"
        },
        "userExists": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/userExists-46326e412f667a486b44-build.zip"
        },
        "updateFeature": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/updateFeature-536746546d635a424c39-build.zip"
        },
        "updateUserFeatureAccess": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/updateUserFeatureAccess-7251615135337a38632b-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5PostAuthentication": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5PostAuthentication-6a74687061686831362f-build.zip"
        },
        "graphQlLambdaAuthorizer71aa6a86": {
          "authClientId": "4766lulmrbktn2jp3i2iqa3l6l",
          "authWebClientId": "5uvjngv59j8pvfc3ispsu0qf8k",
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/graphQlLambdaAuthorizer71aa6a86-576f6c535a316349516c-build.zip"
        },
        "resultsByTeamSession": {},
        "sessionsByTeam": {},
        "fullswingflight": {},
        "fullswingflight03ff7ff503ff7ff5": {},
        "fullswingFlightStorage": {},
        "userPoolGroups": {},
        "fullswingflight03ff7ff503ff7ff5PreTokenGeneration": {
          "accessTokenGeneratorSecret": "6Ez6SwcJ6bHJth"
        },
        "fullswingflight03ff7ff503ff7ff5CreateAuthChallenge": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5CreateAuthChallenge-327572716d6d6b325930-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5DefineAuthChallenge": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5DefineAuthChallenge-3478665931316e356b34-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse": {
          "deploymentBucketName": "amplify-fullswingflight-live-145220-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5VerifyAuthChallengeResponse-516b4a6d742f47416e6f-build.zip",
          "authPortalUserPoolId": "us-east-1_KWjsmZvMg",
          "authPortalClientId": "4766lulmrbktn2jp3i2iqa3l6l"
        }
      },
      "api": {
        "fullswingflight": {}
      },
      "storage": {
        "fullswingFlightStorage": {}
      }
    }
  },
  "stage": {
    "awscloudformation": {
      "AuthRoleName": "amplify-fullswingflight-stage-fd8c8-authRole",
      "UnauthRoleArn": "arn:aws:iam::098884155310:role/amplify-fullswingflight-stage-fd8c8-unauthRole",
      "AuthRoleArn": "arn:aws:iam::098884155310:role/amplify-fullswingflight-stage-fd8c8-authRole",
      "Region": "us-east-1",
      "DeploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
      "UnauthRoleName": "amplify-fullswingflight-stage-fd8c8-unauthRole",
      "StackName": "amplify-fullswingflight-stage-fd8c8",
      "StackId": "arn:aws:cloudformation:us-east-1:098884155310:stack/amplify-fullswingflight-stage-fd8c8/9fd5a980-19aa-11f0-a15f-0e2513589565",
      "AmplifyAppId": "d2q1zlb7j205dz",
      "AuthTriggerTemplateURL": "https://s3.amazonaws.com/amplify-fullswingflight-stage-fd8c8-deployment/amplify-cfn-templates/auth/auth-trigger-cloudformation-template.json"
    },
    "categories": {
      "auth": {
        "fullswingflight03ff7ff503ff7ff5": {},
        "userPoolGroups": {}
      },
      "function": {
        "clubaverages": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/clubaverages-5037782f32366c787a73-build.zip"
        },
        "fetchShadow": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/fetchShadow-4e4f72434a3774364343-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5CustomMessage": {
          "authClientId": "72j2av2reg092r4dcmejt6h7pi",
          "authUrl": "https://stgauth.fullswingapps.com",
          "bayManagerAdminClientId": "5td7tg19sionpqmfln809n1ld",
          "bayManagerClientId": "tnlq8ak8ud5gnd72987aa289q",
          "bayManagerResetUrl": "https://stgbaymanager.fullswingapps.com/login/",
          "bayManagerResidentialClientId": "3490n5ca67qcq9i6jjb8rfsm3b",
          "leaguesClientId": "7i8tejba4a2s76bccgdnamssoc",
          "leaguesAdminClientId": "3v4ldn7o506f68hit0m65nhv4n",
          "leaguesAdminUrl": "https://stgcompeteadmin.fullswingapps.com",
          "leaguesUrl": "https://stgcompete.fullswingapps.com",
          "userPoolId": "us-east-1_QXkqgwCpu",
          "version": "1.0.6",
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5CustomMessage-35303556365a36654951-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5PostAuthentication": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5PostAuthentication-4f4c65577a7549525158-build.zip"
        },
        "fullswingflight03ff7ff503ff7ff5PostConfirmation": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/fullswingflight03ff7ff503ff7ff5PostConfirmation-766e646f777a6e637138-build.zip",
          "GROUP": "Users"
        },
        "fullswingflightstatsShared": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/fullswingflightstatsShared-LambdaLayerVersione5a2b003-build.zip"
        },
        "graphQlLambdaAuthorizer71aa6a86": {
          "authClientId": "4766lulmrbktn2jp3i2iqa3l6l",
          "authWebClientId": "5uvjngv59j8pvfc3ispsu0qf8k",
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/graphQlLambdaAuthorizer71aa6a86-68357766744454453137-build.zip"
        },
        "productRegistrationReport": {
          "sendTo": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/productRegistrationReport-4a32485977512f595873-build.zip"
        },
        "removeUserAccountAndData": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/removeUserAccountAndData-3433507763387a4a4157-build.zip"
        },
        "resultsByTeamSession": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/resultsByTeamSession-73717a33577743487a32-build.zip"
        },
        "sessionsByTeam": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/sessionsByTeam-3637676b796c66333854-build.zip"
        },
        "sessionsinrange": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/sessionsinrange-43664b7977747a336d55-build.zip"
        },
        "sessionstats": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/sessionstats-4a704d4a7164454d6f63-build.zip"
        },
        "shareSession": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/shareSession-4d755644504530312f66-build.zip"
        },
        "sharedsessions": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/sharedsessions-6662563851446d526e59-build.zip"
        },
        "shotsbycategory": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/shotsbycategory-6e462b45697675556f7a-build.zip"
        },
        "shotsbyclub": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/shotsbyclub-5877627a5077414b7149-build.zip"
        },
        "shotsinrange": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/shotsinrange-4b6c38774c2f78316454-build.zip"
        },
        "updateFeature": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/updateFeature-2f4b59304a41336f5157-build.zip"
        },
        "updateUserFeatureAccess": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/updateUserFeatureAccess-52363858655830394570-build.zip"
        },
        "userExists": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/userExists-384d674858755461394a-build.zip"
        },
        "userstats": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/userstats-492b354d6e4e4c4b6769-build.zip"
        },
        "writeShadow": {
          "deploymentBucketName": "amplify-fullswingflight-stage-fd8c8-deployment",
          "s3Key": "amplify-builds/writeShadow-625155482f4c476c4941-build.zip"
        },
        "fullswingflight": {},
        "fullswingflight03ff7ff503ff7ff5": {},
        "fullswingFlightStorage": {},
        "userPoolGroups": {},
        "fullswingflight03ff7ff503ff7ff5PreTokenGeneration": {
          "accessTokenGeneratorSecret": "s2jc-ssdfjndD902"
        }
      },
      "api": {
        "fullswingflight": {}
      },
      "storage": {
        "fullswingFlightStorage": {}
      }
    }
  }
}