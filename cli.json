{"features": {"graphqltransformer": {"addmissingownerfields": true, "improvepluralization": true, "validatetypenamereservedwords": true, "useexperimentalpipelinedtransformer": true, "enableiterativegsiupdates": true, "secondarykeyasgsi": true, "skipoverridemutationinputtypes": true, "showfieldauthnotification": false, "suppressschemamigrationprompt": true, "securityEnhancementNotification": false, "transformerversion": 2, "usesubusernamefordefaultidentityclaim": true, "usefieldnameforprimarykeyconnectionfield": false, "enableautoindexquerynames": true, "respectprimarykeyattributesonconnectionfield": false, "populateownerfieldforstaticgroupauth": true, "subscriptionsInheritPrimaryAuth": true}, "frontend-ios": {"enablexcodeintegration": false}, "auth": {"enablecaseinsensitivity": true, "useinclusiveterminology": true, "breakcirculardependency": true}, "codegen": {"useappsyncmodelgenplugin": true, "usedocsgeneratorplugin": true, "usetypesgeneratorplugin": true, "cleangeneratedmodelsdirectory": true, "retaincasestyle": true, "addtimestampfields": true, "handlelistnullabilitytransparently": true, "emitauthprovider": true, "generateindexrules": true, "enabledartnullsafety": true}, "appsync": {"generategraphqlpermissions": true}, "project": {"overrides": true}}, "debug": {"shareProjectConfig": false}}