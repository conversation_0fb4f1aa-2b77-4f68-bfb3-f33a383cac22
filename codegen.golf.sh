# Codegen helper for Golf Mobile
#
timestamp=$(date "+%d%m-%H00")
ZIP_PATH=golf-zip
MODEL_DIR=golf-models
# Clear output directory
rm -rf golf_schema
mkdir golf_schema
mkdir -p $ZIP_PATH

cat amplify/backend/api/fullswingflight/schema/common.graphql >> golf_schema/schema.graphql
cat amplify/backend/api/fullswingflight/schema/golf.graphql >> golf_schema/schema.graphql
cat amplify/backend/api/fullswingflight/schema/lambda.graphql >> golf_schema/schema.graphql
cat amplify/backend/api/fullswingflight/schema_shims/golf_shims.txt >> golf_schema/schema.graphql

# Comment out special lines
sed -e 's/^\(.*\)@Multisport/#\ &/g' -i '' golf_schema/schema.graphql
sed -e 's/^\(.*\)@Baseball/#\ &/g' -i '' golf_schema/schema.graphql

# Set output dir to match project
amplify codegen models --model-schema golf_schema/schema.graphql --target javascript --output-dir "./src/$MODEL_DIR"
zip -r "$ZIP_PATH/models-golf-${timestamp}.zip" "src/$MODEL_DIR"
# Cleanup temporary files (comment this out to debug)
rm -rf golf_schema
