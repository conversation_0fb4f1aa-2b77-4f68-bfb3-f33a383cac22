//
//  ModelFieldDefinition+Name.swift
//  LaunchMonitorConnection
//
//  Created by <PERSON> on 10/10/24.
//
import Amplify

extension ModelFieldDefinition {

    public static func field(_ key: Coding<PERSON><PERSON>,
                             name: String,
                             is nullability: ModelFieldNullability = .required,
                             isReadOnly: Bool = false,
                             ofType type: ModelFieldType = .string,
                             attributes: [ModelFieldAttribute] = [],
                             association: ModelAssociation? = nil,
                             authRules: AuthRules = []) -> ModelFieldDefinition {
        return .field(name: name,
                      type: type,
                      nullability: nullability,
                      isReadOnly: isReadOnly,
                      association: association,
                      attributes: attributes,
                      authRules: authRules)
    }
}
