export const getLmTeam = /* GraphQL */ `
  query GetLmTeam($id: ID!) {
    getLmTeam(id: $id) {
      id
      owner
      name
      profileImage
      pitcherId
      readers
      players(filter: {_deleted: {ne: true}}) {
        items {
          id
          owner
          readers
          type
          status
          email
          joined_at
          lmTeamId
          lmPlayerId
          lmPlayerName
          lineupOrderBatting
          lineupOrderPitching
          team {
            id
            owner
            name
            profileImage
            pitcherId
            readers
            players {
              nextToken
              startedAt
              __typename
            }
            coaches {
              nextToken
              startedAt
              __typename
            }
            dataSessions {
              nextToken
              startedAt
              __typename
            }
            createdAt
            updatedAt
            _version
            _deleted
            _lastChangedAt
            __typename
          }
          createdAt
          updatedAt
          _version
          _deleted
          _lastChangedAt
          __typename
        }
        nextToken
        startedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      __typename
    }
  }
`;
export const listLmTeams = /* GraphQL */ `
  query ListLmTeams(
    $id: ID
    $filter: ModelLmTeamFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmTeams(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        players(filter: {_deleted: {ne: true}}) {
          items {
            id
            owner
            readers
            type
            status
            email
            joined_at
            lmTeamId
            lmPlayerId
            lmPlayerName
            lineupOrderBatting
            lineupOrderPitching
            createdAt
            updatedAt
            _version
            _deleted
            _lastChangedAt
            __typename
          }
          nextToken
          startedAt
          __typename
        }
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmTeams = /* GraphQL */ `
  query SyncLmTeams(
    $filter: ModelLmTeamFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmTeams(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        players(filter: {_deleted: {ne: true}}) {
          items {
            id
            owner
            readers
            type
            status
            email
            joined_at
            lmTeamId
            lmPlayerId
            lmPlayerName
            lineupOrderBatting
            lineupOrderPitching
            createdAt
            updatedAt
            _version
            _deleted
            _lastChangedAt
            __typename
          }
          nextToken
          startedAt
          __typename
        }
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const teamsByOwner = /* GraphQL */ `
  query TeamsByOwner(
    $owner: String!
    $sortDirection: ModelSortDirection
    $filter: ModelLmTeamFilterInput
    $limit: Int
    $nextToken: String
  ) {
    teamsByOwner(
      owner: $owner
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        owner
        name
        profileImage
        pitcherId
        readers
        players(filter: {_deleted: {ne: true}}) {
          items {
            id
            owner
            readers
            type
            status
            email
            joined_at
            lmTeamId
            lmPlayerId
            lmPlayerName
            lineupOrderBatting
            lineupOrderPitching
            createdAt
            updatedAt
            _version
            _deleted
            _lastChangedAt
            __typename
          }
          nextToken
          startedAt
          __typename
        }
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const getLmUser = /* GraphQL */ `
  query GetLmUser($id: ID!) {
    getLmUser(id: $id) {
      id
      setupComplete
      email
      phone
      fullName
      profileImage
      userType
      gender
      handedness
      birthdate
      companyName
      shippingAddressLine1
      shippingAddressLine2
      shippingPostcode
      shippingLocality
      shippingRegion
      shippingCountry
      subscriptions
      competitiveLevel
      teamName
      organizationSchool
      baseballPlayerPosition
      playerTeams(filter: {status: {eq: Accepted}, _deleted: {ne: true}}) {
        items {
          id
          owner
          readers
          type
          status
          email
          joined_at
          lmTeamId
          lmPlayerId
          lmPlayerName
          lineupOrderBatting
          lineupOrderPitching
          team {
            id
            owner
            name
            profileImage
            pitcherId
            readers
            createdAt
            updatedAt
            _version
            _deleted
            _lastChangedAt
            __typename
          }
          createdAt
          updatedAt
          _version
          _deleted
          _lastChangedAt
          __typename
        }
        nextToken
        startedAt
        __typename
      }
      createdAt
      updatedAt
      _version
      _deleted
      _lastChangedAt
      owner
      __typename
    }
  }
`;
export const listLmUsers = /* GraphQL */ `
  query ListLmUsers(
    $id: ID
    $filter: ModelLmUserFilterInput
    $limit: Int
    $nextToken: String
    $sortDirection: ModelSortDirection
  ) {
    listLmUsers(
      id: $id
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      sortDirection: $sortDirection
    ) {
      items {
        id
        setupComplete
        email
        phone
        fullName
        profileImage
        userType
        gender
        handedness
        birthdate
        companyName
        shippingAddressLine1
        shippingAddressLine2
        shippingPostcode
        shippingLocality
        shippingRegion
        shippingCountry
        subscriptions
        competitiveLevel
        teamName
        organizationSchool
        baseballPlayerPosition
        playerTeams(filter: {status: {eq: Accepted}, _deleted: {ne: true}}) {
          items {
            id
            owner
            readers
            type
            status
            email
            joined_at
            lmTeamId
            lmPlayerId
            lmPlayerName
            lineupOrderBatting
            lineupOrderPitching
            team {
              id
              owner
              name
              profileImage
              pitcherId
              readers
              createdAt
              updatedAt
              _version
              _deleted
              _lastChangedAt
              __typename
            }
            createdAt
            updatedAt
            _version
            _deleted
            _lastChangedAt
            __typename
          }
          nextToken
          startedAt
          __typename
        }
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
export const syncLmUsers = /* GraphQL */ `
  query SyncLmUsers(
    $filter: ModelLmUserFilterInput
    $limit: Int
    $nextToken: String
    $lastSync: AWSTimestamp
  ) {
    syncLmUsers(
      filter: $filter
      limit: $limit
      nextToken: $nextToken
      lastSync: $lastSync
    ) {
      items {
        id
        setupComplete
        email
        phone
        fullName
        profileImage
        userType
        gender
        handedness
        birthdate
        companyName
        shippingAddressLine1
        shippingAddressLine2
        shippingPostcode
        shippingLocality
        shippingRegion
        shippingCountry
        subscriptions
        competitiveLevel
        teamName
        organizationSchool
        baseballPlayerPosition
        playerTeams(filter: {status: {eq: Accepted}, _deleted: {ne: true}}) {
          items {
            id
            owner
            readers
            type
            status
            email
            joined_at
            lmTeamId
            lmPlayerId
            lmPlayerName
            lineupOrderBatting
            lineupOrderPitching
            team {
              id
              owner
              name
              profileImage
              pitcherId
              readers
              createdAt
              updatedAt
              _version
              _deleted
              _lastChangedAt
              __typename
            }
            createdAt
            updatedAt
            _version
            _deleted
            _lastChangedAt
            __typename
          }
          nextToken
          startedAt
          __typename
        }
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        owner
        __typename
      }
      nextToken
      startedAt
      __typename
    }
  }
`;
