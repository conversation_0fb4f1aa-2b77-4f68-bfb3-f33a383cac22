diff --git a/cocoapods-patch-20241010-66317-fu4yxh/AmplifyPlugins/AmplifyPlugins/DataStore/AWSDataStoreCategoryPlugin/Storage/SQLite/Model+SQLite.swift b/Pods/AmplifyPlugins/AmplifyPlugins/DataStore/AWSDataStoreCategoryPlugin/Storage/SQLite/Model+SQLite.swift
index abd04b4..3683667 100644
--- a/cocoapods-patch-20241010-66317-fu4yxh/AmplifyPlugins/AmplifyPlugins/DataStore/AWSDataStoreCategoryPlugin/Storage/SQLite/Model+SQLite.swift
+++ b/Pods/AmplifyPlugins/AmplifyPlugins/DataStore/AWSDataStoreCategoryPlugin/Storage/SQLite/Model+SQLite.swift
@@ -202,7 +202,12 @@ extension Array where Element == ModelSchema {
             }
         }
 
-        let sortedStartList = sorted { $0.name < $1.name }
+        let sortedStartList = sorted { 
+            if $0.priority == $1.priority {
+                return $0.name < $1.name
+            }
+            return $0.priority < $1.priority
+        }
         sortedStartList.forEach(walkAssociatedModels(of:))
         return sortedKeys.map { sortMap[$0]! }
     }
