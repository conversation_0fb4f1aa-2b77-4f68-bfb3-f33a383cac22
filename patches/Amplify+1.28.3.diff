diff --git a/cocoapods-patch-20241010-66172-funan2/Amplify/Amplify/Categories/DataStore/Model/Internal/Schema/ModelSchema+Definition.swift b/Pods/Amplify/Amplify/Categories/DataStore/Model/Internal/Schema/ModelSchema+Definition.swift
index 7f5efaa..00877c6 100644
--- a/cocoapods-patch-20241010-66172-funan2/Amplify/Amplify/Categories/DataStore/Model/Internal/Schema/ModelSchema+Definition.swift
+++ b/Pods/Amplify/Amplify/Categories/DataStore/Model/Internal/Schema/ModelSchema+Definition.swift
@@ -136,13 +136,17 @@ public struct ModelSchemaDefinition {
     internal var primarykeyFields: [ModelFieldName]
     internal var attributes: [ModelAttribute]
 
+    public var priority: ModelSyncPriority = .low
+
     init(name: String,
          pluralName: String? = nil,
          listPluralName: String? = nil,
          syncPluralName: String? = nil,
+         priority: ModelSyncPriority = .low,
          authRules: AuthRules = [],
          attributes: [ModelAttribute] = []) {
         self.name = name
+        self.priority = priority
         self.pluralName = pluralName
         self.listPluralName = listPluralName
         self.syncPluralName = syncPluralName
@@ -179,6 +183,7 @@ public struct ModelSchemaDefinition {
                            pluralName: pluralName,
                            listPluralName: listPluralName,
                            syncPluralName: syncPluralName,
+                           priority: priority,
                            authRules: authRules,
                            attributes: attributes,
                            fields: fields,
@@ -196,7 +201,7 @@ public enum ModelFieldDefinition {
                association: ModelAssociation?,
                attributes: [ModelFieldAttribute],
                authRules: AuthRules)
-
+    
     public static func field(_ key: CodingKey,
                              is nullability: ModelFieldNullability = .required,
                              isReadOnly: Bool = false,
@@ -212,7 +217,6 @@ public enum ModelFieldDefinition {
                       attributes: attributes,
                       authRules: authRules)
     }
-
     @available(*, deprecated, message: "Use .primaryKey(fields:)")
     public static func id(_ key: CodingKey) -> ModelFieldDefinition {
         return id(key.stringValue)
diff --git a/cocoapods-patch-20241010-66172-funan2/Amplify/Amplify/Categories/DataStore/Model/Internal/Schema/ModelSchema.swift b/Pods/Amplify/Amplify/Categories/DataStore/Model/Internal/Schema/ModelSchema.swift
index e99d0e2..c33cf0a 100644
--- a/cocoapods-patch-20241010-66172-funan2/Amplify/Amplify/Categories/DataStore/Model/Internal/Schema/ModelSchema.swift
+++ b/Pods/Amplify/Amplify/Categories/DataStore/Model/Internal/Schema/ModelSchema.swift
@@ -35,6 +35,18 @@ public enum ModelFieldAttribute {
     case primaryKey
 }
 
+/// - Warning: Although this has `public` access, it is intended for internal & codegen use and should not be used
+///   directly by host applications. The behavior of this may change without warning.
+public enum ModelSyncPriority:Int, Codable {
+    case high = 0
+    case medium = 1
+    case low = 2
+
+    public static func < (lhs: Self, rhs: Self) -> Bool {
+        return lhs.rawValue < rhs.rawValue
+    }
+}
+
 /// - Warning: Although this has `public` access, it is intended for internal & codegen use and should not be used
 ///   directly by host applications. The behavior of this may change without warning.
 public struct ModelField {
@@ -106,15 +118,19 @@ public struct ModelSchema {
         return primaryKey
     }
 
+    public var priority: ModelSyncPriority = .low
+
     public init(name: String,
                 pluralName: String? = nil,
                 listPluralName: String? = nil,
                 syncPluralName: String? = nil,
+                priority: ModelSyncPriority = .low,
                 authRules: AuthRules = [],
                 attributes: [ModelAttribute] = [],
                 fields: ModelFields = [:],
                 primaryKeyFieldKeys: [ModelFieldName] = []) {
         self.name = name
+        self.priority = priority
         self.pluralName = pluralName
         self.listPluralName = listPluralName
         self.syncPluralName = syncPluralName
