# Codegen helper for Baseball Mobile
#
timestamp=$(date "+%d%m-%H00")
ZIP_PATH=baseball-zip
MODEL_DIR=baseball-models
# Clear output directory
rm -rf ms_schema
mkdir ms_schema
mkdir -p $ZIP_PATH

cat amplify/backend/api/fullswingflight/schema/common.graphql >> ms_schema/schema.graphql
cat amplify/backend/api/fullswingflight/schema/baseball.graphql >> ms_schema/schema.graphql
cat amplify/backend/api/fullswingflight/schema/multisport.graphql >> ms_schema/schema.graphql
cat amplify/backend/api/fullswingflight/schema/coaching.graphql >> ms_schema/schema.graphql
cat amplify/backend/api/fullswingflight/schema/lambda.graphql >> ms_schema/schema.graphql
cat amplify/backend/api/fullswingflight/schema_shims/ms_shims.txt >> ms_schema/schema.graphql

# Comment out special lines
sed -e 's/^\(.*\)@Golf/#\ &/g' -i '' ms_schema/schema.graphql

# Set output dir to match project
amplify codegen models --model-schema ms_schema/schema.graphql --target javascript --output-dir "./src/$MODEL_DIR"
zip -r "$ZIP_PATH/models-baseball-${timestamp}.zip" "src/$MODEL_DIR"
# Cleanup temporary files (comment this out to debug)
rm -rf ms_schema
