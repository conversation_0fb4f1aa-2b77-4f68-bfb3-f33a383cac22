# Full Swing Kit - Amplify Backend Project

## Table of Contents
- [Overview](#overview)
- [Project Setup](#project-setup)
- [Structure](#structure)
- [Branching](#branching)

## Overview
This is the Amplify backend in use for the Kit iOS app.  This repository is intended to be used as a git submodule for any frontend projects using this backend.  See the ios-native repository for an example.

## Project Setup
Requires use and setup of the Amplify CLI
https://docs.amplify.aws/cli/

### Environments
There are multiple backend environments set up.  Configurations for these can be found in team-provider-info.json.  Other branches may be spun up for development or other reasons.
#### dev
Used for new feature development.  Any user data stored here is likely to be wiped without notice or potentially be out of sync with released code.
#### test
Used for testing deployment of new configurations.  Should be pushed against before 'Live' to ensure no configurations are missing/broken.
#### prod
Backend for TestFlight/QA testing.  This backend should be fairly persistent and stable, leading feature development and testing ahead of 'Live'
#### stage
Used to verify deployment prior to pushing to live.  Should generally have the same version as live, but pushed against prior to release.
#### live
Published environment for App Store builds.  This holds all real user data and should not be manipulated without care.

## Structure
https://docs.amplify.aws/cli/reference/files/

#### .config
Project config files.  local-* files may need local edits to make CLI work.

#### backend
Contains definitions for all backend resources.  

- ###### api
    Contains the schema.graphql file which defines the GraphQL API and data models.

- ###### auth
    Build files for Cognito authentication backend.

- ###### function
    Lambda functions used for various purposes within the Amplify project.

#### extensions
Common code extensions used for front end development.  Currently contains 

#### patches
Patches intended to be applied to Amplify cocoapods.  Currently used in iOS Golf app for adding ability to prioritize tables for initial data sync.
Must be used with cocoapods-patch, unknown if a solution exists for local patches with Swift Package Manager.


## Branching
Branching is similar to ios-native, however the use of CI/CD is not applied to this repository directly so this is less rigid than ios-native.

#### chore/x
* Used for project changes or other DevOPs

#### feature/x
* Used for feature development

#### bugfix/x
* Used to develop fixes for bugs that have been logged by QA

#### development
* Used for completed stories and features from active development

#### test/stage/live
* Merge into these branches when the backend environment has been pushed to. These help track what is currently active in the backend environment.
