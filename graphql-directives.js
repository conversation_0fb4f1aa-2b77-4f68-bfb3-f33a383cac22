import gql from 'graphql-tag';

const clientSchemaExtensions = gql`
    directive @model on OBJECT
    directive @aws_cognito_user_pools on OBJECT
    directive @auth(rules: [AuthRule!]!) on OBJECT | FIELD_DEFINITION
    directive @hasMany(indexName: String, fields: [String]) on FIELD_DEFINITION
    directive @identityClaim on FIELD_DEFINITION
    directive @primaryKey on FIELD_DEFINITION
    directive @index(name: String!, sortKeyFields: [String!], queryField: String!) repeatable on FIELD_DEFINITION
    directive @hasOne(fields: [String!]!) on FIELD_DEFINITION
    directive @belongsTo(fields: [String!]!) on FIELD_DEFINITION
    directive @function(name: String!) on FIELD_DEFINITION
    
    scalar AWSDateTime

    input AuthRule {
        allow: AuthStrategy!
        operations: [ModelOperation]
        ownerField: String
        identityClaim: String
        groups: [String]
        provider: Provider
    }

    enum AuthStrategy { 
        owner
        public
        private
        groups
        custom
    }

    enum ModelOperation {
        create
        update
        delete
        read
        get
    }
    
    enum Provider {
        iam
        userPools
    }
`;