# Amplify CLI Hook (post push auth)
#
# Backfill CustomMessage Lambda on Cognito for an environment
#
# <AUTHOR>
# @Date Oct 19 2023
# @Email <EMAIL>
# Copyright FullSwing Golf
#

require 'aws-sdk-core'
require 'aws-sdk-cognitoidentityprovider'
require 'json'
require_relative 'lib/AwsQueries'
require_relative 'lib/hash_ext'
require_relative 'lib/amplify_env'

include AmplifyEnv
include PoolQueries

params = JSON.parse(gets)
data = params["data"]
error = params["error"]
puts data
puts error

# Set global variables used in API wrappers based on environment arguments
load_env(data)
if $profile_name.nil? 
    puts "Post Push step requires Amplify to be configured for AWS CLI profile."
    return
end

# Set Pre Token Generation Cognito Trigger to correct trigger event version
# Custom Cloud formation stack built by Amplify leaves no room for customizing or setting this version.
# Build ARN for our pre token lambda
pre_trigger_arn = "arn:aws:lambda:us-east-1:098884155310:function:functionfullswingflight03ff7ff503ff7ff5PreTokenGeneration-#{$amplify_env}"

# Initialize AWS SDK client
Aws.config.update(region: 'us-east-1')
$client = Aws::CognitoIdentityProvider::Client.new(profile: $profile_name)

# Request current configuration
pool_response = describe_user_pool()
pool = pool_response.user_pool

# Update configuration if not set to desired version
if pool.lambda_config.pre_token_generation_config.lambda_version != 'V3_0' 
    puts "Pre Token Generation not correct version"
    pool_config = pool.to_h
    pool_config.delete(:id)
    pool_config.delete(:name)
    pool_config.delete(:last_modified_date)
    pool_config.delete(:creation_date)
    pool_config.delete(:schema_attributes)
    pool_config.delete(:username_attributes)
    pool_config.delete(:estimated_number_of_users)
    pool_config.delete(:domain)
    pool_config.delete(:arn)
    pool_config[:admin_create_user_config].delete(:unused_account_validity_days)

    pool_config[:lambda_config][:pre_token_generation_config][:lambda_version] = 'V3_0'

    update_user_pool(pool_config)
else
    puts "Pre Token Generation: " + pool.lambda_config.pre_token_generation_config.to_s
end
