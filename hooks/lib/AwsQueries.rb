#!/home/<USER>/.rbenv/shims/ruby

# DynamoDB Utility Functions
#
# Generates session/shot data for a time duration
#
# <AUTHOR>
# @Date May 5 2021
# @Email <EMAIL>
# Copyright Systematic Group
#

module PoolQueries
    def list_identity_pools()
        pagination_token = nil
        params = {
            max_results: 25,
            pagination_token: pagination_token
        }
        $id_client.list_identity_pools(params)
    end

    def describe_identity_pool()
        pagination_token = nil
        params = {
            identity_pool_id: $pool_id,
        }
        $id_client.describe_identity_pool(params)
    end


    def list_user_pools()
        pagination_token = nil
        params = {
            max_results: 25,
            pagination_token: pagination_token
        }
        $client.list_user_pools(params)
    end

    def describe_user_pool()
        pagination_token = nil
        params = {
            user_pool_id: $pool_id,
        }
        $client.describe_user_pool(params)
    end

    def update_user_pool(attributes)
        pagination_token = nil
        params = {
            user_pool_id: $pool_id,
        }
        puts params.merge(attributes)
        $client.update_user_pool(params.merge(attributes))
    end
end
