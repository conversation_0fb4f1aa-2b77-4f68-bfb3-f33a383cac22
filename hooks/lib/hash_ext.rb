#!/home/<USER>/.rbenv/shims/ruby

# Hash Extensions
#
# Allows querying hash keys by symbol or string
#
# <AUTHOR>
# @Date Jan 12 2023
# @Email <EMAIL>
# Copyright Systematic Group
#

class Hash
    def method_missing(meth, *args, &block)
        if has_key?(meth.to_s)
            self[meth.to_s]
        elsif has_key?(meth.to_sym)
            self[meth.to_sym]
        else
            nil # raise NoMethodError, "undefined method #{meth} for #{self}"
        end
    end

    def eql?(other) 
        other == self
    end
end