#!/home/<USER>/.rbenv/shims/ruby

# Amplify Env
#
# Set Amplify variables 
#
# <AUTHOR>
# @Date April 12 2023
# @Email <EMAIL>
# Copyright Systematic Group
#

module AmplifyEnv
    def load_env(options)
        # Load from user config
        file = File.open("amplify/.config/local-aws-info.json")
        aws_info = JSON.load(file)

        # Populate client IDs
        # Ideally, these wouldn't be hard-coded
        $amplify_id = 'cdwpqqqhufai5dlfgtww3u2iqe'
        $pool_id = 'us-east-1_lQSxB65b8'
        $s3_bucket = 'fullswingflight-amplify131439-prod'
        $amplify_env = options['amplify']['environment']['envName'] ? options['amplify']['environment']['envName'] : 'test'

        $profile_name = nil
        if aws_info[$amplify_env]['useProfile']
            $profile_name = aws_info[$amplify_env]['profileName']
            puts "Using AWS Profile #{$profile_name}"
        end

        # Set client IDs based on environment
        if !options['amplify']['environment']['envName'].nil?
            case $amplify_env
            when 'dev'
                puts 'Using Amplify Dev'
                $amplify_id = '5shdromsbbfobotwnrm7gylzia'
                $pool_id = 'us-east-1_B0NCikVLQ'
                $s3_bucket = 'fullswingflight-amplify94747-dev'
            when 'test'
                puts 'Using Amplify Test'
                $amplify_id = 'cdwpqqqhufai5dlfgtww3u2iqe'
                $pool_id = 'us-east-1_lQSxB65b8'
                $s3_bucket = 'fullswingflight-amplify101733-test'
            when 'prod'
                puts 'Using Amplify Prod'
                $amplify_id = 'nacwtkdzd5cphifqs7egbm23h4'
                $pool_id = 'us-east-1_QXkqgwCpu'
                $s3_bucket = 'fullswingflight-amplify131439-prod'
            when 'live'
                puts 'Using Amplify Live'
                $amplify_id = 'xbqesaheevde3exv45bglfq3da'
                $pool_id = 'us-east-1_KWjsmZvMg'
                $s3_bucket = 'fullswingflight-amplify145220-live'
            end
        end
    end

    def switch_env(options, new_options = nil) 
        load_env(new_options) unless new_options.nil?
        yield
        load_env(options)
    end
end